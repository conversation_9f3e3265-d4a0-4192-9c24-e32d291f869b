package portfoliotrackerbuilder

import (
	"context"
	"fmt"

	networthCommon "github.com/epifi/gamma/frontend/insights/networth/common"
	portfolioTrackerUi "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/portfolio_tracker_ui"
	strategyImpl "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"
	"github.com/epifi/gamma/pkg/feature/release"

	"go.uber.org/zap"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	cxScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/inapphelp"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"

	"github.com/samber/lo"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	wbComponents "github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/dashboard"
)

type PortfolioTrackerBuilder struct {
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient
	portfolioTrackerFactory portfolioTrackerUi.IPortfolioTrackerFactory
	releaseEvaluator        release.IEvaluator
	dailyTracker            *strategyImpl.DailyTracker
	weeklyTracker           *strategyImpl.WeeklyTracker
}

func NewPortfolioTrackerBuilder(
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
	portfolioTrackerFactory portfolioTrackerUi.IPortfolioTrackerFactory,
	releaseEvaluator release.IEvaluator,
	dailyTracker *strategyImpl.DailyTracker,
	weeklyTracker *strategyImpl.WeeklyTracker,
) *PortfolioTrackerBuilder {
	return &PortfolioTrackerBuilder{
		variableGeneratorClient: variableGeneratorClient,
		portfolioTrackerFactory: portfolioTrackerFactory,
		releaseEvaluator:        releaseEvaluator,
		dailyTracker:            dailyTracker,
		weeklyTracker:           weeklyTracker,
	}
}

func (p *PortfolioTrackerBuilder) BuildPortfolioTracker(ctx context.Context, req *strategyImpl.BuildPortfolioTrackerRequest) (*strategyImpl.BuildPortfolioTrackerResponse, error) {
	var (
		scrollableComponents []*secretsFePb.PortfolioTrackerComponent
		navigationToggles    []*secretsFePb.NavigationToggle
	)

	isWeeklyPortfolioTrackerEnabled, err := p.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_WEEKLY_PORTFOLIO_TRACKER).WithActorId(req.ActorId))
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate release config for %v: %w", types.Feature_FEATURE_WEEKLY_PORTFOLIO_TRACKER, err)
	}

	weeklyTrackerEnabled := wbComponents.IsWeeklyTrackerEnabled(isWeeklyPortfolioTrackerEnabled)

	// Select strategy based on weeklyTrackerEnabled flag
	var strategy strategyImpl.PortfolioTrackerStrategy
	strategy = p.dailyTracker
	if weeklyTrackerEnabled {
		logger.Info(ctx, "Weekly portfolio tracker is enabled, using weekly tracker strategy")
		strategy = p.weeklyTracker
	}

	analysisVariableMap, err := p.getAnalysisVariable(ctx, req, strategy)
	if err != nil {
		return nil, fmt.Errorf("failed to get analysis variable map for portfolio tracker: %w", err)
	}

	for _, componentType := range strategy.GetComponentTypes() {
		portfolioTrackerUiRes, portfolioUiErr := p.portfolioTrackerFactory.BuildPortfolioTrackerComponent(ctx, &strategyImpl.BuildPortfolioTrackerComponentRequest{
			ActorId:             req.ActorId,
			ComponentType:       componentType,
			AnalysisVariableMap: analysisVariableMap,
			TrackingStrategy:    strategy,
		})
		if portfolioUiErr != nil {
			return nil, fmt.Errorf("failed to build portfolio tracker ui for component %s: %w", componentType, portfolioUiErr)
		}
		if portfolioTrackerUiRes.PortfolioTrackerComponent != nil {
			scrollableComponents = append(scrollableComponents, portfolioTrackerUiRes.PortfolioTrackerComponent)
		}
		if portfolioTrackerUiRes.NavigationToggle != nil {
			navigationToggles = append(navigationToggles, portfolioTrackerUiRes.NavigationToggle)
		}
	}

	fixedComponents := p.getPortfolioTrackerFixedComponent(navigationToggles, strategy)

	return &strategyImpl.BuildPortfolioTrackerResponse{
		FixedComponents:      fixedComponents,
		ScrollableComponents: scrollableComponents,
		FooterComponents:     p.getPortfolioTrackerFooter(),
	}, nil
}

func (p *PortfolioTrackerBuilder) getAnalysisVariable(ctx context.Context, req *strategyImpl.BuildPortfolioTrackerRequest, strategy strategyImpl.PortfolioTrackerStrategy) (map[analyserVariablePb.AnalysisVariableName]*analyserVariablePb.AnalysisVariable, error) {
	analysisVariables := make([]analyserVariablePb.AnalysisVariableName, 0)
	for _, componentType := range strategy.GetComponentTypes() {
		analysisVariables = append(analysisVariables, strategy.GetComponentToVariableMap()[componentType]...)
	}
	uniqAnalysisVariable := lo.Uniq(analysisVariables)

	getAnalysisVariableResp, err := p.variableGeneratorClient.GetAnalysisVariables(ctx, &analyserVariablePb.GetAnalysisVariablesRequest{
		ActorId:               req.ActorId,
		AnalysisVariableNames: uniqAnalysisVariable,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get analysis variable read only map for portfolio tracker %w", err)
	}
	return getAnalysisVariableResp.GetVariableEnumMap(), nil
}

func (p *PortfolioTrackerBuilder) getPortfolioTrackerFixedComponent(navigationToggles []*secretsFePb.NavigationToggle, strategy strategyImpl.PortfolioTrackerStrategy) []*secretsFePb.PortfolioTrackerComponent {
	var fixedComponents []*secretsFePb.PortfolioTrackerComponent

	// getting the title component for the portfolio tracker
	titleComponent := p.getPortfolioTrackerTitleComponent(strategy.GetTitleComponentConfig())

	// append the title component and navigation toggles to the fixed components, we are appending navigation toggles to fixed components to
	// ensure that navigation toggles are only included for those components whose scrollable components were successfully built.
	// This avoids discrepancies between scrollable components and navigation toggles. If we have a scrollable component, then we will have a navigation component, otherwise not.
	fixedComponents = append(fixedComponents, titleComponent, &secretsFePb.PortfolioTrackerComponent{
		Component: &secretsFePb.PortfolioTrackerComponent_NavigationToggles{
			NavigationToggles: &secretsFePb.NavigationToggleList{
				NavigationToggles: navigationToggles,
			},
		},
	})
	return fixedComponents
}

func (p *PortfolioTrackerBuilder) getPortfolioTrackerTitleComponent(titleConfig *strategyImpl.TitleComponentConfig) *secretsFePb.PortfolioTrackerComponent {
	return &secretsFePb.PortfolioTrackerComponent{
		Component: &secretsFePb.PortfolioTrackerComponent_TitleComponent{
			TitleComponent: secretsFePb.NewPortfolioTrackerTitleComponentBuilder().
				SetTitle(titleConfig.Title, titleConfig.TitleColor).
				SetSubtitle(titleConfig.Subtitle, titleConfig.SubtitleColor).
				SetTAndCComponent(titleConfig.Disclaimer).
				AddCta(uiPb.NewITC().
					WithTexts(commontypes.GetTextFromStringFontColourFontStyle(titleConfig.DisplayDate, "#F6F9FD", commontypes.FontStyle_SUBTITLE_S)).
					WithContainerPadding(8, 12, 8, 12).
					WithBorder(networthCommon.WealthBuilderComponentsBorderColor, 1).
					WithContainerCornerRadius(40)).
				SetBackgroundImage(commontypes.GetVisualElementImageFromUrl(titleConfig.VisualElementUrl)).
				Build(),
		},
	}
}

func (p *PortfolioTrackerBuilder) getPortfolioTrackerFooter() []*uiPb.IconTextComponent {
	footers := make([]*uiPb.IconTextComponent, 0)
	screenOptionsMarshalled, err := anyPb.New(&cxScreenTypes.FeedbackEngineScreenOptions{
		FlowIdentifierDetails: &cxScreenTypes.FlowIdentifierDetails{
			FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA,
			FlowIdentifier:     types.FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_PORTFOLIO_TRACKER.String(),
		},
	})

	if err != nil {
		logger.Error(context.Background(), "error in marshalling screen options for feedback engine screen", zap.Error(err))
	} else {
		feedbackDeeplink := &deeplink.Deeplink{
			Screen:          deeplink.Screen_FEEDBACK_ENGINE_SCREEN,
			ScreenOptionsV2: screenOptionsMarshalled,
		}

		feedbackITC := uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Facing a problem? Share it with us.", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_XS)).
			WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/quick-link-icons/reminders_message.png", 16, 16)).WithRightImagePadding(4).
			WithDeeplink(feedbackDeeplink)

		footers = append(footers, feedbackITC)
	}

	return footers
}
