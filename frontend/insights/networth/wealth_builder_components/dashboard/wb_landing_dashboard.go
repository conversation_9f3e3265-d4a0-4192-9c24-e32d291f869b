//go:generate mockgen -source=wb_landing_dashboard.go -destination=./mocks/mock_wb_landing_dashboard.go package=mocks
package dashboard

import (
	"context"
	"fmt"

	"time"

	"github.com/epifi/be-common/pkg/datetime"
	strategyImpl "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	"github.com/epifi/gamma/frontend/config/genconf"

	segmentPb "github.com/epifi/gamma/api/segment"

	typesPb "github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/gamma/pkg/feature/release"

	networthDeeplink "github.com/epifi/gamma/frontend/insights/networth/deeplink"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	feNetworthUi "github.com/epifi/gamma/api/frontend/insights/networth/ui"
	beNetworthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/insights/networth/enums"
	feWealthAnalyserPb "github.com/epifi/gamma/api/insights/secrets/frontend"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	netWorthScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/common"
	networthConfig "github.com/epifi/gamma/frontend/insights/networth/config"
	"github.com/epifi/gamma/frontend/insights/networth/data_fetcher"
	"github.com/epifi/gamma/frontend/insights/networth/generator/section"
	secretColors "github.com/epifi/gamma/frontend/insights/secrets/colors"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type wbSectionDisplayState struct {
	isZeroState                       bool
	isDailyPortfolioTrackerAvailable  bool
	isWeeklyPortfolioTrackerAvailable bool
}

var (
	WealthBuilderLandingWireSet = wire.NewSet(NewWealthBuilderLanding, wire.Bind(new(IWealthBuilderLanding), new(*WealthBuilderLanding)))
)

const (
	dashboardType = enums.NetWorthDashBoardType_NET_WORTH
)

type WealthBuilderLanding struct {
	networthConfig          *networthConfig.Config
	networthClient          beNetworthPb.NetWorthClient
	dataFetcher             data_fetcher.NetWorthDataFetcher
	sectionGenerator        section.IGenerator
	deeplinkBuilder         deeplink_builder.IDeeplinkBuilder
	releaseEvaluator        release.IEvaluator
	conf                    *genconf.Config
	segmentSrvClient        segmentPb.SegmentationServiceClient
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient
	dailyTracker            *strategyImpl.DailyTracker
	weeklyTracker           *strategyImpl.WeeklyTracker
}

type IWealthBuilderLanding interface {
	GetWealthBuilderLandingDashboard(ctx context.Context, actorId string) (*feNetworthUi.WealthBuilderLandingComponent, error)
}

func NewWealthBuilderLanding(
	networthConfig *networthConfig.Config,
	networthClient beNetworthPb.NetWorthClient,
	dataFetcher data_fetcher.NetWorthDataFetcher,
	sectionGenerator section.IGenerator,
	deeplinkBuilder deeplink_builder.IDeeplinkBuilder,
	releaseEvaluator release.IEvaluator,
	conf *genconf.Config,
	segmentSrvClient segmentPb.SegmentationServiceClient,
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
	dailyTracker *strategyImpl.DailyTracker,
	weeklyTracker *strategyImpl.WeeklyTracker,
) *WealthBuilderLanding {
	return &WealthBuilderLanding{
		networthConfig:          networthConfig,
		networthClient:          networthClient,
		dataFetcher:             dataFetcher,
		sectionGenerator:        sectionGenerator,
		deeplinkBuilder:         deeplinkBuilder,
		releaseEvaluator:        releaseEvaluator,
		conf:                    conf,
		segmentSrvClient:        segmentSrvClient,
		variableGeneratorClient: variableGeneratorClient,
		dailyTracker:            dailyTracker,
		weeklyTracker:           weeklyTracker,
	}
}

// GetWealthBuilderLandingDashboard Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-45899&t=R5KI7488DNfw2YTx-0
func (w *WealthBuilderLanding) GetWealthBuilderLandingDashboard(ctx context.Context, actorId string) (*feNetworthUi.WealthBuilderLandingComponent, error) {
	networthDashboardConfig, err := w.networthConfig.GetNetworthDashboardConfig()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get networth dashboard config")
	}
	categoriesDataMap, err := w.dataFetcher.GetCategoriesValue(ctx, actorId, dashboardType)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get categories value")
	}
	categoriesStatusMap, err := w.dataFetcher.GetCategoriesStatus(ctx, actorId, categoriesDataMap, dashboardType)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get categories status")
	}
	refreshDetailsRes, err := w.networthClient.GetNetWorthInstrumentsRefreshDetails(ctx, &beNetworthPb.GetNetWorthInstrumentsRefreshDetailsRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(refreshDetailsRes, err); grpcErr != nil {
		logger.Error(ctx, "failed to get net worth instrument refresh details", zap.Error(err))
	}
	refreshDetailsMap := w.dataFetcher.ConvertRefreshDetailsToMap(ctx, refreshDetailsRes.GetInstrumentRefreshSummary())

	wbDashboardLiabilitiesEnabled, wbDashboardLiabilitiesEnabledErr := w.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_WB_DASHBOARD_LIABILITIES).WithActorId(actorId))
	if wbDashboardLiabilitiesEnabledErr != nil {
		logger.Error(ctx, "failed to evaluate wb dashboard liabilities enabled")
	}

	var wealthBuilderLandingSections []*feNetworthUi.WealthBuilderLandingSection
	for _, sectionConfig := range networthDashboardConfig.GetSections() {
		if sectionConfig.GetSectionType() == networthFePb.NetworthSectionType_NETWORTH_SECTION_TYPE_ASSETS ||
			(sectionConfig.GetSectionType() == networthFePb.NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES && wbDashboardLiabilitiesEnabled) {
			generatedSection, sectionErr := w.sectionGenerator.GenerateWealthBuilderLandingSection(ctx, actorId, sectionConfig, categoriesDataMap, categoriesStatusMap, refreshDetailsMap)
			if sectionErr != nil {
				return nil, fmt.Errorf("failed to generate net worth section %s: %w", sectionConfig.GetSectionType(), sectionErr)
			}
			wealthBuilderLandingSections = append(wealthBuilderLandingSections, generatedSection)
		}
	}

	// header of wealth builder section
	wealthBuilderDashboardHeader, wbSectionDisplayStates, err := w.getWealthBuilderSectionHeader(ctx, actorId, networthDashboardConfig, categoriesDataMap, refreshDetailsRes.GetInstrumentRefreshSummary())
	if err != nil {
		return nil, fmt.Errorf("failed to get wealth builder section header: %w", err)
	}

	collapsibleDetails := w.getDashboardCollapsibleDetails(wealthBuilderLandingSections)

	// connect more cta will be shown more than 5 assets are connected
	// if less than 5 assets are connected, then connect more cta will not be shown
	// instead, add more deeplink will be present on 5th widget of first section (To be implemented P1)
	// as of now, connect more cta will be shown for all cases (changes this logic later)
	connectMore := w.getConnectMoreDetails(networthDashboardConfig, categoriesDataMap)

	wbLandingDashboard := &feNetworthUi.WealthBuilderLandingDashboard{
		DashboardHeader:              wealthBuilderDashboardHeader,
		WealthBuilderLandingSections: wealthBuilderLandingSections,
		CollapsibleDetails:           collapsibleDetails,
		ActionCta:                    []*ui.IconTextComponent{connectMore},
		BgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#18191B"},
		},
		CornerRadius: 16,
	}
	magicImportEnabled := w.isFeatureEnabled(ctx, typesPb.Feature_FEATURE_WB_MAGIC_IMPORT, actorId)
	if magicImportEnabled {
		wbLandingDashboard.ActionCta = append(wbLandingDashboard.ActionCta, ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.MagicImport, colors.ColorForest, commontypes.FontStyle_SUBTITLE_S)).
			WithBorder(colors.ColorOnDarkDisabled700, 1).WithContainerBackgroundColor("#141517").WithContainerCornerRadius(32).WithContainerPadding(10, 20, 10, 12).
			WithDeeplink(&deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_NETWORTH_MAGIC_IMPORT_SCREEN,
			}))
	}

	footerCta := w.getPartitionFooterCta(categoriesDataMap, wbSectionDisplayStates)
	return &feNetworthUi.WealthBuilderLandingComponent{
		WealthBuilderLandingDashboard: wbLandingDashboard,
		FooterCta:                     footerCta,
		BgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"},
		},
	}, nil
}

func (w *WealthBuilderLanding) isFeatureEnabled(ctx context.Context, feature typesPb.Feature, actorId string) bool {
	featureEnabled, err := w.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(feature).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in checking if feature is enabled", zap.String(logger.FEATURE, feature.String()), zap.Error(err))
		return false
	}
	return featureEnabled
}

func (w *WealthBuilderLanding) getWealthBuilderSectionHeader(ctx context.Context, actorId string, netWorthDashboardConfig *networthFePb.NetWorthDashboardConfig, categoriesDataMap map[networthFePb.NetworthCategory]*data_fetcher.CategoryData, refreshDetails []*beNetworthPb.InstrumentRefreshDetails) (*feNetworthUi.DashboardHeader, *wbSectionDisplayState, error) {
	if len(netWorthDashboardConfig.GetSections()) == 0 {
		return nil, nil, nil
	}
	var refreshTag *ui.IconTextComponent
	for _, refreshDetail := range refreshDetails {
		if refreshDetail.GetRefreshStatus() == beNetworthPb.RefreshStatus_REFRESH_STATUS_REQUIRED {
			netWorthRefreshBottomSheetScreenDeeplink, deeplinkErr := w.deeplinkBuilder.NetWorthRefreshBottomSheetScreen(ctx, refreshDetails, actorId, dashboardType)
			if deeplinkErr != nil {
				return nil, nil, fmt.Errorf("failed to get net worth refresh bottom sheet screen deeplink: %w", deeplinkErr)
			}
			refreshTag = ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.Refresh, "#E795AE", commontypes.FontStyle_SUBTITLE_XS)).
				WithBorder(common.WealthBuilderComponentsBorderColor, 1).WithContainerBackgroundColor("#141517").WithContainerPaddingSymmetrical(8, 6).
				WithLeftImageUrlHeightAndWidth(common.RefreshIcon, 20, 20).WithLeftImagePadding(2).WithContainerCornerRadius(32).
				WithDeeplink(netWorthRefreshBottomSheetScreenDeeplink)
			break
		}
	}
	var (
		percentageTag *ui.IconTextComponent
		err           error
	)
	isDailyPortfolioTrackerEnabled, evalErr := w.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_PORTFOLIO_TRACKER_LANDING_SCREEN).WithActorId(actorId))
	if evalErr != nil {
		return nil, nil, fmt.Errorf("error in evaluating portfolio tracker landing screen release flag: %w", evalErr)
	}

	isWeeklyPortfolioTrackerEnabled, weekEvalErr := w.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_WEEKLY_PORTFOLIO_TRACKER).WithActorId(actorId))
	if weekEvalErr != nil {
		return nil, nil, fmt.Errorf("error in evaluating weekly portfolio tracker landing screen release flag: %w", weekEvalErr)
	}

	weeklyTrackerEnabled := IsWeeklyTrackerEnabled(isWeeklyPortfolioTrackerEnabled)

	if isDailyPortfolioTrackerEnabled {
		percentageTag, err = w.getPercentageTag(ctx, actorId, weeklyTrackerEnabled)
		if err != nil {
			// not returning error in this case to show bare minimal dashboard in case of failure in building daily change page
			logger.Error(ctx, "failed to get daily percentage tag", zap.Error(err))
		}
	}

	var (
		tags                []*ui.IconTextComponent
		sectionDisplayState *wbSectionDisplayState
	)

	if percentageTag != nil {
		tags = append(tags, percentageTag)
		switch {
		case weeklyTrackerEnabled:
			sectionDisplayState = &wbSectionDisplayState{
				isWeeklyPortfolioTrackerAvailable: true,
			}
		default:
			sectionDisplayState = &wbSectionDisplayState{
				isDailyPortfolioTrackerAvailable: true,
			}
		}
	}
	if refreshTag != nil {
		tags = append(tags, refreshTag)
	}

	assetSectionConfig := netWorthDashboardConfig.GetSections()[0]
	for _, widgetConfig := range assetSectionConfig.GetWidgets() {
		category := widgetConfig.GetCategory()
		if categoryData, exists := categoriesDataMap[category]; exists {
			if categoryData.ComputationStatus == beNetworthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS {
				return &feNetworthUi.DashboardHeader{
					DashboardHeader: &feNetworthUi.DashboardHeader_SectionHeaderConnectedState{
						SectionHeaderConnectedState: &feNetworthUi.DashboardHeaderConnectedState{
							Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.YourWealth, "#A8E0D3", commontypes.FontStyle_HEADLINE_M).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)),
							WealthDisplayDetails: &feNetworthUi.WealthDisplayDetails{
								CurrencySymbol:    commontypes.GetTextFromStringFontColourFontStyle("₹", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_CURRENCY_XL),
								TotalDisplayValue: commontypes.GetTextFromStringFontColourFontStyle("0", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_NUMBER_3XL),
							},
							Tags: tags,
							VisibilityDetails: &feNetworthUi.VisibilityDetails{
								Hide: commontypes.GetVisualElementFromUrlHeightAndWidth(common.InfoHide, 28, 20),
								Show: commontypes.GetVisualElementFromUrlHeightAndWidth(common.InfoShow, 28, 20),
							},
						},
					},
				}, sectionDisplayState, nil
			}
		}
	}

	return &feNetworthUi.DashboardHeader{
		DashboardHeader: &feNetworthUi.DashboardHeader_SectionHeaderZeroState{
			SectionHeaderZeroState: &feNetworthUi.DashboardHeaderZeroState{
				Title: commontypes.GetTextFromStringFontColourFontStyle(common.ZeroStateTitle, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_XL).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
				// TODO(amrit): show this ITC once we have implemented the video from design
				// ActionCta: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.HowItWorksTitle, colors.ColorForest, commontypes.FontStyle_HEADLINE_XS)).
				//	WithLeftImageUrlHeightAndWidth(common.PlayIcon, 20, 20).WithLeftImagePadding(4).
				//	WithBorder(colors.ColorOnDarkDisabled700, 1).WithContainerBackgroundColor("#141517").WithContainerCornerRadius(16).WithContainerPaddingSymmetrical(8, 8).
				//	WithDeeplink(nil),
			},
		},
	}, &wbSectionDisplayState{isZeroState: true}, nil
}

func (w *WealthBuilderLanding) getConnectMoreDetails(netWorthDashboardConfig *networthFePb.NetWorthDashboardConfig, categoriesDataMap map[networthFePb.NetworthCategory]*data_fetcher.CategoryData) *ui.IconTextComponent {
	var notConnectedAssets, notConnectedLiabilities []string
	for _, sectionConfig := range netWorthDashboardConfig.GetSections() {
		for _, widgetConfig := range sectionConfig.GetWidgets() {
			categoryData, found := categoriesDataMap[widgetConfig.GetCategory()]
			if !found {
				continue
			}
			if assetType, exists := common.CategoryToAssetTypeMap[widgetConfig.GetCategory()]; exists {
				if categoryData.ComputationStatus == beNetworthPb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND {
					notConnectedAssets = append(notConnectedAssets, assetType.String())
				}
				continue
			}
			if liabilityType, exists := common.CategoryToLiabilityTypeMap[widgetConfig.GetCategory()]; exists {
				if categoryData.ComputationStatus == beNetworthPb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND {
					notConnectedLiabilities = append(notConnectedLiabilities, liabilityType.String())
				}
				continue
			}
		}
	}
	var connectMoreDeeplink *deeplinkPb.Deeplink
	if len(notConnectedAssets) != 0 || len(notConnectedLiabilities) != 0 {
		connectMoreDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEALTH_BUILDER_LANDING_CONNECT_MORE_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&netWorthScreenOptions.ConnectMoreAssetsScreenOptions{
				ConnectMoreAssetsScreenRequestParams: &networthFePb.ConnectMoreAssetsScreenRequestParams{
					AssetTypes:     notConnectedAssets,
					LiabilityTypes: notConnectedLiabilities,
				},
			}),
		}
	}
	connectMore := ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.AddMore, colors.ColorForest, commontypes.FontStyle_SUBTITLE_S)).
		WithLeftImageUrlHeightAndWidth(common.GreenPlusIcon, 20, 20).WithLeftImagePadding(4).
		WithBorder(colors.ColorOnDarkDisabled700, 1).WithContainerBackgroundColor("#141517").WithContainerCornerRadius(32).WithContainerPadding(10, 20, 10, 12).
		WithDeeplink(connectMoreDeeplink)
	return connectMore
}

func (w *WealthBuilderLanding) getDashboardCollapsibleDetails(wealthBuilderLandingSections []*feNetworthUi.WealthBuilderLandingSection) *feNetworthUi.CollapsibleDetails {
	// show collapsible details only if there are more than 5 asset widgets are connected or one of liabilities widgets are connected
	if (len(wealthBuilderLandingSections) > 0 && len(wealthBuilderLandingSections[0].GetWidgets()) > 5) ||
		(len(wealthBuilderLandingSections) > 1 && len(wealthBuilderLandingSections[1].GetWidgets()) > 0) {
		return &feNetworthUi.CollapsibleDetails{
			ShowMoreCta: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.ExpandText, colors.ColorForest, commontypes.FontStyle_SUBTITLE_S)).
				WithRightImageUrlHeightAndWidth(common.DownChevron, 20, 20).WithRightImagePadding(4).
				WithBorder(colors.ColorOnDarkDisabled700, 1).WithContainerBackgroundColor("#141517").WithContainerCornerRadius(32).WithContainerPadding(10, 20, 10, 12),
			ShowLessCta: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.CollapseText, colors.ColorForest, commontypes.FontStyle_SUBTITLE_S)).
				WithRightImageUrlHeightAndWidth(common.UpChevron, 20, 20).WithRightImagePadding(4).
				WithBorder(colors.ColorOnDarkDisabled700, 1).WithContainerBackgroundColor("#141517").WithContainerCornerRadius(32).WithContainerPadding(10, 20, 10, 12),
		}
	}
	return nil
}

//nolint:unparam
func (w *WealthBuilderLanding) getPartitionFooterCta(categoriesDataMap map[networthFePb.NetworthCategory]*data_fetcher.CategoryData, wbSectionDisplayState *wbSectionDisplayState) *ui.IconTextComponent {
	if wbSectionDisplayState == nil {
		return nil
	}
	// Get mutual fund data before switch statement
	mfData, found := categoriesDataMap[networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_MUTUAL_FUNDS]
	hasMutualFunds := found && mfData.ComputationStatus == beNetworthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS

	// Base ITC builder with common configurations in all the cases
	baseITC := ui.NewITC().WithContainerBackgroundColor(colors.ColorForest).WithContainerCornerRadius(40).
		WithRightImageUrlHeightAndWidth(common.WhiteChevron, 24, 24).WithRightImagePadding(12).
		WithContainerPaddingSymmetrical(16, 12)

	switch {
	case wbSectionDisplayState.isWeeklyPortfolioTrackerAvailable:
		return baseITC.WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.WeeklyReportText, colors.ColorSnow, commontypes.FontStyle_SUBTITLE_M)).
			WithLeftVisualElementUrlHeightAndWidth(common.NewTextIcon, 20, 40).WithLeftImagePadding(12).
			WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PORTFOLIO_TRACKER_LANDING_SCREEN})
	case wbSectionDisplayState.isDailyPortfolioTrackerAvailable:
		return baseITC.WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.DailyReportText, colors.ColorSnow, commontypes.FontStyle_SUBTITLE_M)).
			WithLeftVisualElementUrlHeightAndWidth(common.NewTextIcon, 20, 40).WithLeftImagePadding(12).
			WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PORTFOLIO_TRACKER_LANDING_SCREEN})
	case hasMutualFunds:
		return baseITC.WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.MfSummaryText, colors.ColorSnow, commontypes.FontStyle_SUBTITLE_M)).
			WithDeeplink(deeplink_builder.WealthAnalyserReportDeeplink(feWealthAnalyserPb.WealthAnalyserReportType_WEALTH_ANALYSER_REPORT_TYPE_MUTUAL_FUND))
	case wbSectionDisplayState.isZeroState:
		return baseITC.WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.TrackBankBalancesText, colors.ColorSnow, commontypes.FontStyle_SUBTITLE_M)).
			WithDeeplink(networthDeeplink.ConnectedAccountDashboardDeeplink())
	default:
		return nil
	}
}

func (w *WealthBuilderLanding) getPercentageTag(ctx context.Context, actorId string, weeklyTrackerEnabled bool) (*ui.IconTextComponent, error) {
	variablesResp, getVariablesErr := w.variableGeneratorClient.GetAnalysisVariables(ctx, &analyserVariablePb.GetAnalysisVariablesRequest{
		ActorId: actorId,
		AnalysisVariableNames: []analyserVariablePb.AnalysisVariableName{analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS,
			analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION, analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION},
	})
	if getVariablesErr != nil {
		return nil, fmt.Errorf("failed to get analysis analyserVariablePb: %v", getVariablesErr)
	}

	var strategy strategyImpl.PortfolioTrackerStrategy
	strategy = w.dailyTracker
	displayText := common.OneDayText
	if weeklyTrackerEnabled {
		strategy = w.weeklyTracker
		displayText = common.OneWeekText
	}

	changeParams, err := strategy.GetChangeParams(ctx, actorId, variablesResp.GetVariableEnumMap())
	if err != nil {
		return nil, fmt.Errorf("failed to get change params for daily tracker: %w", err)
	}
	percentageChange := float64(0)
	if changeParams.TotalPreviousValue != 0 {
		percentageChange = (changeParams.TotalChange / changeParams.TotalPreviousValue) * 100
	}
	if percentageChange == 0 {
		logger.Info(ctx, "percentage change is 0, not showing daily percentage tag")
		return nil, nil
	}

	percentageSign := ""
	changePercentageColor := secretColors.ColorSupportingAmber200
	if percentageChange >= 0 {
		changePercentageColor = colors.SupportingMoss200
		percentageSign = "+"
	}
	percentageChangeText := fmt.Sprintf("%s%.2f%%", percentageSign, percentageChange)

	return ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(displayText+" ", "#929599", commontypes.FontStyle_SUBTITLE_XS),
		commontypes.GetTextFromStringFontColourFontStyle(percentageChangeText, changePercentageColor, commontypes.FontStyle_SUBTITLE_XS)).
		WithBorder(common.WealthBuilderComponentsBorderColor, 1).WithContainerPaddingSymmetrical(8, 6).WithContainerCornerRadius(32).
		WithRightImageUrlHeightAndWidth(common.WhiteChevron, 20, 20).
		WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PORTFOLIO_TRACKER_LANDING_SCREEN}), nil
}

// IsWeeklyTrackerEnabled checks if the weekly tracker is enabled based on the current day of the week.
func IsWeeklyTrackerEnabled(isFeatureEnabled bool) bool {
	now := time.Now().In(datetime.IST)

	if now.Weekday() == time.Sunday || now.Weekday() == time.Monday {
		return isFeatureEnabled
	}
	return false
}
