// nolint: gosec
package internal

// For colors
const (
	FontColorGrayLead              = "#646464"
	FontColorPastelLemon           = "#F4E7BF"
	FontColorTertiaryLeon          = "#AC7C44"
	TxnTimeLayout                  = "2006-01-02"
	BillInfoDashboardRed           = "#EFC0C0"
	BillInfoDashboardGreen         = "#C5E9B2"
	BillInfoDashboardYellow        = "#EAD8A3"
	BillInfoDashboardWhite         = "#FFFFFF"
	BillInfoDashboardLightRed      = "#FAD0D0"
	BillInfoDashboardLightGreen    = "#D9F2CC"
	BillInfoDashboardLightYellow   = "#F4E7BF"
	BillInfoDashboardDarkYellow    = "#CDA428"
	BillInfoDashboardDarkRed       = "#9E5A57"
	DarkBlack                      = "#18191B"
	GreyBlack                      = "#28292B"
	JadeGreen                      = "#DCF3EE"
	BillDashboardFontColorDarkGrey = "#525355"
	BillDashboardFontColorBlack    = "#333333"
	BlackColor                     = "#333333"
	GreyedOutColor                 = "#CED2D6"
	TertiaryOceanColor             = "#478295"
	MidOceanColor                  = "#C0DAE0"
	White                          = "#FFFFFF"
	Ivory                          = "#F7F9FA"
	ForestGreen                    = "#00B899"
	CharcoalBlack                  = "#38393B"
	DarkGray                       = "#6A6D70"
	Amber                          = "#C0723D"
	SlateGray                      = "#8D8D8D"
	DarkLayerGray                  = "#313234"
	JadeLightGreen                 = "#A8E0D3"
	JadeSemiDarkGreen              = "#6BCDB6"
	SmokeGray                      = "#E7E7E7"
	SemiDarkGray                   = "#929599"
)

// For Icon Urls
const (
	VisaFederalPartnerIconUrl                   = "https://epifi-icons.pointz.in/credit_card_images/visa-federal-partner-icon.png"
	UfoIconUrl                                  = "https://epifi-icons.pointz.in/credit_card_images/ufo_ladder_image.png"
	BestInMarketInterestIconUrl                 = "https://epifi-icons.pointz.in/credit_card_images/best_in_market_interest_tag_4x.png"
	InfoIconUrl                                 = "https://epifi-icons.pointz.in/credit_card_images/information.png"
	FiCoinSuccessIcon                           = "https://epifi-icons.pointz.in/credit_card_images/coins_success.png"
	FiCoinInProgressIcon                        = "https://epifi-icons.pointz.in/credit_card_images/coins_pending.png"
	FiBadgeUrl                                  = "https://epifi-icons.pointz.in/credit_card_images/Fi_Badge_Icon_4x.png"
	FederalFiBadgeUrl                           = "https://epifi-icons.pointz.in/card-images/federal_fi_icon.png"
	PartnerLogoUrl                              = "https://epifi-icons.pointz.in/emi/bank_info_image.png"
	BulbUrl                                     = "https://epifi-icons.pointz.in/credit_card_images/Bulb_4x.png"
	CalendarUrl                                 = "https://epifi-icons.pointz.in/credit_card_images/Autopay_Calendar_2_4x.png"
	WarningTriangleUrl                          = "https://epifi-icons.pointz.in/credit_card_images/Warning_Triangle_Yellow_4x.png"
	UpiFederalLogoUrl                           = "https://epifi-icons.pointz.in/credit_card_images/Upi_Federal_Icon_4x.png"
	FailureTxnIconUrl                           = "https://epifi-icons.pointz.in/credit_card_images/failed.png"
	DebitTxnIconUrl                             = "https://epifi-icons.pointz.in/credit_card_images/debit.png"
	CreditTxnIconUrl                            = "https://epifi-icons.pointz.in/credit_card_images/credit.png"
	EmiDashboardIcon                            = "https://epifi-icons.pointz.in/credit_card_images/emiControl.png"
	FiRewardsIconUrl                            = "https://epifi-icons.pointz.in/credit_card_images/Fi_Rewards_4x.png"
	StatementDashboardIcon                      = "https://epifi-icons.pointz.in/credit_card_images/Statement_Dashboard_Icon_4x.png"
	LargeExclamationMarkIcon                    = "https://epifi-icons.pointz.in/credit_card_images/large-exclamation-mark.png"
	FiProcessingRewardsIconUrl                  = "https://epifi-icons.pointz.in/credit_card_images/fi_coin_reward_processing.png"
	RightArrowIconUrl                           = "https://epifi-icons.pointz.in/credit_card_images/Right_Arrow_4X.png"
	BillingCycleCalendarUrl                     = "https://epifi-icons.pointz.in/credit_card_images/Billing_Credit_Card_Calendar_4x.png"
	LoopIconUrl                                 = "https://epifi-icons.pointz.in/credit_card_images/loop_icon_padding_4x.png"
	TickIconUrl                                 = "https://epifi-icons.pointz.in/credit_card_images/Tick_4x.png"
	UpgradeRewardToFiveXIconUrl                 = "https://epifi-icons.pointz.in/credit_card_images/Upgrade_Reward_Tier_FiveX_4x.png"
	ShoppingBagIconUrl                          = "https://epifi-icons.pointz.in/credit_card_images/placeholder_shopping_merchant_4x.png"
	ShoppingBagIconDashboardUrl                 = "https://epifi-icons.pointz.in/credit_card_images/Shopping_Bag_4x.png"
	FiDashboardRewardsIcon                      = "https://epifi-icons.pointz.in/credit_card_images/Fi_Rewards_Dashboard_Icon.png"
	FiFixedDepositIconUrl                       = "https://epifi-icons.pointz.in/credit_card_images/fixed_deposit_4x.png"
	BellReminderDashboardIconUrl                = "https://epifi-icons.pointz.in/credit_card_images/bell_reminder.png"
	MultiplierRewardsIcon5x                     = "https://epifi-icons.pointz.in/credit_card_images/unsecured_v2_cvp_accelarated_4x.png"
	MultiplierRewardsIcon2x                     = "https://epifi-icons.pointz.in/credit_card_images/2x_reward_img.png"
	MultiplierRewardsIcon1x                     = "https://epifi-icons.pointz.in/credit_card_images/unsecured_v2_cvp_base_4x.png"
	VioletSpecsIconUrl                          = "https://epifi-icons.pointz.in/credit_card_images/violet_specs_4x.png"
	BlueNoteIconUrl                             = "https://epifi-icons.pointz.in/credit_card_images/blue_note.png"
	HourGlassIconUrl                            = "https://epifi-icons.pointz.in/credit_card_images/hour_glass_4x.png"
	RedTickIconUrl                              = "https://epifi-icons.pointz.in/credit_card_images/red_tick_4x.png"
	WarningRedTriangleIconUrl                   = "https://epifi-icons.pointz.in/credit_card_images/warning_triangle_red_4x.png"
	GreenNoteIconUrl                            = "https://epifi-icons.pointz.in/credit_card_images/green_note_4x.png"
	CreditCardIconUrl                           = "https://epifi-icons.pointz.in/credit_card_images/credit_card_icon.png"
	UpdateAppDisplayImage                       = "https://epifi-icons.pointz.in/credit_card_images/ufo_ladder_image.png"
	EditPencilIcon                              = "https://epifi-icons.pointz.in/credit_card_images/edit_pencil_gray_4x.png"
	EmiNoEligibleTransactions                   = "https://epifi-icons.pointz.in/credit_card_images/emi_no_eligible_transactions.png"
	UnsecuredBaseRewardsDashboardIconUrl        = "https://epifi-icons.pointz.in/credit_card_images/dashboard_rewards_unsecured_base_rewards_4x.png"
	UnsecuredAcceleratedRewardsDashboardIconUrl = "https://epifi-icons.pointz.in/credit_card_images/dashboard_rewards_unsecured_accelarated_rewards_4x.png"
	MassUnsecuredBaseRewardsIconUrl             = "https://epifi-icons.pointz.in/credit_card_images/massunsecured_base_rewards_4x.png"
	MassUnsecuredAcceleratedRewardsIconUrl      = "https://epifi-icons.pointz.in/credit_card_images/massunsecured_accelerated_rewards_4x.png"
	FdScreenHeaderImageLeft                     = "https://epifi-icons.pointz.in/credit_card_images/fd_screen_header_left_image_4x.png"
	GreenInfoIcon                               = "https://epifi-icons.pointz.in/credit_card_images/green_i_button.png"
	FdScreenTopSectionIconUrl                   = "https://epifi-icons.pointz.in/credit_card_images/fd_screen_top_section_visual_element_4x.png"
	GreenPlusIconUrl                            = "https://epifi-icons.pointz.in/credit_card_images/green_plus_icon_4x.png"
	BoosterAppliedCheckmark                     = "https://epifi-icons.pointz.in/credit_card_images/booster_applied_checkmark"
	WhyOpenAFdStoryUrl                          = "https://stories.fi.money/why-open-an-fd-for-simplifi"

	DashboardV2CardImageUrl             = "https://epifi-icons.pointz.in/credit_card_v2/mangnifi-card-4x-img.png"
	DashboardV2SmallCardWhiteIconUrl    = "https://epifi-icons.pointz.in/credit_card/v2/small-card-white-4x.png"
	DashboardV2ChevronRightWhiteIconUrl = "https://epifi-icons.pointz.in/rewards/chevron-right-white.png"
	DashboardV2CardControlsIconUrl      = "https://epifi-icons.pointz.in/credit_card/v2/card-controls-icon-4x.png"
	DashboardV2CollectedOffersIconUrl   = "https://epifi-icons.pointz.in/credit_card/v2/collected-offers-icon-4x.png"
	DashboardV2PercentageIconUrl        = "https://epifi-icons.pointz.in/credit_card/v2/percentage-black-4x.png"
	DashboardV2SmallCardBlackIconUrl    = "https://epifi-icons.pointz.in/credit_card/v2/small-card-black-4x.png"
)

// For texts
const (
	FixedDepositWorthText                             = "By creating a Fixed Deposit worth"
	QRCodeVerificationFailureTitle                    = "Card activation failed!"
	QRCodeVerificationVendorFailureDescription        = "Apologies, it looks like we're facing some issues with our partner. Can you try again in a few hours?"
	QRCodeVerificationCardAlreadyActivatedTitle       = "Card Already Activated!!"
	QRCodeVerificationCardAlreadyActivatedDescription = "You have already activated your card. You can view and change the way your card works from the card settings screen."
	RpcFailedRetryText                                = "Uh oh, we faced a server error while \nloading this. Please try again"
	Retry                                             = "Retry"
	InterestRateTitleHtml                             = "INTEREST<br>RATE"
	MaturityAmountTitleHtml                           = "MATURITY<br>AMOUNT"
	SelectDepositTenure                               = "Select deposit tenure"
	DepositTenureTitleHtml                            = "DEPOSIT<br>TENURE"
	RewardInfoTitle                                   = "You win 1 Fi-Coin for every ₹5 you spend on a transaction"
	AccountNumberFormat                               = "Federal Bank •••• %s"
	RemainingDue                                      = "Remaining due"
	UpdateAppTitle                                    = "Update your Fi app to continue"
	UpdateAppDescription                              = "We’ve rolled out some new fixes that are necessary to proceed."
	BillingCycleLayout                                = "%d%s %s - %d%s %s"
	FdScreenTopSectionText                            = "Get a credit limit of"
	InsufficientBalanceText                           = "Insufficient balance. Add funds to savings account"
	AddFunds                                          = "Add funds"
	AddSelectNominee                                  = "ADD / SELECT A NOMINEE"
	NomineeOptOutText                                 = "I want to continue without nominee"
	FdScreenTncText                                   = "By continuing, I agree to the Terms and Conditions of Federal’s Fixed Deposit."
	TncText                                           = "Terms and Conditions"
	KfsText                                           = "KFS"
	MitcText                                          = "MITC"
	OpenFixedDeposit                                  = "Open Fixed Deposit"
	FixedDepositDetails                               = "FIXED DEPOSIT DETAILS"
	InterestRatePa                                    = "returns p.a."
	CloseFdAnyTime                                    = "Close your card & FD anytime"
	AutoRenewFd                                       = "Auto-renews after"
	SimplifiIntroScreenConsentText                    = "I agree to Federal Bank’s MITC, KFS and Terms and Conditions"
	MagnifiIntroScreenConsentText                     = "I consent to Federal Bank receiving & processing my credit information from CIBIL and understand this may affect my credit score."
	AmplifiIntroScreenConsentText                     = "I consent to Federal Bank processing my credit information from credit bureaus for eligibility check and understand this may affect my credit score."

	// Dashboard V2 Texts
	DashboardV2ViewManageCreditCardText   = "View & Manage Credit Card"
	DashboardV2CardSettingsInfoText       = "See card settings, transactions, rewards & more"
	DashboardV2ExclusiveMagniFiOffersText = "Exclusive Magnifi offers"
	DashboardV2ViewAllText                = "VIEW ALL >"
	DashboardV2FiCoinsRewardsText         = "Turn your Fi-Coins into rewards"
	DashboardV2CardControlsTitle          = "Card controls"
	DashboardV2CardControlsDesc           = "Manage your card, set limits, etc."
	DashboardV2CollectedOffersTitle       = "My collected offers"
	DashboardV2CollectedOffersDesc        = "View your bill generation details"
	DashboardV2LoungeAccessTitle          = "Lounge Access"
	DashboardV2LoungeAccessDesc           = "Manage your monthly payments"
	DashboardV2CardDetailsTitle           = "Card details & benefits"
	DashboardV2CardDetailsDesc            = "View your benefits, charges and TnC docs"
)

// Dashboard V2 UI Constants
const (
	DashboardV2CardCornerRadius  = 20
	DashboardV2CardLeftPadding   = 16
	DashboardV2CardRightPadding  = 16
	DashboardV2CardTopPadding    = 12
	DashboardV2CardBottomPadding = 12
	DashboardV2CardBorderWidth   = 1
)

// For numeric properties
const (
	FiCoinToInrExchangeRate = 0.03
	InrToFiCoin2x           = 0.4
	InrToFiCoin5x           = 1
	WelcomeVouchersWorthInr = 5000
	Milestone1SpendBenefit  = 2500
	Milestone2SpendBenefit  = 8000
	Milestone1Spend         = 250000
	Milestone2Spend         = 400000
	Ratio2xSpend            = 0.2
	Ratio5xSpend            = 0.8
	CopyCardDetailsExpiry   = 30
	MaxNominees             = 1
)

// for T&C's
const (
	FdTncUrl         = "https://www.federalbank.co.in/epifi-tandc"
	SimplifiKfsUrl   = "https://fi.money/secured-credit-card/key-fact-statement"
	SimplifiMitcUrl  = "https://fi.money/secured-credit-card/important-tnc"
	OnboardingTncUrl = "https://fi.money/credit-card/T&Cs"
)

// Dashboard V2 Contact Section Constants
const (
	DashboardV2FederalHelpIconUrl   = "https://epifi-icons.pointz.in/credit_card/v2/federal-help-img-4x.png"
	DashboardV2NeedHelpText         = "Need help?"
	DashboardV2FederalHelpDescText  = "For any queries regarding your credit card, reach out to Federal Bank."
	DashboardV2FederalBankText      = "Federal Bank."
	DashboardV2CallIconUrl          = "https://epifi-icons.pointz.in/credit_card/v2/call-icon-4x.png"
	DashboardV2EmailIconUrl         = "https://epifi-icons.pointz.in/credit_card/v2/email-icon-4x.png"
	DashboardV2RightArrowIconUrl    = "https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"
	DashboardV2PhoneNumberText      = "***********"
	DashboardV2EmailAddressText     = "<EMAIL>"
	DashboardV2PhoneNumberUrl       = "tel://***********"
	DashboardV2EmailAddressUrl      = "mailto:<EMAIL>"
	DashboardV2DeliveryTruckIconUrl = "https://epifi-icons.pointz.in/credit_card/v2/delivery-truck-4x-img.png"
	DashboardV2CardTrackingText     = "Your credit card should be with you in 5-7 days. Track it here"

	// Contact Section UI Constants
	DashboardV2FederalIconHeight  = 52
	DashboardV2FederalIconWidth   = 54
	DashboardV2ContactIconSize    = 20
	DashboardV2ContactIconPadding = 2
	DashboardV2ArrowIconSize      = 24
)
