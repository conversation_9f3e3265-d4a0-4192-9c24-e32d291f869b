package ozonetel

import (
	"github.com/samber/lo"

	"context"
	"net/http"

	"github.com/golang/protobuf/ptypes/empty"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	vendorPb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/vendornotification/metrics"

	callIvrPb "github.com/epifi/gamma/api/cx/call_ivr"

	"github.com/epifi/gamma/pkg/obfuscator"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	ozonetelConsumerPb "github.com/epifi/gamma/api/cx/call/consumer"
	callRoutingPb "github.com/epifi/gamma/api/cx/call_routing"
	"github.com/epifi/gamma/api/vendornotification/cx/ozonetel"
	ozonetelVendor "github.com/epifi/gamma/api/vendors/ozonetel"
	redactorConf "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/redactor"
	"github.com/epifi/gamma/vendornotification/security"
)

type CallRoutingService struct {
	cxCallRoutingClient  callRoutingPb.CallRoutingClient
	conf                 *config.Config
	callDetailsPublisher queue.Publisher
	cxCallIvrClient      callIvrPb.IvrClient
}

type OzonetelCallDetailsPublisher queue.Publisher

func NewCallRoutingService(cxCallRoutingClient callRoutingPb.CallRoutingClient, conf *config.Config,
	callDetailsPublisher OzonetelCallDetailsPublisher, cxCallIvrClient callIvrPb.IvrClient) *CallRoutingService {
	return &CallRoutingService{
		cxCallRoutingClient:  cxCallRoutingClient,
		conf:                 conf,
		callDetailsPublisher: callDetailsPublisher,
		cxCallIvrClient:      cxCallIvrClient,
	}
}

var (
	_ ozonetel.CallRoutingServer = &CallRoutingService{}
)

const (
	GetRoutingChannelRequest   = "GetRoutingChannelRequest"
	ScreenPopCallbackRequest   = "ScreenPopCallbackRequest"
	PushToURLCallbackRequest   = "PushToURLCallbackRequest"
	InitiateIvrRequest         = "InitiateIvrRequest"
	ProcessIvrUserInputRequest = "ProcessIvrUserInputRequest"
	GetIvrStateRequest         = "GetIvrStateRequest"
)

func (c *CallRoutingService) GetRoutingChannel(ctx context.Context, request *ozonetel.GetRoutingChannelRequest) (*ozonetel.GetRoutingChannelResponse, error) {
	if err := security.CheckWhiteList(
		ctx, c.conf.OzonetelWhitelist, c.conf.NumberOfHopsThatAddXForwardedFor,
		c.conf.VpcCidrIPPrefix,
	); err != nil {
		logger.Error(ctx, "IP whitelisting check failed for call routing api call", zap.Error(err))
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, request, GetRoutingChannelRequest, "", redactorConf.Config)
	phoneNumber, err := commontypes.ParsePhoneNumber(request.GetPhoneNumber())
	if err != nil {
		logger.Error(ctx, "invalid phone number passed in call routing request by ozonetel", zap.Error(err))
		logger.Info(
			ctx, "returning default routing channel in response",
			zap.Int32("default_channel", c.conf.Ozonetel.DefaultChannelNumber),
		)
		// return default channel if we are unable to figure out routing channel for user
		return &ozonetel.GetRoutingChannelResponse{
			RoutingChannel: c.conf.Ozonetel.DefaultChannelNumber,
		}, nil
	}
	if phoneNumber.GetCountryCode() == 0 {
		phoneNumber.CountryCode = 91
	}
	logger.Info(ctx, "received request to get routing channel", zap.String(logger.PHONE_NUMBER_HASH, obfuscator.HashedPhoneNum(phoneNumber)))
	resp, err := c.cxCallRoutingClient.GetRoutingChannelForUser(ctx, &callRoutingPb.GetRoutingChannelForUserRequest{
		PhoneNumber: phoneNumber,
		MonitorUcid: request.GetMonitorUcid(),
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error while fetching routing channel from cx", zap.Error(err))
		logger.Info(ctx, "returning default routing channel in response",
			zap.Int32("default_channel", c.conf.Ozonetel.DefaultChannelNumber))
		// return default channel if we are unable to figure out routing channel for user
		return &ozonetel.GetRoutingChannelResponse{
			RoutingChannel: c.conf.Ozonetel.DefaultChannelNumber,
		}, nil
	}
	// if a valid recording id is returned, the same is communicated to ozonetel
	// so that the user is not routed to any routing channel
	// either the recording id or (routing channel + priority) should only be populated in the response
	if resp.GetRecordingId() != callRoutingPb.RecordingIdentifier_RECORDING_IDENTIFIER_UNSPECIFIED {
		recordingId, ok := c.conf.Ozonetel.RecordingIdToNumberMapping[resp.GetRecordingId().String()]
		// if a valid recording id mapping is found, the corresponding mapped integer value is returned
		if ok {
			return &ozonetel.GetRoutingChannelResponse{RecordingId: recordingId}, nil
		}
		// otherwise we just log this error and proceed forward
		logger.Error(ctx, "recording id to number mapping not found in config", zap.String("recordingId", resp.GetRecordingId().String()))
	}
	channelNumber, ok := c.conf.Ozonetel.RoutingChannelToNumberMapping[resp.GetRoutingChannel().String()]
	if !ok {
		// add default channel number if not found in map
		channelNumber = c.conf.Ozonetel.DefaultChannelNumber
	}
	rpcResp := &ozonetel.GetRoutingChannelResponse{RoutingChannel: channelNumber}
	// populate priority field only when this flag is set
	if c.conf.Ozonetel.IsPriorityRoutingEnabled {
		rpcResp.Priority = resp.GetPriority()
	}
	// version field determines if IVR flow is enabled
	if resp.GetVersion() != 0 {
		rpcResp.Version = resp.GetVersion()
	}
	return rpcResp, nil
}

func (c *CallRoutingService) ProcessScreenPopCallback(ctx context.Context, req *ozonetel.ScreenPopCallbackResponse) (*empty.Empty, error) {
	if err := security.CheckWhiteList(
		ctx, c.conf.OzonetelWhitelist, c.conf.NumberOfHopsThatAddXForwardedFor,
		c.conf.VpcCidrIPPrefix,
	); err != nil {
		logger.Error(ctx, "IP whitelisting check failed for ProcessScreenPopCallback api call", zap.Error(err),
			zap.Any(logger.OZONETEL_MONITOR_UCID, req.GetMonitorUcid()))
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, ScreenPopCallbackRequest, "", redactorConf.Config)
	logger.Debug(ctx, "raw screenpop event", zap.Any("event", req.String()))
	callDetails := ozonetelVendor.ConvertScreenPopResponseToCallDetails(ctx, req, c.conf.Ozonetel)
	queueMsg := &ozonetelConsumerPb.AddOrUpdateOzonetelCallDetailsRequest{
		CallDetails: callDetails,
	}
	_, publishErr := c.callDetailsPublisher.Publish(ctx, queueMsg)
	if publishErr != nil {
		logger.Error(ctx, "error while publishing screenpop url payload to queue", zap.Error(publishErr), zap.Any(logger.OZONETEL_MONITOR_UCID, req.GetMonitorUcid()))
	}

	return &empty.Empty{}, nil
}

func (c *CallRoutingService) ProcessPushToURLCallback(ctx context.Context, req *ozonetel.PushToURLCallbackResponse) (*empty.Empty, error) {

	if err := security.CheckWhiteList(
		ctx, c.conf.OzonetelWhitelist, c.conf.NumberOfHopsThatAddXForwardedFor,
		c.conf.VpcCidrIPPrefix,
	); err != nil {
		logger.Error(ctx, "IP whitelisting check failed for ProcessPushToURLCallback api call",
			zap.Error(err), zap.Any(logger.OZONETEL_MONITOR_UCID, req.GetMonitorUcid()))
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, req, PushToURLCallbackRequest, "", redactorConf.Config)
	logger.Debug(ctx, "raw push to url event", zap.Any("event", req.String()))
	callDetails := ozonetelVendor.ConvertPushToURLResponseToCallDetails(ctx, req, c.conf.Ozonetel)
	queueMsg := &ozonetelConsumerPb.AddOrUpdateOzonetelCallDetailsRequest{
		CallDetails: callDetails,
	}
	_, publishErr := c.callDetailsPublisher.Publish(ctx, queueMsg)
	if publishErr != nil {
		logger.Error(ctx, "error while publishing push to url payload to queue", zap.Any(logger.OZONETEL_MONITOR_UCID, req.GetMonitorUcid()), zap.Error(publishErr))
	}

	return &empty.Empty{}, nil
}

func (c *CallRoutingService) InitiateIvr(ctx context.Context, request *ozonetel.InitiateIvrRequest) (*ozonetel.InitiateIvrResponse, error) {
	if err := security.CheckWhiteList(
		ctx, c.conf.OzonetelWhitelist, c.conf.NumberOfHopsThatAddXForwardedFor,
		c.conf.VpcCidrIPPrefix,
	); err != nil {
		logger.Error(ctx, "IP whitelisting check failed for initiate IVR api call", zap.Error(err))
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, request, InitiateIvrRequest, "", redactorConf.Config)
	logger.SecureDebug(ctx, vendorPb.Vendor_Ozonetel, "raw initiate ivr request", zap.String(logger.REQUEST, request.String()))
	parsedPhoneNumber := ozonetelVendor.ParseOzonetelPhoneNumber(ctx, request.GetPhoneNumber(), metrics.IncrementInvalidCampaignDidCount, "phoneNumber", ozonetelVendor.GetOzonetelCallbackLogFields(request.GetMonitorUcid(), ""))
	resp, err := c.cxCallIvrClient.InitiateIvr(ctx, ozonetelVendor.ConvertToCxInitiateIvr(ctx, request))
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error while calling cx initiate IVR", zap.Error(te), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()), zap.String(logger.PHONE_NUMBER_HASH, obfuscator.HashedPhoneNum(parsedPhoneNumber)))
		if resp.GetStatus().IsInvalidArgument() {
			return c.getInitiateIvrResponse(ctx, http.StatusBadRequest, initiateIvrInvalidArgument)
		}
		return c.getInitiateIvrResponse(ctx, http.StatusInternalServerError, initiateIvrInternal)
	}
	// record the sent question id to ozonotal
	metrics.RecordSentQuestionIDtoOzonotal(callIvrPb.IvrQuestion_name[resp.GetIvrStateInfo().GetIvrQuestionId()], resp.GetIvrStateInfo().GetPreferredLanguage().String())
	recordingId, ok := c.conf.Ozonetel.RecordingIdToNumberMapping[resp.GetIvrStateInfo().GetRecordingId().String()]
	// if a valid recording id mapping is found, the corresponding mapped integer value is returned
	if !ok {
		logger.Error(ctx, "invalid recording id in response", zap.String("recordingId", resp.GetIvrStateInfo().GetRecordingId().String()), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()))
	}
	ivrQuestionId := c.getIvrQuestionId(ctx, resp.GetIvrStateInfo().GetPreferredLanguage().String(), resp.GetIvrStateInfo().GetIvrQuestionId(), request.GetMonitorUcid())
	return &ozonetel.InitiateIvrResponse{
		Status:        http.StatusOK,
		IvrType:       int32(resp.GetIvrStateInfo().GetIvrType().Number()),
		IvrQuestionId: ivrQuestionId,
		IvrState:      int32(resp.GetIvrStateInfo().GetIvrState().Number()),
		RecordingId:   recordingId,
		WaitDuration:  resp.GetIvrStateInfo().GetWaitDuration(),
	}, nil
}

func (c *CallRoutingService) ProcessIvrUserInput(ctx context.Context, request *ozonetel.ProcessIvrUserInputRequest) (*ozonetel.ProcessIvrUserInputResponse, error) {
	if err := security.CheckWhiteList(
		ctx, c.conf.OzonetelWhitelist, c.conf.NumberOfHopsThatAddXForwardedFor,
		c.conf.VpcCidrIPPrefix,
	); err != nil {
		logger.Error(ctx, "IP whitelisting check failed for process IVR user input api call", zap.Error(err))
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, request, ProcessIvrUserInputRequest, "", redactorConf.Config)
	logger.SecureDebug(ctx, vendorPb.Vendor_Ozonetel, "raw process ivr user input request", zap.String(logger.REQUEST, request.String()))
	parsedPhoneNumber := ozonetelVendor.ParseOzonetelPhoneNumber(ctx, request.GetPhoneNumber(), metrics.IncrementInvalidCampaignDidCount, "phoneNumber", ozonetelVendor.GetOzonetelCallbackLogFields(request.GetMonitorUcid(), ""))
	internalQuestionId, found := lo.FindKeyBy(c.conf.Ozonetel.InternalQuestionIDToOzonetelQuestionID, func(k int32, v map[string]int32) bool {
		return lo.Contains(lo.Values(v), request.GetCurrentIvrQuestionId())
	})

	if !found {
		logger.Error(ctx, "error in finding internal question id for corresponding ozonotal question id", zap.Int32("targetValue", request.GetCurrentIvrQuestionId()))
	}
	var preferredLanguage string
	for lang, id := range c.conf.Ozonetel.InternalQuestionIDToOzonetelQuestionID[internalQuestionId] {
		if id == request.GetCurrentIvrQuestionId() {
			preferredLanguage = lang
			break
		}
	}
	// record the received question id from ozonotal
	metrics.RecordReceivedQuestionIdFromOzonotal(callIvrPb.IvrQuestion_name[internalQuestionId], preferredLanguage)
	// temp comment
	resp, err := c.cxCallIvrClient.ProcessIvrUserInput(ctx, ozonetelVendor.ConvertToCxProcessIvrUserInput(ctx, request, internalQuestionId))
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error while calling cx process ivr user input", zap.Error(te), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()), zap.String(logger.PHONE_NUMBER_HASH, obfuscator.HashedPhoneNum(parsedPhoneNumber)))
		if resp.GetStatus().IsInvalidArgument() {
			return c.getProcessIvrUserInputResponse(ctx, http.StatusBadRequest, processIvrUserInputInvalidArgument)
		}
		if resp.GetStatus().IsRecordNotFound() {
			return c.getProcessIvrUserInputResponse(ctx, http.StatusNotFound, processIvrUserInputNotFound)
		}
		return c.getProcessIvrUserInputResponse(ctx, http.StatusInternalServerError, processIvrUserInputInternal)
	}
	recordingId, ok := c.conf.Ozonetel.RecordingIdToNumberMapping[resp.GetIvrStateInfo().GetRecordingId().String()]
	// if a valid recording id mapping is found, the corresponding mapped integer value is returned
	if !ok {
		logger.Error(ctx, "invalid recording id in response", zap.String("recordingId", resp.GetIvrStateInfo().GetRecordingId().String()), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()))
	}
	metrics.RecordSentQuestionIDtoOzonotal(callIvrPb.IvrQuestion_name[resp.GetIvrStateInfo().GetIvrQuestionId()], resp.GetIvrStateInfo().GetPreferredLanguage().String())

	ivrQuestionId := c.getIvrQuestionId(ctx, resp.GetIvrStateInfo().GetPreferredLanguage().String(), resp.GetIvrStateInfo().GetIvrQuestionId(), request.GetMonitorUcid())
	return &ozonetel.ProcessIvrUserInputResponse{
		Status:        http.StatusOK,
		IvrType:       int32(resp.GetIvrStateInfo().GetIvrType().Number()),
		IvrQuestionId: ivrQuestionId,
		IvrState:      int32(resp.GetIvrStateInfo().GetIvrState().Number()),
		RecordingId:   recordingId,
		WaitDuration:  resp.GetIvrStateInfo().GetWaitDuration(),
	}, nil
}

func (c *CallRoutingService) GetIvrState(ctx context.Context, request *ozonetel.GetIvrStateRequest) (*ozonetel.GetIvrStateResponse, error) {
	if err := security.CheckWhiteList(
		ctx, c.conf.OzonetelWhitelist, c.conf.NumberOfHopsThatAddXForwardedFor,
		c.conf.VpcCidrIPPrefix,
	); err != nil {
		logger.Error(ctx, "IP whitelisting check failed for get IVR state api call", zap.Error(err))
		return nil, err
	}
	redactor.LogCallbackRequestData(ctx, request, GetIvrStateRequest, "", redactorConf.Config)
	logger.SecureDebug(ctx, vendorPb.Vendor_Ozonetel, "raw get ivr state request", zap.String(logger.REQUEST, request.String()))
	resp, err := c.cxCallIvrClient.GetIvrState(ctx, ozonetelVendor.ConvertToCxGetIvrState(ctx, request))
	parsedPhoneNumber := ozonetelVendor.ParseOzonetelPhoneNumber(ctx, request.GetPhoneNumber(), metrics.IncrementInvalidCampaignDidCount, "phoneNumber", ozonetelVendor.GetOzonetelCallbackLogFields(request.GetMonitorUcid(), ""))
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error while calling cx get IVR state", zap.Error(te), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()), zap.String(logger.PHONE_NUMBER_HASH, obfuscator.HashedPhoneNum(parsedPhoneNumber)))
		if resp.GetStatus().IsInvalidArgument() {
			return c.getGetIvrStateResponse(ctx, http.StatusBadRequest, getIvrStateInvalidArgument)
		}
		if resp.GetStatus().IsRecordNotFound() {
			return c.getGetIvrStateResponse(ctx, http.StatusNotFound, getIvrStateNotFound)
		}
		return c.getGetIvrStateResponse(ctx, http.StatusInternalServerError, getIvrStateInternal)
	}
	recordingId, ok := c.conf.Ozonetel.RecordingIdToNumberMapping[resp.GetIvrStateInfo().GetRecordingId().String()]
	// if a valid recording id mapping is found, the corresponding mapped integer value is returned
	if !ok {
		logger.Error(ctx, "invalid recording id in response", zap.String("recordingId", resp.GetIvrStateInfo().GetRecordingId().String()), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()))
	}
	ivrQuestionId := c.getIvrQuestionId(ctx, resp.GetIvrStateInfo().GetPreferredLanguage().String(), resp.GetIvrStateInfo().GetIvrQuestionId(), request.GetMonitorUcid())
	return &ozonetel.GetIvrStateResponse{
		Status:        http.StatusOK,
		IvrType:       int32(resp.GetIvrStateInfo().GetIvrType().Number()),
		IvrQuestionId: ivrQuestionId,
		IvrState:      int32(resp.GetIvrStateInfo().GetIvrState().Number()),
		RecordingId:   recordingId,
		WaitDuration:  resp.GetIvrStateInfo().GetWaitDuration(),
	}, nil
}
func (c *CallRoutingService) getIvrQuestionId(ctx context.Context, language string, internalQuestionId int32, monintorUcid string) int32 {
	preferredLanguage := typesv2.Language_LANGUAGE_ENGLISH.String()
	if language != "" && language != typesv2.Language_LANGUAGE_UNSPECIFIED.String() {
		preferredLanguage = language
	}
	ivrQuestionId, ok := c.conf.Ozonetel.InternalQuestionIDToOzonetelQuestionID[internalQuestionId][preferredLanguage]
	if !ok {
		logger.Error(ctx, "Mapping for internal question ID to ozonetel question ID is not present", zap.Int32("ivrQuestionId", internalQuestionId), zap.String(logger.OZONETEL_MONITOR_UCID, monintorUcid))
	}
	return ivrQuestionId
}
