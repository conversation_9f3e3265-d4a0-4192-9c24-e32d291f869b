Application:
  WorkerOptions:
    Options:
      MaxConcurrentActivityExecutionSize: 100
      WorkerActivitiesPerSecond: 200.0
      MaxConcurrentLocalActivityExecutionSize: 100
      WorkerLocalActivitiesPerSecond: 200.0
      TaskQueueActivitiesPerSecond: 600.0
      MaxConcurrentActivityTaskPollers: 5
      MaxConcurrentWorkflowTaskExecutionSize: 300
      MaxConcurrentWorkflowTaskPollers: 5
  Flags:
    SkipDeprecatedCelestialActivitiesRegistration: false

Server:
  HttpPort: 9090
  GrpcPort: 9000

AWS:
  Region: "ap-south-1"

Tracing:
  Enable: true

AaAnalytics:
  OldestTsFromTodayInMonths: 36

WorkflowParamsList:
  - WorkflowName: "RefreshWealthAnalysis"
    ActivityParamsList:
      - ActivityName: "GetDataAndInitiateAnalysis"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "120s"
        RetryParams:
          # Exponential retry strategy that runs for 12 hours with max cap between retries at 60min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 60m0s 60m0s 60m0s 60m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "60m"
            BackoffCoefficient: 2.0
            MaxAttempts: 22
      - ActivityName: "GetAnalysisStatus"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 12 hours with max cap between retries at 60min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 60m0s 60m0s 60m0s 60m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "60m"
            BackoffCoefficient: 2.0
            MaxAttempts: 22
      - ActivityName: "UpdateUserAnalysis"
        ScheduleToCloseTimeout: "12h"
        StartToCloseTimeout: "120s"
        RetryParams:
          # Exponential retry strategy that runs for 12 hours with max cap between retries at 60min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 60m0s 60m0s 60m0s 60m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "60m"
            BackoffCoefficient: 2.0
            MaxAttempts: 22
      - ActivityName: "UpdateAnalysisRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
  - WorkflowName: "ProcessTechAnalysisAttempt"
    ActivityParamsList:
      - ActivityName: "UpdateAnalysisAttempt"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "InitiateWealthAnalysis"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "UpdateAnalysedUser"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
