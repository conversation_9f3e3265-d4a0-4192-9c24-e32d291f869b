// nolint: dogsled
package consumer_test

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/gamma/firefly/config"
	genConf "github.com/epifi/gamma/firefly/config/genconf"
	"github.com/epifi/gamma/firefly/v2/consumer"
	daoMocks "github.com/epifi/gamma/firefly/v2/dao/mocks"
	"github.com/epifi/gamma/firefly/v2/test"
)

var (
	ffConf    *config.Config
	ffGenConf *genConf.Config
)

func TestMain(m *testing.M) {
	conf, genConf, _, _, teardown := test.InitTestServerV2()
	ffConf = conf
	ffGenConf = genConf
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockDependencies struct {
	cardRequestDao      *daoMocks.MockCardRequestDao
	creditCardDao       *daoMocks.MockCreditCardDao
	creditCardOffersDao *daoMocks.MockCreditCardOffersDao
}

func getFireflyV2SvcWithMocks(t *testing.T) (*consumer.Service, *mockDependencies, func()) {
	ctr := gomock.NewController(t)

	mockCardRequestDao := daoMocks.NewMockCardRequestDao(ctr)
	mockCreditCardDao := daoMocks.NewMockCreditCardDao(ctr)
	mockOffersDao := daoMocks.NewMockCreditCardOffersDao(ctr)

	svc := consumer.NewService(mockCardRequestDao, mockCreditCardDao, mockOffersDao, nil)

	md := &mockDependencies{
		cardRequestDao:      mockCardRequestDao,
		creditCardDao:       mockCreditCardDao,
		creditCardOffersDao: mockOffersDao,
	}

	return svc, md, func() {
		ctr.Finish()
	}
}
