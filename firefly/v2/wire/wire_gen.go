// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/creditcard"
	"github.com/epifi/gamma/firefly/config/genconf"
	"github.com/epifi/gamma/firefly/v2"
	"github.com/epifi/gamma/firefly/v2/consumer"
	"github.com/epifi/gamma/firefly/v2/dao/impl"
	"github.com/epifi/gamma/firefly/v2/wire/types"
)

// Injectors from wire.go:

func InitialiseFireflyV2Svc(conf *genconf.Config, db types.CreditCardFederalPGDB, usersClient user.UsersClient, ccVgClient creditcard.CreditCardClient, authClient auth.AuthClient, bankCustClient bankcust.BankCustomerServiceClient, onbClient onboarding.OnboardingClient, ccOnboardingStateUpdateEventPublisher types.CcOnboardingStateUpdateEventPublisher) *v2.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	cardRequestDao := impl.NewCardRequestDao(db, domainIdGenerator)
	creditCardDao := impl.NewCreditCardDao(db, domainIdGenerator)
	creditCardOfferDao := impl.NewCreditCardOfferDao(db, domainIdGenerator)
	service := v2.NewService(conf, ccVgClient, authClient, cardRequestDao, creditCardDao, creditCardOfferDao, usersClient, bankCustClient, onbClient, ccOnboardingStateUpdateEventPublisher)
	return service
}

func InitialiseConsumerV2Service(db types.CreditCardFederalPGDB, eventBroker events.Broker) *consumer.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	cardRequestDao := impl.NewCardRequestDao(db, domainIdGenerator)
	creditCardDao := impl.NewCreditCardDao(db, domainIdGenerator)
	creditCardOfferDao := impl.NewCreditCardOfferDao(db, domainIdGenerator)
	service := consumer.NewService(cardRequestDao, creditCardDao, creditCardOfferDao, eventBroker)
	return service
}
