package providers

import (
	"context"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	rpEnachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
)

// MandateViewProvider interface provides abstract methods to process data for mandate setup stage
// to serve mandate info to client, the process can be divided into three steps
// - validation: like dedupe/expiry check or some more specific to each vendor/program
// - deeplink generation: once validated, fe deeplink to be generated
// - post dl generation: once deeplink is generated then any processing required can be part of this step
//
// Note: Potential race condition (leading to data loss) can occur if the LSE (preapprovedloan.LoanStepExecution)
// in the interface method arguments below is being updated from multiple places simultaneously.
// For example, if the LSE is being updated from within a MandateViewProvider implementation while at the same time
// being updated from within an activity, the second updater can override fields set by the first updater.
// One way to avoid this is to take a DB read lock on LSE record before calling below methods,
// which is an overkill as not all implementations of MandateViewProvider may be updating LSE.
// Hence the locks should be taken within each implementation as needed, and thus only the LSE ID
// should be passed as an argument to below methods instead of the complete struct.
// TODO(Brijesh): Make sure this race condition does not occur when implementing new version of mandate setup.
type MandateViewProvider interface {
	// ValidateLoanStep method is used to run some validation on top of lse object
	// validations like expiry dedupe can be abstracted behind this method
	// if a vendor/program does not have any validations then that specific implementation can return nil
	// return error and string if any error is encountered
	// here error would contain error object returned by underlying impl and string here is
	// user facing error message which can be used to propagate it to client layer for more contextual messaging around the error
	ValidateLoanStep(ctx context.Context, loanHeader *palPb.LoanHeader, lse *palPb.LoanStepExecution, accDetails *palPb.MandateData_BankingDetails_AccountDetails) (error, string)
	// GetLoansMandateSetupScreen method is used to generate mandate setup deeplink for given vendor/program
	// steps like calling vendor or internal API can be abstracted behind this method
	GetLoansMandateSetupScreen(ctx context.Context, loanHeader *palPb.LoanHeader, lse *palPb.LoanStepExecution, accDetails *palPb.MandateData_BankingDetails_AccountDetails, mode rpEnachEnumsPb.EnachRegistrationAuthMode) (*deeplinkPb.Deeplink, error)
	// PostDlGenerationProcessing method is used to perform some processing post mandate setup deeplink is generated
	// this processing can involve db update, triggering some temporal signal, etc depending on vendor/loan program
	PostDlGenerationProcessing(ctx context.Context, loanHeader *palPb.LoanHeader, lseId string, accDetails *palPb.MandateData_BankingDetails_AccountDetails) error
}
