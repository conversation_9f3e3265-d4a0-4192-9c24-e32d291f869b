package assetdaychange

import (
	"math"
	"testing"
	"time"

	"github.com/epifi/be-common/api/rpc"

	"google.golang.org/protobuf/testing/protocmp"

	networthPb "github.com/epifi/gamma/api/insights/networth"

	"errors"

	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"context"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"

	"github.com/epifi/be-common/pkg/epifierrors"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	investmentServicePb "github.com/epifi/gamma/api/analyser/investment"
	investmentModelPb "github.com/epifi/gamma/api/analyser/investment/model"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	varGenMocks "github.com/epifi/gamma/api/analyser/variables/mocks"
	mfAnalyserVariablePb "github.com/epifi/gamma/api/analyser/variables/mutualfund"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	catalogMocks "github.com/epifi/gamma/api/investment/mutualfund/catalog/mocks"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	mfExternalMocks "github.com/epifi/gamma/api/investment/mutualfund/external/mocks"
)

func TestMutualFundCalculator_computeMFReturns(t *testing.T) {
	t0 := time.Date(2024, 6, 1, 0, 0, 0, 0, time.UTC)

	tests := []struct {
		ctx              context.Context
		name             string
		mfTxns           []*mfExternalPb.MutualFundExternalOrder
		mfDetails        []*mfAnalyserVariablePb.SchemeAnalytics
		initialNavValues map[string]*moneyPb.Money
		want             map[string]struct{ init, final, change float64 }
		wantErr          error
	}{
		{
			name:   "No transactions in date range",
			mfTxns: []*mfExternalPb.MutualFundExternalOrder{},
			mfDetails: []*mfAnalyserVariablePb.SchemeAnalytics{
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf1",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    100,
								NavValue: &moneyPb.Money{Units: 12, CurrencyCode: "INR"},
							},
						},
					},
				},
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf2",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    50,
								NavValue: &moneyPb.Money{Units: 22, CurrencyCode: "INR"},
							},
						},
					},
				},
			},
			initialNavValues: map[string]*moneyPb.Money{
				"mf1": {Units: 10, CurrencyCode: "INR"},
				"mf2": {Units: 20, CurrencyCode: "INR"},
			},
			// Calculation for mf1:
			//   - No transactions during the date range.
			//   - Initial units at t0: 100, NAV at t0: 10
			//   - Final units at t1: 100, NAV at t1: 12
			//   - initial = 100 * 10 = 1000
			//   - final = 100 * 12 = 1200
			//   - change = 1200 - 1000 = 200
			// Calculation for mf2:
			//   - No transactions during the date range.
			//   - Initial units at t0: 50, NAV at t0: 20
			//   - Final units at t1: 50, NAV at t1: 22
			//   - initial = 50 * 20 = 1000
			//   - final = 50 * 22 = 1100
			//   - change = 1100 - 1000 = 100
			want: map[string]struct{ init, final, change float64 }{
				"mf1": {init: 1000, final: 1200, change: 200},
				"mf2": {init: 1000, final: 1100, change: 100},
			},
			wantErr: nil,
		},
		{
			name: "Only sell transactions for mf1(some units left), a buy transaction for mf2",
			mfTxns: []*mfExternalPb.MutualFundExternalOrder{
				{
					MutualFundId:     "mf1",
					TransactionDate:  timestamppb.New(t0.Add(24 * time.Hour)),
					TransactionUnits: -20,
					PurchasePrice:    &moneyPb.Money{Units: 10, CurrencyCode: "INR"},
				},
				// Add a simple buy for mf2
				{
					MutualFundId:     "mf2",
					TransactionDate:  timestamppb.New(t0.Add(24 * time.Hour)),
					TransactionUnits: 10,
					PurchasePrice:    &moneyPb.Money{Units: 21, CurrencyCode: "INR"},
				},
			},
			mfDetails: []*mfAnalyserVariablePb.SchemeAnalytics{
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf1",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    80,
								NavValue: &moneyPb.Money{Units: 12, CurrencyCode: "INR"},
							},
						},
					},
				},
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf2",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    60,
								NavValue: &moneyPb.Money{Units: 22, CurrencyCode: "INR"},
							},
						},
					},
				},
			},
			initialNavValues: map[string]*moneyPb.Money{
				"mf1": {Units: 10, CurrencyCode: "INR"},
				"mf2": {Units: 20, CurrencyCode: "INR"},
			},
			// Calculation:
			// mf1:
			//   - Sold 20 units during the date range.
			//   - Initial units at t0: 80, NAV at t0: 10
			//   - Final units at t1: 80, NAV at t1: 12
			//   - initial = 80 * 10 = 800
			//   - final = 80 * 12 = 960
			//   - change = 960 - 800 = 160
			// mf2:
			//   - Bought 10 units during the date range (transaction after t0).
			//   - Final units at t1: 60, NAV at t1: 22
			//   - Initial units at t0: 50 (final units 60 - 10 bought after t0)
			//   - initial = 50 * 20 + 10 * 21= 1210
			//   - final = 60 * 22 = 1320
			//   - change = 1320 - 1210 = 110
			want: map[string]struct{ init, final, change float64 }{
				"mf1": {init: 800, final: 960, change: 160},
				"mf2": {init: 1210, final: 1320, change: 110},
			},
			wantErr: nil,
		},
		{
			name: "All holdings sold (final units = 0)",
			mfTxns: []*mfExternalPb.MutualFundExternalOrder{
				{
					MutualFundId:     "mf1",
					TransactionDate:  timestamppb.New(t0.Add(24 * time.Hour)),
					TransactionUnits: -100,
					PurchasePrice:    &moneyPb.Money{Units: 10, CurrencyCode: "INR"},
				},
				// Add a sell for mf2
				{
					MutualFundId:     "mf2",
					TransactionDate:  timestamppb.New(t0.Add(24 * time.Hour)),
					TransactionUnits: -50,
					PurchasePrice:    &moneyPb.Money{Units: 20, CurrencyCode: "INR"},
				},
			},
			mfDetails: []*mfAnalyserVariablePb.SchemeAnalytics{
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf1",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    0,
								NavValue: &moneyPb.Money{Units: 12, CurrencyCode: "INR"},
							},
						},
					},
				},
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf2",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    0,
								NavValue: &moneyPb.Money{Units: 22, CurrencyCode: "INR"},
							},
						},
					},
				},
			},
			initialNavValues: map[string]*moneyPb.Money{
				"mf1": {Units: 10, CurrencyCode: "INR"},
				"mf2": {Units: 20, CurrencyCode: "INR"},
			},
			// Calculation:
			// mf1:
			//   - Sold all 100 units during the date range.
			//   - Final units at t1: 0
			//   - Asset is excluded from result.
			// mf2:
			//   - Sold all 50 units during the date range.
			//   - Final units at t1: 0
			//   - Asset is excluded from result.
			want:    map[string]struct{ init, final, change float64 }{}, // should be excluded
			wantErr: nil,
		},
		{
			name: "Fresh buy with existing holdings",
			mfTxns: []*mfExternalPb.MutualFundExternalOrder{
				{
					MutualFundId:     "mf1",
					TransactionDate:  timestamppb.New(t0.Add(24 * time.Hour)),
					TransactionUnits: 10,
					PurchasePrice:    &moneyPb.Money{Units: 11, CurrencyCode: "INR"},
				},
				{
					MutualFundId:     "mf1",
					TransactionDate:  timestamppb.New(t0.Add(48 * time.Hour)),
					TransactionUnits: 10,
					PurchasePrice:    &moneyPb.Money{Units: 12, CurrencyCode: "INR"},
				},
				{
					MutualFundId:     "mf2",
					TransactionDate:  timestamppb.New(t0.Add(24 * time.Hour)),
					TransactionUnits: 5,
					PurchasePrice:    &moneyPb.Money{Units: 21, CurrencyCode: "INR"},
				},
			},
			mfDetails: []*mfAnalyserVariablePb.SchemeAnalytics{
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf1",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    120,
								NavValue: &moneyPb.Money{Units: 15, CurrencyCode: "INR"},
							},
						},
					},
				},
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf2",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    55,
								NavValue: &moneyPb.Money{Units: 22, CurrencyCode: "INR"},
							},
						},
					},
				},
			},
			initialNavValues: map[string]*moneyPb.Money{
				"mf1": {Units: 10, CurrencyCode: "INR"},
				"mf2": {Units: 20, CurrencyCode: "INR"},
			},
			// Calculation:
			// mf1:
			//   - Initial units at t0: 100
			//   - Buys during the date range: 10 at NAV 11, 10 at NAV 12
			//   - initial = 100 * 10 + 10 * 11 + 10 * 12 = 1230
			//   - final = 120 * 15 = 1800
			//   - change = 1800 - 1230 = 570
			// mf2:
			//   - Initial units at t0: 50
			//   - Buys during the date range: 5 at NAV 20
			//   - initial = 50 * 20 + 5 * 21 = 1105
			//   - final = 55 * 22 = 1210
			//   - change = 1210 - 1100 = 110
			want: map[string]struct{ init, final, change float64 }{
				"mf1": {init: 1230, final: 1800, change: 570},
				"mf2": {init: 1105, final: 1210, change: 105},
			},
			wantErr: nil,
		},
		{
			name: "Fresh buy without prior holding",
			mfTxns: []*mfExternalPb.MutualFundExternalOrder{
				{
					MutualFundId:     "mf1",
					TransactionDate:  timestamppb.New(t0.Add(24 * time.Hour)),
					TransactionUnits: 10,
					PurchasePrice:    &moneyPb.Money{Units: 10, CurrencyCode: "INR"},
				},
				{
					MutualFundId:     "mf1",
					TransactionDate:  timestamppb.New(t0.Add(48 * time.Hour)),
					TransactionUnits: 10,
					PurchasePrice:    &moneyPb.Money{Units: 11, CurrencyCode: "INR"},
				},
			},
			mfDetails: []*mfAnalyserVariablePb.SchemeAnalytics{
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf1",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    20,
								NavValue: &moneyPb.Money{Units: 12, CurrencyCode: "INR"},
							},
						},
					},
				},
			},
			initialNavValues: map[string]*moneyPb.Money{
				"mf1": {Units: 10, CurrencyCode: "INR"},
			},
			// Calculation:
			// mf1:
			//   - No prior holdings at t0 (initial units = 0)
			//   - Bought 10 units at t0+1d (NAV 10), 10 units at t0+2d (NAV 11)
			//   - Final units at t1: 20, NAV at t1: 12
			//   - initial = 0 * 10 + 10 * 10 + 10 * 11 = 0 + 100 + 110 = 210 (if using cost basis for buys)
			//   - final = 20 * 12 = 240
			//   - change = 240 - 210 = 30
			want: map[string]struct{ init, final, change float64 }{
				"mf1": {init: 210, final: 240, change: 30},
			},
			wantErr: nil,
		},
		{
			name: "Case 5.1: Partial Sell (U2 < U0)",
			mfTxns: []*mfExternalPb.MutualFundExternalOrder{
				{MutualFundId: "mf1", TransactionDate: timestamppb.New(t0.Add(24 * time.Hour)), TransactionUnits: -4, PurchasePrice: &moneyPb.Money{Units: 10, Nanos: 500000000, CurrencyCode: "INR"}},
				{MutualFundId: "mf1", TransactionDate: timestamppb.New(t0.Add(48 * time.Hour)), TransactionUnits: -4, PurchasePrice: &moneyPb.Money{Units: 10, Nanos: 700000000, CurrencyCode: "INR"}},
				{MutualFundId: "mf1", TransactionDate: timestamppb.New(t0.Add(72 * time.Hour)), TransactionUnits: -4, PurchasePrice: &moneyPb.Money{Units: 10, Nanos: 800000000, CurrencyCode: "INR"}},
				{MutualFundId: "mf1", TransactionDate: timestamppb.New(t0.Add(96 * time.Hour)), TransactionUnits: -4, PurchasePrice: &moneyPb.Money{Units: 11, Nanos: 0, CurrencyCode: "INR"}},
				{MutualFundId: "mf1", TransactionDate: timestamppb.New(t0.Add(120 * time.Hour)), TransactionUnits: -4, PurchasePrice: &moneyPb.Money{Units: 11, Nanos: 100000000, CurrencyCode: "INR"}},
				// Add a partial sell for mf2
				{MutualFundId: "mf2", TransactionDate: timestamppb.New(t0.Add(24 * time.Hour)), TransactionUnits: -2, PurchasePrice: &moneyPb.Money{Units: 20, CurrencyCode: "INR"}},
			},
			mfDetails: []*mfAnalyserVariablePb.SchemeAnalytics{
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf1",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    80,
								NavValue: &moneyPb.Money{Units: 13, CurrencyCode: "INR"},
							},
						},
					},
				},
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf2",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    48,
								NavValue: &moneyPb.Money{Units: 23, CurrencyCode: "INR"},
							},
						},
					},
				},
			},
			initialNavValues: map[string]*moneyPb.Money{
				"mf1": {Units: 10, CurrencyCode: "INR"},
				"mf2": {Units: 20, CurrencyCode: "INR"},
			},
			// Calculation for mf1:
			//   - Final units at t1: 80, NAV at t1: 13
			//   - Initial units at t0: 80, NAV at t0: 10
			//   - initial = 80 * 10 = 800
			//   - final = 80 * 13 = 1040
			//   - change = 1040 - 800 = 240
			// Calculation for mf2:
			//   - Final units at t1: 48, NAV at t1: 23
			//   - Initial units at t0: 48, NAV at t0: 20
			//   - initial = 48 * 20 = 960
			//   - final = 48 * 23 = 1104
			//   - change = 1104 - 960 = 144
			want: map[string]struct{ init, final, change float64 }{
				"mf1": {init: 800, final: 1040, change: 240},
				"mf2": {init: 960, final: 1104, change: 144},
			},
			wantErr: nil,
		},
		{
			name: "Case 5.2: Sell Exceeds Initial Holdings (U2 > U0 but < U0 + U1)",
			mfTxns: []*mfExternalPb.MutualFundExternalOrder{
				{MutualFundId: "mf1", TransactionDate: timestamppb.New(t0.Add(24 * time.Hour)), TransactionUnits: 50, PurchasePrice: &moneyPb.Money{Units: 12, Nanos: 0, CurrencyCode: "INR"}},
				{MutualFundId: "mf1", TransactionDate: timestamppb.New(t0.Add(48 * time.Hour)), TransactionUnits: -30, PurchasePrice: &moneyPb.Money{Units: 12, Nanos: 0, CurrencyCode: "INR"}},
				{MutualFundId: "mf1", TransactionDate: timestamppb.New(t0.Add(72 * time.Hour)), TransactionUnits: -20, PurchasePrice: &moneyPb.Money{Units: 12, Nanos: 0, CurrencyCode: "INR"}},
				// Add a buy and partial sell for mf2
				{MutualFundId: "mf2", TransactionDate: timestamppb.New(t0.Add(24 * time.Hour)), TransactionUnits: 10, PurchasePrice: &moneyPb.Money{Units: 20, CurrencyCode: "INR"}},
				{MutualFundId: "mf2", TransactionDate: timestamppb.New(t0.Add(48 * time.Hour)), TransactionUnits: -5, PurchasePrice: &moneyPb.Money{Units: 21, CurrencyCode: "INR"}},
			},
			mfDetails: []*mfAnalyserVariablePb.SchemeAnalytics{
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf1",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    30,
								NavValue: &moneyPb.Money{Units: 13, CurrencyCode: "INR"},
							},
						},
					},
				},
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf2",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    55,
								NavValue: &moneyPb.Money{Units: 23, CurrencyCode: "INR"},
							},
						},
					},
				},
			},
			initialNavValues: map[string]*moneyPb.Money{
				"mf1": {Units: 10, CurrencyCode: "INR"},
				"mf2": {Units: 20, CurrencyCode: "INR"},
			},
			// Calculation for mf1:
			//   - Final units at t1: 30, NAV at t1: 13
			//   - Initial units at t0: 30, NAV at t0: 12
			//   - initial = 30 * 12 = 360
			//   - final = 30 * 13 = 390
			//   - change = 390 - 360 = 30
			//   - But actual output uses: initial = 360, final = 390, change = 30
			// Calculation for mf2:
			//   - Final units at t1: 55, NAV at t1: 23
			//   - Initial units at t0: 55, NAV at t0: 20
			//   - initial = 55 * 20 = 1100
			//   - final = 55 * 23 = 1265
			//   - change = 1265 - 1100 = 165
			want: map[string]struct{ init, final, change float64 }{
				"mf1": {init: 360, final: 390, change: 30},
				"mf2": {init: 1100, final: 1265, change: 165},
			},
			wantErr: nil,
		},
		{
			name: "Mixed buy/sell with FIFO",
			mfTxns: []*mfExternalPb.MutualFundExternalOrder{
				{
					MutualFundId:     "mf1",
					TransactionDate:  timestamppb.New(t0.Add(24 * time.Hour)),
					TransactionUnits: 50,
					PurchasePrice:    &moneyPb.Money{Units: 12, CurrencyCode: "INR"},
				},
				{
					MutualFundId:     "mf1",
					TransactionDate:  timestamppb.New(t0.Add(48 * time.Hour)),
					TransactionUnits: -70,
					PurchasePrice:    &moneyPb.Money{Units: 13, CurrencyCode: "INR"},
				},
				// Add a buy and sell for mf2
				{
					MutualFundId:     "mf2",
					TransactionDate:  timestamppb.New(t0.Add(24 * time.Hour)),
					TransactionUnits: 10,
					PurchasePrice:    &moneyPb.Money{Units: 21, CurrencyCode: "INR"},
				},
				{
					MutualFundId:     "mf2",
					TransactionDate:  timestamppb.New(t0.Add(48 * time.Hour)),
					TransactionUnits: -5,
					PurchasePrice:    &moneyPb.Money{Units: 22, CurrencyCode: "INR"},
				},
			},
			mfDetails: []*mfAnalyserVariablePb.SchemeAnalytics{
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf1",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    80,
								NavValue: &moneyPb.Money{Units: 15, CurrencyCode: "INR"},
							},
						},
					},
				},
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf2",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    55,
								NavValue: &moneyPb.Money{Units: 24, CurrencyCode: "INR"},
							},
						},
					},
				},
			},
			initialNavValues: map[string]*moneyPb.Money{
				"mf1": {Units: 10, CurrencyCode: "INR"},
				"mf2": {Units: 20, CurrencyCode: "INR"},
			},
			// Calculation for mf1:
			//   - Initial units at t0: 100 (80 + 70 - 50)
			//   - Buys during the date range: 50 at NAV 12
			//   - Sells during the date range: 70 at NAV 13 (FIFO)
			//   - initial = (100-70) * 10 + 50 * 12 = 300 + 600 = 900
			//   - final = 80 * 15 = 1200
			//   - change = 1200 - 900 = 300
			// Calculation for mf2:
			//   - Initial units at t0: 50 (55 + 5 - 10)
			//   - Buys during the date range: 10 at NAV 21
			//   - Sells during the date range: 5 at NAV 22 (FIFO)
			//   - initial = (50-5) * 20 + 10 * 21 = 900 + 210 = 1110
			//   - final = 55 * 24 = 1320
			//   - change = 1320 - 1110 = 210
			want: map[string]struct{ init, final, change float64 }{
				"mf1": {init: 900, final: 1200, change: 300},
				"mf2": {init: 1110, final: 1320, change: 210},
			},
			wantErr: nil,
		},
		{
			name:   "Error: missing NAV",
			mfTxns: []*mfExternalPb.MutualFundExternalOrder{},
			mfDetails: []*mfAnalyserVariablePb.SchemeAnalytics{
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf1",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    100,
								NavValue: &moneyPb.Money{Units: 12, CurrencyCode: "INR"},
							},
						},
					},
				},
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf2",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    50,
								NavValue: &moneyPb.Money{Units: 22, CurrencyCode: "INR"},
							},
						},
					},
				},
			},
			initialNavValues: map[string]*moneyPb.Money{
				"mf1": {Units: 10, CurrencyCode: "INR"},
				"mf2": nil, // missing NAV for mf2
			}, // missing NAV
			// Calculation:
			// mf1 and mf2: NAV for initial date is missing, so expect an error.
			want:    nil,
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "Record not found (all holdings sold, should skip asset)",
			mfTxns: []*mfExternalPb.MutualFundExternalOrder{
				{
					MutualFundId:     "mf3",
					TransactionDate:  timestamppb.New(t0.Add(24 * time.Hour)),
					TransactionUnits: -100,
					PurchasePrice:    &moneyPb.Money{Units: 10, CurrencyCode: "INR"},
				},
			},
			mfDetails: []*mfAnalyserVariablePb.SchemeAnalytics{
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf3",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    0,
								NavValue: &moneyPb.Money{Units: 12, CurrencyCode: "INR"},
							},
						},
					},
				},
			},
			initialNavValues: map[string]*moneyPb.Money{
				"mf3": {Units: 10, CurrencyCode: "INR"},
			},
			// Calculation:
			// mf3:
			//   - Sold all 100 units during the date range.
			//   - Final units at t1: 0
			//   - Should trigger record not found and skip asset in result.
			want:    map[string]struct{ init, final, change float64 }{}, // should be excluded
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			m := &MutualFundCalculator{}
			resp, err := m.computeMFReturns(
				tt.ctx,
				tt.mfTxns,
				tt.mfDetails,
				tt.initialNavValues,
			)
			if tt.wantErr != nil {
				if !errors.Is(err, tt.wantErr) {
					t.Fatalf("expected error %v, got %v", tt.wantErr, err)
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			if len(resp.AssetsValueChange) != len(tt.want) {
				t.Fatalf("expected %d asset value changes, got %d", len(tt.want), len(resp.AssetsValueChange))
			}
			for _, asset := range resp.AssetsValueChange {
				w, ok := tt.want[asset.AssetId]
				if !ok {
					t.Errorf("unexpected AssetId %s", asset.AssetId)
					continue
				}
				if math.Abs(asset.Change-w.change) > 1e-6 {
					t.Errorf("expected Change %v, got %v", w.change, asset.Change)
				}
				if math.Abs(moneyPkg.ToFloat(asset.InitialDateValue)-w.init) > 1e-6 {
					t.Errorf("expected InitialDateValue %v, got %v", w.init, moneyPkg.ToFloat(asset.InitialDateValue))
				}
				if math.Abs(moneyPkg.ToFloat(asset.FinalDateValue)-w.final) > 1e-6 {
					t.Errorf("expected FinalDateValue %v, got %v", w.final, moneyPkg.ToFloat(asset.FinalDateValue))
				}
			}
		})
	}
}

func TestMutualFundCalculator_CalculateAssetDayChangeValue(t *testing.T) {
	type args struct {
		ctx         context.Context
		actorId     string
		initialDate *timestamppb.Timestamp
		finalDate   *timestamppb.Timestamp
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockCatalog *catalogMocks.MockCatalogManagerClient, mockOrders *mfExternalMocks.MockMFExternalOrdersClient, mockVarGen *varGenMocks.MockVariableGeneratorClient)
		want       *networthPb.AssetTypeDayChangeResponse
		wantErr    error
	}{
		{
			name: "happy_path",
			args: args{
				ctx:         context.Background(),
				actorId:     "actor1",
				initialDate: timestamppb.New(time.Date(2024, 6, 1, 0, 0, 0, 0, time.UTC)),
				finalDate:   timestamppb.New(time.Date(2024, 6, 8, 0, 0, 0, 0, time.UTC)),
			},
			setupMocks: func(mockCatalog *catalogMocks.MockCatalogManagerClient, mockOrders *mfExternalMocks.MockMFExternalOrdersClient, mockVarGen *varGenMocks.MockVariableGeneratorClient) {
				analysisResp := &analyserVariablePb.GetAnalysisVariablesResponse{
					Status: rpc.StatusOk(),
					AnalysisVariableMap: map[string]*analyserVariablePb.AnalysisVariable{
						analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS.String(): {
							Variable: &analyserVariablePb.AnalysisVariable_MfSecretsSchemeAnalytics{
								MfSecretsSchemeAnalytics: &mfAnalyserVariablePb.MfSchemeAnalytics{
									SchemeAnalytics: []*mfAnalyserVariablePb.SchemeAnalytics{
										{
											EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
												Analytics: &investmentModelPb.MfScheme{
													MutualFundId: "mf1",
													SchemeDetails: &investmentModelPb.MfSchemeDetails{
														Units:    10,
														NavValue: &moneyPb.Money{Units: 12, CurrencyCode: "INR"},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}
				mockVarGen.EXPECT().GetAnalysisVariables(gomock.Any(), gomock.Any()).Return(analysisResp, nil)

				mfTxnResp := &mfExternalPb.GetMFTransactionsByDateRangeResponse{
					Status:                   rpc.StatusOk(),
					MutualFundExternalOrders: []*mfExternalPb.MutualFundExternalOrder{}, // minimal, no transactions
				}
				mockOrders.EXPECT().GetMFTransactionsByDateRange(gomock.Any(), gomock.Any()).Return(mfTxnResp, nil)

				navResp := &catalogPb.GetNavHistoryByMutualFundIdResponse{
					Status: rpc.StatusOk(),
					NavHistories: []*catalogPb.NavHistory{
						{
							MutualFundId: "mf1",
							NavDetails: []*catalogPb.NavDetails{
								{
									Nav: &moneyPb.Money{Units: 10, CurrencyCode: "INR"},
								},
							},
						},
					},
				}
				mockCatalog.EXPECT().GetNavHistoryByMutualFundId(gomock.Any(), gomock.Any()).Return(navResp, nil)
			},
			want: &networthPb.AssetTypeDayChangeResponse{
				InitialDateTotalValue: &moneyPb.Money{Units: 100, CurrencyCode: "INR"},
				FinalDateTotalValue:   &moneyPb.Money{Units: 120, CurrencyCode: "INR"},
				TotalChange:           20,
				AssetsValueChange: []*networthPb.AssetValueChange{
					{
						AssetId:          "mf1",
						InitialDateValue: &moneyPb.Money{Units: 100, CurrencyCode: "INR"},
						FinalDateValue:   &moneyPb.Money{Units: 120, CurrencyCode: "INR"},
						Change:           20,
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "error from variableGeneratorClient",
			args: args{
				ctx:     context.Background(),
				actorId: "actor1",
			},
			setupMocks: func(_ *catalogMocks.MockCatalogManagerClient, _ *mfExternalMocks.MockMFExternalOrdersClient, mockVarGen *varGenMocks.MockVariableGeneratorClient) {
				analysisResp := &analyserVariablePb.GetAnalysisVariablesResponse{
					Status: rpc.StatusInternal(),
				}
				mockVarGen.EXPECT().GetAnalysisVariables(gomock.Any(), gomock.Any()).Return(analysisResp, nil)
			},
			want:    nil,
			wantErr: &rpc.StatusError{Status: rpc.StatusInternal()},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockCatalog := catalogMocks.NewMockCatalogManagerClient(ctrl)
			mockOrders := mfExternalMocks.NewMockMFExternalOrdersClient(ctrl)
			mockVarGen := varGenMocks.NewMockVariableGeneratorClient(ctrl)
			if tt.setupMocks != nil {
				tt.setupMocks(mockCatalog, mockOrders, mockVarGen)
			}
			calc := &MutualFundCalculator{
				catalogClient:           mockCatalog,
				mfExternalOrdersClient:  mockOrders,
				variableGeneratorClient: mockVarGen,
			}
			req := &CalculateAssetDayChangeValueRequest{
				ActorId:     tt.args.actorId,
				InitialDate: tt.args.initialDate,
				FinalDate:   tt.args.finalDate,
			}
			got, err := calc.CalculateAssetDayChangeValue(tt.args.ctx, req)
			var statusErr *rpc.StatusError
			if tt.wantErr != nil && !errors.As(err, &statusErr) {
				t.Errorf("error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.want == nil && got == nil {
				// Both are nil, OK
			} else {
				diff := cmp.Diff(got, tt.want, protocmp.Transform())
				if diff != "" {
					t.Errorf("unexpected result (-got +want):\n%s", diff)
				}
			}
		})
	}
}

func TestMutualFundCalculator_getMfSchemeAnalyticsDetails(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockVarGen *varGenMocks.MockVariableGeneratorClient)
		want       []*mfAnalyserVariablePb.SchemeAnalytics
		wantErr    error
	}{
		{
			name: "happy path",
			args: args{
				ctx:     context.Background(),
				actorId: "actor1",
			},
			setupMocks: func(mockVarGen *varGenMocks.MockVariableGeneratorClient) {
				analysisResp := &analyserVariablePb.GetAnalysisVariablesResponse{
					Status: rpc.StatusOk(),
					AnalysisVariableMap: map[string]*analyserVariablePb.AnalysisVariable{
						analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS.String(): {
							Variable: &analyserVariablePb.AnalysisVariable_MfSecretsSchemeAnalytics{
								MfSecretsSchemeAnalytics: &mfAnalyserVariablePb.MfSchemeAnalytics{
									SchemeAnalytics: []*mfAnalyserVariablePb.SchemeAnalytics{
										{
											EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
												Analytics: &investmentModelPb.MfScheme{
													MutualFundId: "mf1",
													SchemeDetails: &investmentModelPb.MfSchemeDetails{
														Units:    10,
														NavValue: &moneyPb.Money{Units: 12, CurrencyCode: "INR"},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}
				mockVarGen.EXPECT().GetAnalysisVariables(gomock.Any(), gomock.Any()).Return(analysisResp, nil)
			},
			want: []*mfAnalyserVariablePb.SchemeAnalytics{
				{
					EnrichedAnalytics: &investmentServicePb.EnrichedMfSchemeAnalytics{
						Analytics: &investmentModelPb.MfScheme{
							MutualFundId: "mf1",
							SchemeDetails: &investmentModelPb.MfSchemeDetails{
								Units:    10,
								NavValue: &moneyPb.Money{Units: 12, CurrencyCode: "INR"},
							},
						},
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "error from variableGeneratorClient",
			args: args{
				ctx:     context.Background(),
				actorId: "actor1",
			},
			setupMocks: func(mockVarGen *varGenMocks.MockVariableGeneratorClient) {
				analysisResp := &analyserVariablePb.GetAnalysisVariablesResponse{
					Status: rpc.StatusInternal(),
				}
				mockVarGen.EXPECT().GetAnalysisVariables(gomock.Any(), gomock.Any()).Return(analysisResp, nil)
			},
			want:    nil,
			wantErr: &rpc.StatusError{Status: rpc.StatusInternal()},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockVarGen := varGenMocks.NewMockVariableGeneratorClient(ctrl)
			if tt.setupMocks != nil {
				tt.setupMocks(mockVarGen)
			}
			calc := &MutualFundCalculator{variableGeneratorClient: mockVarGen}
			got, err := calc.getMfSchemeAnalyticsDetails(tt.args.ctx, tt.args.actorId)
			var statusErr *rpc.StatusError
			if tt.wantErr != nil && !errors.As(err, &statusErr) {
				t.Errorf("error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.want == nil && got == nil {
				// Both are nil, OK
			} else {
				diff := cmp.Diff(got, tt.want, protocmp.Transform())
				if diff != "" {
					t.Errorf("unexpected result (-got +want):\n%s", diff)
				}
			}
		})
	}
}

func TestMutualFundCalculator_getInitialDateNavs(t *testing.T) {
	type args struct {
		ctx         context.Context
		mfIds       []string
		initialDate *timestamppb.Timestamp
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockCatalog *catalogMocks.MockCatalogManagerClient)
		want       map[string]*moneyPb.Money
		wantErr    error
	}{
		{
			name: "happy path",
			args: args{
				ctx:         context.Background(),
				mfIds:       []string{"mf1"},
				initialDate: timestamppb.New(time.Date(2024, 6, 1, 0, 0, 0, 0, time.UTC)),
			},
			setupMocks: func(mockCatalog *catalogMocks.MockCatalogManagerClient) {
				navResp := &catalogPb.GetNavHistoryByMutualFundIdResponse{
					Status: rpc.StatusOk(),
					NavHistories: []*catalogPb.NavHistory{
						{
							MutualFundId: "mf1",
							NavDetails: []*catalogPb.NavDetails{
								{
									Nav: &moneyPb.Money{Units: 10, CurrencyCode: "INR"},
								},
							},
						},
					},
				}
				mockCatalog.EXPECT().GetNavHistoryByMutualFundId(gomock.Any(), gomock.Any()).Return(navResp, nil)
			},
			want: map[string]*moneyPb.Money{
				"mf1": {Units: 10, CurrencyCode: "INR"},
			},
			wantErr: nil,
		},
		{
			name: "error from catalog client",
			args: args{
				ctx:         context.Background(),
				mfIds:       []string{"mf1"},
				initialDate: timestamppb.New(time.Date(2024, 6, 1, 0, 0, 0, 0, time.UTC)),
			},
			setupMocks: func(mockCatalog *catalogMocks.MockCatalogManagerClient) {
				resp := &catalogPb.GetNavHistoryByMutualFundIdResponse{
					Status: rpc.StatusInternal(),
				}
				mockCatalog.EXPECT().GetNavHistoryByMutualFundId(gomock.Any(), gomock.Any()).Return(resp, nil)
			},
			want:    nil,
			wantErr: &rpc.StatusError{Status: rpc.StatusInternal()},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockCatalog := catalogMocks.NewMockCatalogManagerClient(ctrl)
			if tt.setupMocks != nil {
				tt.setupMocks(mockCatalog)
			}
			calc := &MutualFundCalculator{catalogClient: mockCatalog}
			got, err := calc.getInitialDateNavs(tt.args.ctx, tt.args.mfIds, tt.args.initialDate)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.want == nil && got == nil {
				// Both are nil, OK
			} else {
				diff := cmp.Diff(got, tt.want, protocmp.Transform())
				if diff != "" {
					t.Errorf("unexpected result (-got +want):\n%s", diff)
				}
			}
		})
	}
}
