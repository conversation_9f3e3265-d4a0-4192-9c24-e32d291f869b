Application:
  Environment: "prod"
  Name: "tiering"

Server:
  Ports:
    GrpcPort: 8092
    GrpcSecurePort: 9501
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  AppName: "tiering"
  StatementTimeout: 10s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

TieringDb:
  AppName: "tiering"
  StatementTimeout: 10s
  Name: "tiering"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: true

Tracing:
  Enable: true

RudderStack:
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"
    TieringDbCreds: "prod/rds/epifimetis/tiering_dev_user"
    StartreePinotAuthToken: "prod/pinot/startree"

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

TierReEvaluationEventSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-tiering-re-evaluation-queue"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 3
      MaxAttempts: 12
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "account-tiering"

CooloffParams:
  MaxMovementsAllowed: 10
  EvaluationWindowDuration: 8766h # 1 year

GraceParams:
  ResetDuration: 720h # 30 days
  Ladder: [720h, 360h, 168h, 72h] # [30 days, 15 days, 7 days, 3 days]
  SilentGracePeriod: 72h # 3 days
  GoodUserSilentGracePeriod: 720h # 30 days

DisplayPropertyKeyExpiryDuration: 24h # 1 day

DaoCacheRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
    Password: "" ## empty string for no password
  AuthDetails:
    SecretPath: "prod/redis/centralgrowth/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: tiering
  HystrixCommand:
    CommandName: "tiering_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 1500
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 30

DisplayPropertiesRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
    Password: "" ## empty string for no password
  AuthDetails:
    SecretPath: "prod/redis/centralgrowth/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: tiering

DisplayComponentTTLMap:
  - "DISPLAY_COMPONENT_TIER_INTRODUCTION_SCREEN": 4380h # 6 months

FeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_REGULAR_TIER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 399
          MinIOSVersion: 554
        StickyPercentageConstraintConfig:
          RolloutPercentage: 30
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - FEATURE_TIERING_MULTIPLE_WAYS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 421
          MinIOSVersion: 584
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100

TieringFeatureRelease:
  IsFeatureEnabled: true
  RolloutPercentage: 100
  AllowedGroups:
    - 1 # INTERNAL
    - 2 # FNF
    - 19 # TIERING

ProcessKycUpdateEventSubscriber:
  StartOnServerStart: false
  Disable: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-tiering-process-kyc-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessSalaryUpdateEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-tiering-process-salary-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessBalanceUpdateEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-tiering-balance-update-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessAddFundsOrderEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-tiering-order-add-funds-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessInvestmentEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-tiering-process-investment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

ProcessUsStocksWalletOrderEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-tiering-process-usstocks-wallet-order-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

AutoUpgradeCutoff: 8766h # 1 year

NotificationConfigMap:
  - NOTIFICATION_TYPE_UPGRADE:
      IsEnabled: true
      ExpireAfter: 168h # 7 days
      AutoDismissOnHomeAfterInSeconds: 60
  - NOTIFICATION_TYPE_DOWNGRADE:
      IsEnabled: true
      ExpireAfter: 720h # 30 days
      AutoDismissOnHomeAfterInSeconds: 60
  - NOTIFICATION_TYPE_GRACE:
      IsEnabled: true
      AutoDismissOnHomeAfterInSeconds: 60

EnableGoodUserSilentGraceSegmentation: true
EnableRewardsAbuserSegmentation: false

SegmentIds:
  GoodUser: "5d7bab8a-3ab0-4aa7-87e9-6eef32f7675b"
  RewardsAbuser: "AWS_d4323ba4857b43e5a5173e07986c1a31"
  HigherTiersCashbackRewardInEligibleUser: "8ce993d2-e5f2-40f4-8d56-7a5f006cb89d"

TierToIneligibleActorSegmentIdMap:
  - TIER_FI_PLUS: "4e1fa4c8-11b2-4830-81de-0114cfddf6c6"
  - TIER_FI_INFINITE: "21ffa50d-8a9b-4df4-a2a3-5fa788093174"
  - TIER_FI_AA_SALARY_BAND_3: "a9b3463c-5de0-4bfe-99c3-d2e8b11f1c71"

SilentGraceTierSegmentsMap:
  - TIER_ONE_HUNDRED: "5d7bab8a-3ab0-4aa7-87e9-6eef32f7675b"
  - TIER_ONE_THOUSAND: "5d7bab8a-3ab0-4aa7-87e9-6eef32f7675b"

ActorTierInfoCacheConfig:
  IsCachingEnabled: true
  Prefix: "TIERING:ATI:"
  CacheTTl: "1h"

EligibleTierMovementCacheConfig:
  IsCachingEnabled: true
  Prefix: "TIERING:ETM:"
  CacheTTl: "1h"

TierMovementHistoryCacheConfig:
  IsCachingEnabled: true
  Prefix: "TIERING:TMH:"
  CacheTTl: "1h"

MinAndroidVersion: 229
MinIosVersion: 331

TierUpdateEventExternalPublisher:
  TopicName: "prod-tiering-tier-update-topic"

EnableHomeTopBarBanner: true
EnablePromoWidget: true

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-12231.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12231"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/growth-infra/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: tiering-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 10000
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 50
      SleepWindow: 15s
      FallbackMaxConcurrency: 10000

ProcessBalanceUpdateEventMarketingSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-tiering-balance-update-marketing-events-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "account-tiering"

TierEODBalanceServiceConfigMap:
  - TIER_TEN:
      Enable: false
  - TIER_ONE_HUNDRED:
      Enable: true
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 2000
  - TIER_ONE_THOUSAND:
      Enable: true
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 10000
  - TIER_ONE_THOUSAND_FIVE_HUNDRED:
      Enable: false
  - TIER_TWO_THOUSAND:
      Enable: false
  - TIER_ONE_THOUSAND_TWO_NINETY:
      Enable: true
      EODBalanceAggregateDuration: 1080h # 45 days
      MinAggregationDays: 30
      AverageBalanceThreshold: 80000

AutoUpgradeConfig:
  DisableUserSpecificChecks: false
  DisableHigherTierCheck: true
  DisableAverageBalanceCheck: false
  AvgBalanceDurationBuffer: 24h
  DisableBeenInTieringCheck: true
  DisableToTierConditionMap:
    Standard: true
    Plus: true
    Infinite: true
    SalaryLite: true
    Salary: true
    AaSalary: true
  AutoUpgradeAvgBalanceConditionMap:
    - TIER_ONE_HUNDRED:
        AverageBalanceConditionMap:
          #          - "168h": 5000 # 7 days
          - "720h": 2000 # 30 days
    - TIER_ONE_THOUSAND:
        AverageBalanceConditionMap:
          - "720h": 25000 # 30 days
    - TIER_ONE_THOUSAND_TWO_NINETY:
        AverageBalanceConditionMap:
          - "720h": 60000 # 30 days

EnableTierDowngrades: true

IsReactivationPromoWidgetEnabled: false

SegmentIdsForReactivationPromoWidget_3txn:

SegmentIdsForReactivationPromoWidget_5txn:

RegularTierLaunchDate: "2030-10-15T00:00:00+05:30"

MinAvgMonthlyBalanceForRegularTier: 5000
MinBalancePenaltyForRegularTier: 200
ShouldRoundGraceToNextDay: false

TieringTrialConfig:
  StartTimeUnix: 1748944683
  EndTimeUnix: 1750357800
  SegmentIds:
    - "0c161ab4-0583-4496-a0c1-91ad0e4f89b1"
    - "9cd5e2fb-fe04-4352-a880-076d096c67cc"
    - "6d6180f3-2716-4f5c-83da-06998f8c1c16"
    - "86a1110f-551c-450a-be1f-7019beb1175d"
  AllowedProvenancesList:
    - "PROVENANCE_AUTOMATIC"

CriteriaSegmentExclusions:
  SalaryB2CExcludedSegments:
    - "97805efc-2faf-47ea-9d98-737120fd2812"
  AaSalaryExcludedSegments:
    - "7812d013-f14e-482f-9802-e9459a87b996"
