package salaryestimation

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	caPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/analytics"
	caDataAnalytics "github.com/epifi/gamma/api/connected_account/data_analytics"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/datasharing"
	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/salaryestimation"
	datasharingTypes "github.com/epifi/gamma/api/typesv2/datasharing"
	salaryEstimationScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryestimation"
	salaryEstimationTypes "github.com/epifi/gamma/api/typesv2/salaryestimation"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	genConf "github.com/epifi/gamma/salaryestimation/config/genconf"
)

type Service struct {
	conf                   *genConf.Config
	caAnalyticsClient      analytics.AnalyticsClient
	caDataAnalyticsClient  caDataAnalytics.DataAnalyticsClient
	consentClient          consent.ConsentClient
	dataSharingClient      datasharing.DataSharingClient
	connectedAccountClient caPb.ConnectedAccountClient
	preApprovedLoanClient  palPb.PreApprovedLoanClient
	eventBroker            events.Broker
}

func NewService(
	conf *genConf.Config,
	caAnalyticsClient analytics.AnalyticsClient,
	caDataAnalyticsClient caDataAnalytics.DataAnalyticsClient,
	consentClient consent.ConsentClient,
	dataSharingClient datasharing.DataSharingClient,
	connectedAccountClient caPb.ConnectedAccountClient,
	preApprovedLoanClient palPb.PreApprovedLoanClient,
	eventBroker events.Broker,
) *Service {
	return &Service{
		conf:                   conf,
		caAnalyticsClient:      caAnalyticsClient,
		caDataAnalyticsClient:  caDataAnalyticsClient,
		consentClient:          consentClient,
		dataSharingClient:      dataSharingClient,
		connectedAccountClient: connectedAccountClient,
		preApprovedLoanClient:  preApprovedLoanClient,
		eventBroker:            eventBroker,
	}
}

func (s *Service) GetSalary(ctx context.Context, req *salaryestimation.GetSalaryRequest) (*salaryestimation.GetSalaryResponse, error) {
	res, err := s.caDataAnalyticsClient.GetAnalysisByActor(ctx, &caDataAnalytics.GetAnalysisByActorRequest{ActorId: req.GetActorId()})
	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "error getting analysis by actor", zap.Error(err))
		if res.GetStatus().IsRecordNotFound() {
			return &salaryestimation.GetSalaryResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		return &salaryestimation.GetSalaryResponse{Status: rpc.StatusInternal()}, nil
	}
	var salaryAccountId string
	for accountId, accountAnalysis := range res.GetAnalysis().GetAccountAnalyses() {
		if accountAnalysis.GetStatistics().GetIsSalaryAccount().ToBool() {
			salaryAccountId = accountId
			break
		}
	}
	return &salaryestimation.GetSalaryResponse{
		Status: rpc.StatusOk(),
		Salary: &salaryestimation.Salary{
			Source:        salaryEstimationTypes.Source_SOURCE_AA,
			SalaryAccount: &salaryestimation.SalaryAccount{AccountId: salaryAccountId},
			ComputedAt:    res.GetAnalysis().GetL2AnalysisCompletedAt(),
		},
		L1AnalysisSignedUrl: res.GetL1AnalysisSignedUrl(),
	}, nil
}

func (s *Service) ComputeSalary(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*salaryestimation.ComputeSalaryResponse, error) {
	switch req.GetSource() {
	case salaryEstimationTypes.Source_SOURCE_AA:
		nextAction, err := s.computeSalaryViaAa(ctx, req)
		if err != nil {
			logger.Error(ctx, "error computing salary for AA flow", zap.Error(err))
			return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
		}
		logger.Info(ctx, "next action data of computed salary for AA flow", zap.String(logger.SCREEN, nextAction.GetScreen().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Any("aa_data_flow_params", req.GetSourceFlowParams().GetAaDataFlowParams()))
		return &salaryestimation.ComputeSalaryResponse{
			Status:     rpc.StatusOk(),
			NextAction: nextAction,
		}, nil
	default:
		logger.Error(ctx, "no source specified, screen for choosing source is not yet supported")
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInvalidArgument()}, nil
	}
}

func (s *Service) computeSalaryViaAa(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	if req.GetRequireHoldingScreen() {
		return getHoldingScreenForAAAnalysis(req.GetClient(), req.GetClientReqId()), nil
	}
	err := s.initiateAnalysis(ctx, req.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error initiating analysis")
	}
	logger.Info(ctx, "checking analysis status from data analytics", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	analysisStatusRes, err := s.caDataAnalyticsClient.GetAnalysisStatus(ctx, &caDataAnalytics.GetAnalysisStatusRequest{
		ActorId:     req.GetActorId(),
		ClientReqId: req.GetClientReqId(),
	})
	if err = epifigrpc.RPCError(analysisStatusRes, err); err != nil {
		if analysisStatusRes.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "analysis not found, getting next action for customer", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
			nextAction, computeErr := s.computeSalaryForFlowParams(ctx, req)
			if computeErr != nil {
				return nil, errors.Wrap(computeErr, "error computing salary for flow params")
			}
			logger.Info(ctx, "next action for customer", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.SCREEN, nextAction.GetScreen().String()))
			return nextAction, nil
		}
		logger.Info(ctx, "error getting analysis status", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return nil, errors.Wrap(err, "error getting analysis status")
	}
	logger.Info(ctx, fmt.Sprintf("got analysis status: %v from data analytics", analysisStatusRes.GetAnalysisStatus().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	analysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(analysisStatusRes.GetAnalysisStatus())
	logger.Info(ctx, fmt.Sprintf("salary estimation analysis status: %v", analysisStatus.String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	nextAction, nextActionErr := s.getLatestAnalysisStatusScreen(ctx, req, req.GetClientReqId(), analysisStatus)
	if nextActionErr != nil {
		return nil, fmt.Errorf("error getting latest analysis status screen: %w", nextActionErr)
	}
	logger.Info(ctx, "next action for customer", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.SCREEN, nextAction.GetScreen().String()))
	return nextAction, nil
}

func (s *Service) computeSalaryForFlowParams(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	aaDataFlowParams := req.GetSourceFlowParams().GetAaDataFlowParams()
	logger.Info(ctx, fmt.Sprintf("getting next action for aa data flow params: %v", aaDataFlowParams.GetInputType().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	switch aaDataFlowParams.GetInputType() {
	default:
		logger.Info(ctx, "no input type specified, assuming fresh visit to flow")
		return s.getNextActionForFreshVisit(ctx, req)
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_DATA_SHARE:
		return s.getNextActionForDataShareInput(ctx, req)
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_DATA_STATUS:
		return s.getNextActionForDataStatusInput(ctx, req)
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_ANALYSIS:
		return s.getNextActionForAnalysisInput(ctx, req)
	}
}

func (s *Service) initiateAnalysis(ctx context.Context, actorId string) error {
	crId := uuid.NewString()
	analysisInitRes, err := s.caAnalyticsClient.InitiateAnalysis(ctx, &analytics.InitiateAnalysisRequest{
		ActorId:     actorId,
		ClientReqId: crId,
		Client:      common.Owner_OWNER_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(analysisInitRes, err); err != nil {
		if analysisInitRes.GetStatus().IsAlreadyExists() ||
			analysisInitRes.GetStatus().GetCode() == uint32(analytics.InitiateAnalysisResponse_ANOTHER_ANALYSIS_FOR_ACTOR_ALREADY_IN_PROGRESS) ||
			analysisInitRes.GetStatus().GetCode() == uint32(analytics.InitiateAnalysisResponse_NO_NEW_DATA_FOR_ANALYSIS_FOR_ANY_ACCOUNT_FOR_ACTOR) ||
			analysisInitRes.GetStatus().GetCode() == uint32(analytics.InitiateAnalysisResponse_AA_DATA_PULL_IN_PROGRESS_FOR_ACTOR) {
			logger.Info(ctx, fmt.Sprintf("fire and forget initiate analysis custom handling, status code: %d, msg: %s",
				analysisInitRes.GetStatus().GetCode(), analysisInitRes.GetStatus().GetShortMessage()), zap.String(logger.CLIENT_REQUEST_ID, crId))
			return nil
		}
		return errors.Wrapf(err, "error initiating analysis for actor_id: %s, client_req_id: %s", actorId, crId)
	}
	logger.Info(ctx, "New analysis initiated with new client req id", zap.String(logger.CLIENT_REQUEST_ID, analysisInitRes.GetClientReqId()))
	return nil
}

func (s *Service) getNextActionForDataShareInput(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	dataSharingConsentId, err := s.getConsentIdForDataSharing(ctx, req.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting consent id for data sharing")
	}
	dataShareParams := req.GetSourceFlowParams().GetAaDataFlowParams().GetDataShareParams()
	initDataShareRes, err := s.dataSharingClient.InitiateDataSharing(ctx, &datasharing.InitiateDataSharingRequest{
		Client:      datasharingTypes.Client_CLIENT_SALARY_ESTIMATION,
		ClientReqId: req.GetClientReqId(),
		ActorId:     req.GetActorId(),
		DataType:    datasharingTypes.DataType_DATA_TYPE_AA_ACCOUNTS,
		DataRequestParams: &datasharingTypes.DataRequestParams{
			ReqParams: &datasharingTypes.DataRequestParams_AaAccountsDataQueryParams{
				AaAccountsDataQueryParams: &datasharingTypes.AaAccountsDataQueryParams{
					AccountIds:          dataShareParams.GetAaAccountIds(),
					OldestTransactionTs: timestamppb.New(s.conf.OldestAATransactionUpdatedAt()),
					LatestTransactionTs: timestamppb.Now(),
					ConsentId:           dataSharingConsentId,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(initDataShareRes, err); err != nil {
		return nil, errors.Wrap(err, "error initiating data sharing")
	}
	return initDataShareRes.GetNextActionDeeplink(), nil
}

func (s *Service) getConsentIdForDataSharing(ctx context.Context, actorId string) (string, error) {
	perpetualConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ActorId:     actorId,
		ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
		Owner:       common.Owner_OWNER_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(perpetualConsentRes, err); err != nil && !perpetualConsentRes.GetStatus().IsRecordNotFound() {
		return "", fmt.Errorf("error fetching perpetual consent: %w", err)
	}
	if perpetualConsentRes.GetConsentId() != "" {
		return perpetualConsentRes.GetConsentId(), nil
	}
	latestOneTimeDataSharingConsent, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
		ActorId:     actorId,
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(latestOneTimeDataSharingConsent, err); err != nil && !latestOneTimeDataSharingConsent.GetStatus().IsRecordNotFound() {
		return "", errors.Wrap(err, "error fetching latest one time data sharing consent")
	}
	if time.Now().After(latestOneTimeDataSharingConsent.GetExpiresAt().AsTime()) {
		return "", errors.Errorf("latest one time data sharing consent expired at: %s", latestOneTimeDataSharingConsent.GetExpiresAt().AsTime())
	}
	return latestOneTimeDataSharingConsent.GetConsentId(), nil
}

func (s *Service) getNextActionForDataStatusInput(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	dataSharingRecord := req.GetSourceFlowParams().GetAaDataFlowParams().GetDataStatusParams().GetDataSharingRecord()
	analysisAttemptClientReqId := dataSharingRecord.GetClientRequestId()
	var files []*analytics.File
	for _, sharedFile := range dataSharingRecord.GetData().GetAaAccountsData().GetFiles() {
		file := &analytics.File{
			FilePath: sharedFile.GetUrl(),
			FileMetadata: &analytics.FileMetadata{
				AccountsMetadata: []*analytics.AccountMetadata{
					{
						AccountId:           sharedFile.GetMetadata().GetAaAccountFileMetadata().GetAccountId(),
						OldestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetOldestTransactionTs(),
						LatestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetLatestTransactionTs(),
					},
				},
			},
		}
		files = append(files, file)
	}
	res, err := s.caDataAnalyticsClient.InitiateAnalysis(ctx, &caDataAnalytics.InitiateAnalysisRequest{
		ClientReqId: analysisAttemptClientReqId,
		ActorId:     dataSharingRecord.GetActorId(),
		RequestParams: &caDataAnalytics.AnalysisRequestParams{
			DataExchangeRecord: &analytics.DataExchangeRecord{
				ClientReqId: dataSharingRecord.GetClientRequestId(),
				ActorId:     dataSharingRecord.GetActorId(),
				Owner:       dataSharingRecord.GetClientOwnership(),
				Data: &analytics.Data{
					Files: files,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(res, err); err != nil && !res.GetStatus().IsAlreadyExists() {
		return nil, fmt.Errorf("error initiating analysis: %w", err)
	}
	analysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(res.GetAnalysisStatus())
	return s.getLatestAnalysisStatusScreen(ctx, req, analysisAttemptClientReqId, analysisStatus)
}

func getSalaryEstAnalysisStatusFromCaAnalysisStatus(caAnalysisStatus caDataAnalytics.AnalysisStatus) salaryEstimationTypes.AnalysisStatus {
	switch caAnalysisStatus {
	default:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_UNSPECIFIED
	case caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_CREATED,
		caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_INITIATED,
		caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_ANALYSIS_PENDING:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS
	case caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL
	case caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_FAILED:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED
	}
}

func getHoldingScreenForAAAnalysis(client salaryestimation.Client, clientReqId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_VERIFY_INCOME_HOME_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.VerifyIncomeHomeScreenOptions{
			Client:      client.String(),
			ClientReqId: clientReqId,
			Source:      salaryEstimationTypes.Source_SOURCE_AA.String(),
		}),
	}
}
