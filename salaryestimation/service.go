package salaryestimation

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	caPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/analytics"
	caDataAnalytics "github.com/epifi/gamma/api/connected_account/data_analytics"
	"github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/datasharing"
	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/salaryestimation"
	datasharingTypes "github.com/epifi/gamma/api/typesv2/datasharing"
	caScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/connectedaccount"
	salaryEstimationScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryestimation"
	salaryEstimationTypes "github.com/epifi/gamma/api/typesv2/salaryestimation"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	salaryestimationPkg "github.com/epifi/gamma/pkg/salaryestimation"
	genConf "github.com/epifi/gamma/salaryestimation/config/genconf"
	seEvents "github.com/epifi/gamma/salaryestimation/events"
)

type Service struct {
	conf                       *genConf.Config
	caAnalyticsClient          analytics.AnalyticsClient
	caDataAnalyticsClient      caDataAnalytics.DataAnalyticsClient
	consentClient              consent.ConsentClient
	dataSharingClient          datasharing.DataSharingClient
	connectedAccountClient     caPb.ConnectedAccountClient
	preApprovedLoanClient      palPb.PreApprovedLoanClient
	eventBroker                events.Broker
	salaryEstimationAttemptDao SalaryEstimationAttemptDao
}

func NewService(
	conf *genConf.Config,
	caAnalyticsClient analytics.AnalyticsClient,
	caDataAnalyticsClient caDataAnalytics.DataAnalyticsClient,
	consentClient consent.ConsentClient,
	dataSharingClient datasharing.DataSharingClient,
	connectedAccountClient caPb.ConnectedAccountClient,
	preApprovedLoanClient palPb.PreApprovedLoanClient,
	eventBroker events.Broker,
	salaryEstimationAttemptDao SalaryEstimationAttemptDao,
) *Service {
	return &Service{
		conf:                       conf,
		caAnalyticsClient:          caAnalyticsClient,
		caDataAnalyticsClient:      caDataAnalyticsClient,
		consentClient:              consentClient,
		dataSharingClient:          dataSharingClient,
		connectedAccountClient:     connectedAccountClient,
		preApprovedLoanClient:      preApprovedLoanClient,
		eventBroker:                eventBroker,
		salaryEstimationAttemptDao: salaryEstimationAttemptDao,
	}
}

func (s *Service) GetSalary(ctx context.Context, req *salaryestimation.GetSalaryRequest) (*salaryestimation.GetSalaryResponse, error) {
	res, err := s.caDataAnalyticsClient.GetAnalysisByActor(ctx, &caDataAnalytics.GetAnalysisByActorRequest{ActorId: req.GetActorId()})
	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "error getting analysis by actor", zap.Error(err))
		if res.GetStatus().IsRecordNotFound() {
			return &salaryestimation.GetSalaryResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		return &salaryestimation.GetSalaryResponse{Status: rpc.StatusInternal()}, nil
	}
	var salaryAccountId string
	for accountId, accountAnalysis := range res.GetAnalysis().GetAccountAnalyses() {
		if accountAnalysis.GetStatistics().GetIsSalaryAccount().ToBool() {
			salaryAccountId = accountId
			break
		}
	}
	return &salaryestimation.GetSalaryResponse{
		Status: rpc.StatusOk(),
		Salary: &salaryestimation.Salary{
			Source:        salaryEstimationTypes.Source_SOURCE_AA,
			SalaryAccount: &salaryestimation.SalaryAccount{AccountId: salaryAccountId},
			ComputedAt:    res.GetAnalysis().GetL2AnalysisCompletedAt(),
		},
		L1AnalysisSignedUrl: res.GetL1AnalysisSignedUrl(),
	}, nil
}

func (s *Service) ComputeSalary(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*salaryestimation.ComputeSalaryResponse, error) {
	// Check if stateful salary estimation is enabled
	if s.conf.Flags().EnableStatefulSalaryEstimation() {
		logger.Info(ctx, "stateful salary estimation enabled, checking for existing attempt", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return s.computeSalaryWithStatefulLogic(ctx, req)
	}

	// Fallback to original logic if flag is disabled
	switch req.GetSource() {
	case salaryEstimationTypes.Source_SOURCE_AA:
		nextAction, err := s.computeSalaryViaAa(ctx, req)
		if err != nil {
			logger.Error(ctx, "error computing salary for AA flow", zap.Error(err))
			return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
		}
		logger.Info(ctx, "next action data of computed salary for AA flow", zap.String(logger.SCREEN, nextAction.GetScreen().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Any("aa_data_flow_params", req.GetSourceFlowParams().GetAaDataFlowParams()))
		return &salaryestimation.ComputeSalaryResponse{
			Status:     rpc.StatusOk(),
			NextAction: nextAction,
		}, nil
	default:
		logger.Error(ctx, "no source specified, screen for choosing source is not yet supported")
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInvalidArgument()}, nil
	}
}

func (s *Service) computeSalaryWithStatefulLogic(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*salaryestimation.ComputeSalaryResponse, error) {
	existingAttempt, err := s.salaryEstimationAttemptDao.GetByClientReqID(ctx, req.GetClientReqId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error getting existing attempt", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}
	if existingAttempt != nil {
		// Existing attempt found, handle based on status
		logger.Info(ctx, "existing attempt found", zap.String("attempt_id", existingAttempt.GetId()), zap.String("status", existingAttempt.GetStatus().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return s.handleExistingAttempt(ctx, req, existingAttempt)
	}
	logger.Info(ctx, "no existing attempt found, creating new attempt", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	nextAction, err := s.createNewAttemptAndProceed(ctx, req)
	if err != nil {
		logger.Error(ctx, "error creating new attempt and proceeding", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}
	return &salaryestimation.ComputeSalaryResponse{
		Status:     rpc.StatusOk(),
		NextAction: nextAction,
	}, nil
}

func (s *Service) computeSalaryViaAa(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	if req.GetRequireHoldingScreen() {
		return getHoldingScreenForAAAnalysis(req.GetClient(), req.GetClientReqId()), nil
	}
	err := s.initiateWealthAnalysis(ctx, req.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error initiating analysis")
	}
	logger.Info(ctx, "checking analysis status from data analytics", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	analysisStatusRes, err := s.caDataAnalyticsClient.GetAnalysisStatus(ctx, &caDataAnalytics.GetAnalysisStatusRequest{
		ActorId:     req.GetActorId(),
		ClientReqId: req.GetClientReqId(),
	})
	if err = epifigrpc.RPCError(analysisStatusRes, err); err != nil {
		if analysisStatusRes.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "analysis not found, getting next action for customer", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
			nextAction, computeErr := s.computeSalaryForFlowParams(ctx, req)
			if computeErr != nil {
				return nil, errors.Wrap(computeErr, "error computing salary for flow params")
			}
			logger.Info(ctx, "next action for customer", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.SCREEN, nextAction.GetScreen().String()))
			return nextAction, nil
		}
		logger.Info(ctx, "error getting analysis status", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return nil, errors.Wrap(err, "error getting analysis status")
	}
	logger.Info(ctx, fmt.Sprintf("got analysis status: %v from data analytics", analysisStatusRes.GetAnalysisStatus().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	analysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(analysisStatusRes.GetAnalysisStatus())
	logger.Info(ctx, fmt.Sprintf("salary estimation analysis status: %v", analysisStatus.String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	nextAction, nextActionErr := s.getLatestAnalysisStatusScreen(ctx, req, req.GetClientReqId(), analysisStatus)
	if nextActionErr != nil {
		return nil, fmt.Errorf("error getting latest analysis status screen: %w", nextActionErr)
	}
	logger.Info(ctx, "next action for customer", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.SCREEN, nextAction.GetScreen().String()))
	return nextAction, nil
}

func (s *Service) computeSalaryForFlowParams(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	aaDataFlowParams := req.GetSourceFlowParams().GetAaDataFlowParams()
	logger.Info(ctx, fmt.Sprintf("getting next action for aa data flow params: %v", aaDataFlowParams.GetInputType().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	switch aaDataFlowParams.GetInputType() {
	default:
		logger.Info(ctx, "no input type specified, assuming fresh visit to flow")
		return s.getNextActionForFreshVisit(ctx, req)
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_DATA_SHARE:
		return s.getNextActionForDataShareInput(ctx, req)
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_DATA_STATUS:
		return s.getNextActionForDataStatusInput(ctx, req)
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_ANALYSIS:
		return s.getNextActionForAnalysisInput(ctx, req)
	}
}

func (s *Service) initiateWealthAnalysis(ctx context.Context, actorId string) error {
	crId := uuid.NewString()
	analysisInitRes, err := s.caAnalyticsClient.InitiateAnalysis(ctx, &analytics.InitiateAnalysisRequest{
		ActorId:     actorId,
		ClientReqId: crId,
		Client:      common.Owner_OWNER_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(analysisInitRes, err); err != nil {
		if analysisInitRes.GetStatus().IsAlreadyExists() ||
			analysisInitRes.GetStatus().GetCode() == uint32(analytics.InitiateAnalysisResponse_ANOTHER_ANALYSIS_FOR_ACTOR_ALREADY_IN_PROGRESS) ||
			analysisInitRes.GetStatus().GetCode() == uint32(analytics.InitiateAnalysisResponse_NO_NEW_DATA_FOR_ANALYSIS_FOR_ANY_ACCOUNT_FOR_ACTOR) ||
			analysisInitRes.GetStatus().GetCode() == uint32(analytics.InitiateAnalysisResponse_AA_DATA_PULL_IN_PROGRESS_FOR_ACTOR) {
			logger.Info(ctx, fmt.Sprintf("fire and forget initiate analysis custom handling, status code: %d, msg: %s",
				analysisInitRes.GetStatus().GetCode(), analysisInitRes.GetStatus().GetShortMessage()), zap.String(logger.CLIENT_REQUEST_ID, crId))
			return nil
		}
		return errors.Wrapf(err, "error initiating analysis for actor_id: %s, client_req_id: %s", actorId, crId)
	}
	logger.Info(ctx, "New analysis initiated with new client req id", zap.String(logger.CLIENT_REQUEST_ID, analysisInitRes.GetClientReqId()))
	return nil
}

func (s *Service) getNextActionForDataShareInput(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	dataSharingConsentId, err := s.getConsentIdForDataSharing(ctx, req.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting consent id for data sharing")
	}
	dataShareParams := req.GetSourceFlowParams().GetAaDataFlowParams().GetDataShareParams()
	initDataShareRes, err := s.dataSharingClient.InitiateDataSharing(ctx, &datasharing.InitiateDataSharingRequest{
		Client:      datasharingTypes.Client_CLIENT_SALARY_ESTIMATION,
		ClientReqId: req.GetClientReqId(),
		ActorId:     req.GetActorId(),
		DataType:    datasharingTypes.DataType_DATA_TYPE_AA_ACCOUNTS,
		DataRequestParams: &datasharingTypes.DataRequestParams{
			ReqParams: &datasharingTypes.DataRequestParams_AaAccountsDataQueryParams{
				AaAccountsDataQueryParams: &datasharingTypes.AaAccountsDataQueryParams{
					AccountIds:          dataShareParams.GetAaAccountIds(),
					OldestTransactionTs: timestamppb.New(s.conf.OldestAATransactionUpdatedAt()),
					LatestTransactionTs: timestamppb.Now(),
					ConsentId:           dataSharingConsentId,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(initDataShareRes, err); err != nil {
		return nil, errors.Wrap(err, "error initiating data sharing")
	}
	return initDataShareRes.GetNextActionDeeplink(), nil
}

func (s *Service) getConsentIdForDataSharing(ctx context.Context, actorId string) (string, error) {
	perpetualConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ActorId:     actorId,
		ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
		Owner:       common.Owner_OWNER_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(perpetualConsentRes, err); err != nil && !perpetualConsentRes.GetStatus().IsRecordNotFound() {
		return "", fmt.Errorf("error fetching perpetual consent: %w", err)
	}
	if perpetualConsentRes.GetConsentId() != "" {
		return perpetualConsentRes.GetConsentId(), nil
	}
	latestOneTimeDataSharingConsent, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
		ActorId:     actorId,
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(latestOneTimeDataSharingConsent, err); err != nil && !latestOneTimeDataSharingConsent.GetStatus().IsRecordNotFound() {
		return "", errors.Wrap(err, "error fetching latest one time data sharing consent")
	}
	if time.Now().After(latestOneTimeDataSharingConsent.GetExpiresAt().AsTime()) {
		return "", errors.Errorf("latest one time data sharing consent expired at: %s", latestOneTimeDataSharingConsent.GetExpiresAt().AsTime())
	}
	return latestOneTimeDataSharingConsent.GetConsentId(), nil
}

func (s *Service) getNextActionForDataStatusInput(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	dataSharingRecord := req.GetSourceFlowParams().GetAaDataFlowParams().GetDataStatusParams().GetDataSharingRecord()
	analysisAttemptClientReqId := dataSharingRecord.GetClientRequestId()
	var files []*analytics.File
	for _, sharedFile := range dataSharingRecord.GetData().GetAaAccountsData().GetFiles() {
		file := &analytics.File{
			FilePath: sharedFile.GetUrl(),
			FileMetadata: &analytics.FileMetadata{
				AccountsMetadata: []*analytics.AccountMetadata{
					{
						AccountId:           sharedFile.GetMetadata().GetAaAccountFileMetadata().GetAccountId(),
						OldestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetOldestTransactionTs(),
						LatestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetLatestTransactionTs(),
					},
				},
			},
		}
		files = append(files, file)
	}
	res, err := s.caDataAnalyticsClient.InitiateAnalysis(ctx, &caDataAnalytics.InitiateAnalysisRequest{
		ClientReqId: analysisAttemptClientReqId,
		ActorId:     dataSharingRecord.GetActorId(),
		RequestParams: &caDataAnalytics.AnalysisRequestParams{
			DataExchangeRecord: &analytics.DataExchangeRecord{
				ClientReqId: dataSharingRecord.GetClientRequestId(),
				ActorId:     dataSharingRecord.GetActorId(),
				Owner:       dataSharingRecord.GetClientOwnership(),
				Data: &analytics.Data{
					Files: files,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(res, err); err != nil && !res.GetStatus().IsAlreadyExists() {
		return nil, fmt.Errorf("error initiating analysis: %w", err)
	}
	analysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(res.GetAnalysisStatus())
	return s.getLatestAnalysisStatusScreen(ctx, req, analysisAttemptClientReqId, analysisStatus)
}

func getSalaryEstAnalysisStatusFromCaAnalysisStatus(caAnalysisStatus caDataAnalytics.AnalysisStatus) salaryEstimationTypes.AnalysisStatus {
	switch caAnalysisStatus {
	default:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_UNSPECIFIED
	case caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_CREATED,
		caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_INITIATED,
		caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_ANALYSIS_PENDING:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS
	case caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL
	case caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_FAILED:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED
	}
}

func getHoldingScreenForAAAnalysis(client salaryestimation.Client, clientReqId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_VERIFY_INCOME_HOME_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.VerifyIncomeHomeScreenOptions{
			Client:      client.String(),
			ClientReqId: clientReqId,
			Source:      salaryEstimationTypes.Source_SOURCE_AA.String(),
		}),
	}
}

func (s *Service) handleExistingAttempt(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, attempt *salaryestimation.SalaryEstimationAttempt) (*salaryestimation.ComputeSalaryResponse, error) {
	switch attempt.GetStep() {
	case salaryestimation.AttemptStep_ATTEMPT_STEP_SALARY_ESTIMATION:
		// Initiate wealth analysis and process as new attempt
		err := s.initiateWealthAnalysis(ctx, attempt.GetActorId())
		if err != nil {
			logger.Error(ctx, "error initiating wealth analysis for existing attempt", zap.Error(err), zap.String("attempt_id", attempt.GetId()))
			return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
		}
		nextAction, err := s.processNewAttempt(ctx, attempt)
		if err != nil {
			logger.Error(ctx, "error processing new attempt for existing attempt", zap.Error(err), zap.String("attempt_id", attempt.GetId()))
			return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
		}
		return &salaryestimation.ComputeSalaryResponse{
			Status:     rpc.StatusOk(),
			NextAction: nextAction,
		}, nil

	case salaryestimation.AttemptStep_ATTEMPT_STEP_ACCOUNT_CONNECTION:
		// Return account connection screen
		accConnScreen, err := s.getAccountConnectionScreen(ctx, &getAccountConnectionScreenReq{
			attempt:                attempt,
			hasPerpetualConsent:    attempt.GetAttemptInfo().GetPerpetualConsentId() != "",
			hasValidOneTimeConsent: attempt.GetAttemptInfo().GetLatestValidOneTimeConsentId() != "",
		})
		if err != nil {
			logger.Error(ctx, "error getting account connection screen for existing attempt", zap.Error(err), zap.String("attempt_id", attempt.GetId()))
			return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
		}
		return &salaryestimation.ComputeSalaryResponse{
			Status:     rpc.StatusOk(),
			NextAction: accConnScreen,
		}, nil

	case salaryestimation.AttemptStep_ATTEMPT_STEP_DATA_SHARING:
		// Download data, upload data, and handle analysis initiation
		nextAction, err := s.handleDataSharingForExistingAttempt(ctx, attempt)
		if err != nil {
			logger.Error(ctx, "error handling data sharing for existing attempt", zap.Error(err), zap.String("attempt_id", attempt.GetId()))
			return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
		}
		return &salaryestimation.ComputeSalaryResponse{
			Status:     rpc.StatusOk(),
			NextAction: nextAction,
		}, nil

	case salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS:
		if attempt.GetStatus() == salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING {
			// Handle analysis initiation and status for pending analysis
			nextAction, err := s.handleAnalysisForExistingAttempt(ctx, attempt)
			if err != nil {
				logger.Error(ctx, "error handling analysis for existing attempt", zap.Error(err), zap.String("attempt_id", attempt.GetId()))
				return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
			}
			return &salaryestimation.ComputeSalaryResponse{
				Status:     rpc.StatusOk(),
				NextAction: nextAction,
			}, nil
		} else {
			// Get latest analysis status and return deeplink
			nextAction, err := s.getLatestAnalysisStatusForExistingAttempt(ctx, attempt)
			if err != nil {
				logger.Error(ctx, "error getting latest analysis status for existing attempt", zap.Error(err), zap.String("attempt_id", attempt.GetId()))
				return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
			}
			return &salaryestimation.ComputeSalaryResponse{
				Status:     rpc.StatusOk(),
				NextAction: nextAction,
			}, nil
		}

	default:
		logger.Error(ctx, "unhandled attempt step for existing attempt", zap.String("step", attempt.GetStep().String()), zap.String("attempt_id", attempt.GetId()))
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}
}

func (s *Service) handleDataSharingForExistingAttempt(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	// Get consent ID from attempt info
	consentId := attempt.GetAttemptInfo().GetPerpetualConsentId()
	if consentId == "" {
		consentId = attempt.GetAttemptInfo().GetLatestValidOneTimeConsentId()
	}
	if consentId == "" {
		return nil, errors.New("no consent ID found in attempt info")
	}

	// Get account IDs from attempt info
	accountIds := attempt.GetAttemptInfo().GetConnectedAccountIds()
	if len(accountIds) == 0 {
		return nil, errors.New("no connected account IDs found in attempt info")
	}

	// Download data
	downloadDataRes, err := s.dataSharingClient.DownloadData(ctx, &datasharing.DownloadDataRequest{
		Client:          datasharingTypes.Client_CLIENT_SALARY_ESTIMATION,
		ClientOwnership: common.Owner_OWNER_EPIFI_TECH,
		ClientReqId:     attempt.GetClientReqId(),
		ActorId:         attempt.GetActorId(),
		DataType:        datasharingTypes.DataType_DATA_TYPE_AA_ACCOUNTS,
		DataRequestParams: &datasharingTypes.DataRequestParams{
			ReqParams: &datasharingTypes.DataRequestParams_AaAccountsDataQueryParams{
				AaAccountsDataQueryParams: &datasharingTypes.AaAccountsDataQueryParams{
					AccountIds:          accountIds,
					OldestTransactionTs: timestamppb.New(s.getFiDataRangeFromForSalaryEstimation()),
					LatestTransactionTs: timestamppb.Now(),
					ConsentId:           consentId,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(downloadDataRes, err); err != nil {
		return nil, errors.Wrap(err, "error downloading data")
	}

	// Upload data
	uploadDataRes, err := s.dataSharingClient.UploadData(ctx, &datasharing.UploadDataRequest{
		DataSharingRecord: downloadDataRes.GetDataSharingRecord(),
	})
	if err = epifigrpc.RPCError(uploadDataRes, err); err != nil {
		return nil, errors.Wrap(err, "error uploading data")
	}

	// Update attempt with data sharing client request ID
	err = s.updateAttemptStepStatusAndInfo(ctx, attempt.GetId(),
		salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS,
		salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING,
		&salaryestimation.AttemptInfo{
			ConnectedAccountIds:         accountIds,
			PerpetualConsentId:          attempt.GetAttemptInfo().GetPerpetualConsentId(),
			LatestValidOneTimeConsentId: attempt.GetAttemptInfo().GetLatestValidOneTimeConsentId(),
			DataSharingClientReqId:      downloadDataRes.GetDataSharingRecord().GetClientRequestId(),
		})
	if err != nil {
		return nil, errors.Wrap(err, "failed to update attempt with data sharing info")
	}

	// Handle analysis initiation and status
	return s.handleAnalysisInitiationAndStatus(ctx, attempt, downloadDataRes.GetDataSharingRecord())
}

func (s *Service) handleAnalysisForExistingAttempt(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	// Get data sharing record using the data sharing client request ID
	dataSharingClientReqId := attempt.GetAttemptInfo().GetDataSharingClientReqId()
	if dataSharingClientReqId == "" {
		return nil, errors.New("no data sharing client request ID found in attempt info")
	}

	// Get data sharing record
	dataSharingRes, err := s.dataSharingClient.GetDataSharingRecord(ctx, &datasharing.GetDataSharingRecordRequest{
		ClientReqId: dataSharingClientReqId,
	})
	if err = epifigrpc.RPCError(dataSharingRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting data sharing record")
	}

	// Handle analysis initiation and status
	return s.handleAnalysisInitiationAndStatus(ctx, attempt, dataSharingRes.GetDataSharingRecord())
}

func (s *Service) getLatestAnalysisStatusForExistingAttempt(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	// Get latest analysis status from analytics service
	analysisClientReqId := attempt.GetAttemptInfo().GetAnalysisClientReqId()
	if analysisClientReqId == "" {
		analysisClientReqId = attempt.GetClientReqId()
	}

	analysisStatusRes, err := s.caDataAnalyticsClient.GetAnalysisStatus(ctx, &caDataAnalytics.GetAnalysisStatusRequest{
		ActorId:     attempt.GetActorId(),
		ClientReqId: analysisClientReqId,
	})
	if err = epifigrpc.RPCError(analysisStatusRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting latest analysis status")
	}

	// Convert analysis status and return appropriate deeplink
	analysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(analysisStatusRes.GetAnalysisStatus())
	return s.getAnalysisStatusScreen(ctx, attempt, analysisStatus)
}

func (s *Service) createNewAttemptAndProceed(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	source, err := s.convertSourceToAttemptSource(req.GetSource())
	if err != nil {
		return nil, errors.Wrap(err, "error converting source to attempt source")
	}
	newAttempt := &salaryestimation.SalaryEstimationAttempt{
		ClientReqId:  req.GetClientReqId(),
		Source:       source,
		ActorId:      req.GetActorId(),
		ClientParams: &salaryestimation.ClientParams{Client: req.GetClient()},
		Step:         salaryestimation.AttemptStep_ATTEMPT_STEP_SALARY_ESTIMATION,
		Status:       salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
	}
	createdAttempt, err := s.salaryEstimationAttemptDao.Create(ctx, newAttempt)
	if err != nil {
		return nil, errors.Wrap(err, "error creating new attempt")
	}
	err = s.initiateWealthAnalysis(ctx, createdAttempt.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error initiating wealth analysis")
	}
	nextAction, err := s.processNewAttempt(ctx, createdAttempt)
	if err != nil {
		return nil, errors.Wrap(err, "error processing consent and data sharing flow for new attempt")
	}
	return nextAction, nil
}

func (s *Service) updateAttemptStatus(ctx context.Context, attemptId string, status salaryestimation.AttemptStatus) error {
	attempt := &salaryestimation.SalaryEstimationAttempt{
		Id:        attemptId,
		Status:    status,
		UpdatedAt: timestamppb.Now(),
	}

	_, err := s.salaryEstimationAttemptDao.Update(ctx, attempt, []salaryestimation.SalaryEstimationAttemptFieldMask{
		salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS,
	})
	if err != nil {
		logger.Error(ctx, "error updating attempt status", zap.Error(err), zap.String("attempt_id", attemptId), zap.String("status", status.String()))
		return errors.Wrap(err, "failed to update attempt status")
	}

	logger.Info(ctx, "updated attempt status", zap.String("attempt_id", attemptId), zap.String("status", status.String()))
	return nil
}

func (s *Service) updateAttemptStepStatus(ctx context.Context, attemptId string, step salaryestimation.AttemptStep, status salaryestimation.AttemptStatus) error {
	attempt := &salaryestimation.SalaryEstimationAttempt{
		Id:     attemptId,
		Step:   step,
		Status: status,
	}
	fieldMasks := []salaryestimation.SalaryEstimationAttemptFieldMask{
		salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STEP,
		salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS,
	}
	_, err := s.salaryEstimationAttemptDao.Update(ctx, attempt, fieldMasks)
	if err != nil {
		return errors.Wrapf(err, "error updating attempt step and status for attempt id: %s", attemptId)
	}
	return nil
}

func (s *Service) updateAttemptStepStatusAndInfo(ctx context.Context, attemptId string, step salaryestimation.AttemptStep, status salaryestimation.AttemptStatus, attemptInfo *salaryestimation.AttemptInfo) error {
	attempt := &salaryestimation.SalaryEstimationAttempt{
		Id:          attemptId,
		Step:        step,
		Status:      status,
		AttemptInfo: attemptInfo,
	}
	fieldMasks := []salaryestimation.SalaryEstimationAttemptFieldMask{
		salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STEP,
		salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS,
		salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_ATTEMPT_INFO,
	}
	_, err := s.salaryEstimationAttemptDao.Update(ctx, attempt, fieldMasks)
	if err != nil {
		return errors.Wrapf(err, "error updating attempt step, status and info for attempt id: %s", attemptId)
	}
	return nil
}

func (s *Service) updateAttemptInfo(ctx context.Context, attemptId string, attemptInfo *salaryestimation.AttemptInfo) error {
	// TODO(brijesh); Read, use mergo and then update in transaction
	attempt := &salaryestimation.SalaryEstimationAttempt{
		Id:          attemptId,
		AttemptInfo: attemptInfo,
	}
	fieldMasks := []salaryestimation.SalaryEstimationAttemptFieldMask{
		salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_ATTEMPT_INFO,
	}
	_, err := s.salaryEstimationAttemptDao.Update(ctx, attempt, fieldMasks)
	if err != nil {
		return errors.Wrapf(err, "error updating attempt info for attempt id: %s", attemptId)
	}
	return nil
}

func (s *Service) processNewAttempt(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	perpetualConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ActorId:     attempt.GetActorId(),
		ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
		Owner:       common.Owner_OWNER_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(perpetualConsentRes, err); err != nil && !perpetualConsentRes.GetStatus().IsRecordNotFound() {
		return nil, errors.Wrap(err, "error fetching perpetual consent")
	}
	if perpetualConsentRes.GetConsentId() != "" {
		return s.handlePerpetualConsentFlow(ctx, attempt, perpetualConsentRes.GetConsentId())
	}
	oneTimeConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
		ActorId:     attempt.GetActorId(),
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(oneTimeConsentRes, err); err != nil && !oneTimeConsentRes.GetStatus().IsRecordNotFound() {
		return nil, errors.Wrap(err, "error fetching one-time consent")
	}
	return s.handleOneTimeConsentFlow(ctx, attempt, oneTimeConsentRes)
}

func (s *Service) handlePerpetualConsentFlow(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt, consentId string) (*deeplink.Deeplink, error) {
	err := s.updateAttemptInfo(ctx, attempt.GetId(), &salaryestimation.AttemptInfo{PerpetualConsentId: consentId})
	if err != nil {
		return nil, errors.Wrap(err, "failed to update attempt with consent info")
	}
	accounts, err := salaryestimationPkg.GetAccountsToBeAnalysedForActor(ctx, s.connectedAccountClient, attempt.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			if err = s.updateAttemptStepStatus(ctx, attempt.GetId(), salaryestimation.AttemptStep_ATTEMPT_STEP_ACCOUNT_CONNECTION, salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING); err != nil {
				return nil, errors.Wrap(err, "failed to update attempt for account connection")
			}
			accConnScreen, screenErr := s.getAccountConnectionScreen(ctx, &getAccountConnectionScreenReq{attempt: attempt, hasPerpetualConsent: true})
			if screenErr != nil {
				return nil, errors.Wrap(screenErr, "error getting account connection screen")
			}
			return accConnScreen, nil
		}
		return nil, errors.Wrap(err, "error getting accounts to be analyzed")
	}
	var accountIds []string
	for _, account := range accounts {
		accountIds = append(accountIds, account.GetAccountId())
	}
	err = s.updateAttemptStepStatusAndInfo(ctx, attempt.GetId(),
		salaryestimation.AttemptStep_ATTEMPT_STEP_DATA_SHARING, salaryestimation.AttemptStatus_ATTEMPT_STATUS_IN_PROGRESS,
		&salaryestimation.AttemptInfo{
			ConnectedAccountIds: accountIds,
			PerpetualConsentId:  consentId,
		},
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to update attempt with account info")
	}
	downloadDataRes, err := s.dataSharingClient.DownloadData(ctx, &datasharing.DownloadDataRequest{
		Client:          datasharingTypes.Client_CLIENT_SALARY_ESTIMATION,
		ClientOwnership: common.Owner_OWNER_EPIFI_TECH,
		ClientReqId:     attempt.GetClientReqId(),
		ActorId:         attempt.GetActorId(),
		DataType:        datasharingTypes.DataType_DATA_TYPE_AA_ACCOUNTS,
		DataRequestParams: &datasharingTypes.DataRequestParams{
			ReqParams: &datasharingTypes.DataRequestParams_AaAccountsDataQueryParams{
				AaAccountsDataQueryParams: &datasharingTypes.AaAccountsDataQueryParams{
					AccountIds:          accountIds,
					OldestTransactionTs: timestamppb.New(s.getFiDataRangeFromForSalaryEstimation()),
					LatestTransactionTs: timestamppb.Now(),
					ConsentId:           consentId,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(downloadDataRes, err); err != nil {
		return nil, errors.Wrap(err, "error downloading data")
	}
	uploadDataRes, err := s.dataSharingClient.UploadData(ctx, &datasharing.UploadDataRequest{
		DataSharingRecord: downloadDataRes.GetDataSharingRecord(),
	})
	if err = epifigrpc.RPCError(uploadDataRes, err); err != nil {
		return nil, errors.Wrap(err, "error uploading data")
	}
	err = s.updateAttemptStepStatusAndInfo(ctx, attempt.GetId(), salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS, salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING, &salaryestimation.AttemptInfo{
		ConnectedAccountIds:    accountIds,
		PerpetualConsentId:     consentId,
		DataSharingClientReqId: downloadDataRes.GetDataSharingRecord().GetClientRequestId(),
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to update attempt for analysis step")
	}
	return s.handleAnalysisInitiationAndStatus(ctx, attempt, downloadDataRes.GetDataSharingRecord())
}

func (s *Service) handleOneTimeConsentFlow(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt, oneTimeConsentRes *consent.FetchConsentResponse) (*deeplink.Deeplink, error) {
	hasValidOneTimeConsent := false
	if oneTimeConsentRes.GetConsentId() != "" && oneTimeConsentRes.GetExpiresAt() != nil {
		if time.Now().Before(oneTimeConsentRes.GetExpiresAt().AsTime()) {
			hasValidOneTimeConsent = true
		}
	}
	if hasValidOneTimeConsent {
		err := s.updateAttemptInfo(ctx, attempt.GetId(), &salaryestimation.AttemptInfo{LatestValidOneTimeConsentId: oneTimeConsentRes.GetConsentId()})
		if err != nil {
			return nil, errors.Wrap(err, "failed to update attempt with one-time consent info")
		}
	}
	aaDataPullStatus, err := salaryestimationPkg.GetAADataPullStatus(ctx, s.connectedAccountClient, attempt.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			err = s.updateAttemptStepStatus(ctx, attempt.GetId(),
				salaryestimation.AttemptStep_ATTEMPT_STEP_ACCOUNT_CONNECTION, salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING)
			if err != nil {
				return nil, errors.Wrap(err, "failed to update attempt for account connection")
			}
			accConnScreen, screenErr := s.getAccountConnectionScreen(ctx, &getAccountConnectionScreenReq{attempt: attempt, hasValidOneTimeConsent: hasValidOneTimeConsent})
			if screenErr != nil {
				return nil, errors.Wrap(screenErr, "error getting account connection screen")
			}
			return accConnScreen, nil
		}
		return nil, errors.Wrap(err, "error getting AA data pull status")
	}
	err = s.updateAttemptStepStatus(ctx, attempt.GetId(),
		salaryestimation.AttemptStep_ATTEMPT_STEP_CONSENT_COLLECTION, salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING)
	if err != nil {
		return nil, errors.Wrap(err, "failed to update attempt for consent collection")
	}
	if aaDataPullStatus.IsDataPullSuccess() {
		if hasValidOneTimeConsent {
			
		}
		accountBlocks, gaErr := getAccountBlocks(aaDataPullStatus.GetSuccessAccount())
		if gaErr != nil {
			return nil, errors.Wrap(gaErr, "error getting account blocks")
		}
		accountSelectionScreen, screenErr := s.getAccountSelectionScreen(ctx, attempt, accountBlocks, hasValidOneTimeConsent)
		if screenErr != nil {
			return nil, errors.Wrap(screenErr, "error getting account selection screen")
		}
		return accountSelectionScreen, nil
	}
	return nil, errors.Errorf("unhandled aa data pull status: %s", aaDataPullStatus)

	//else if aaDataPullStatus.IsDataPullInProgress() {
	//	//// Data pull is in progress, update to DATA_SHARING step with IN_PROGRESS status
	//	//if err := s.updateAttemptStepStatusAndInfo(ctx, attempt.GetId(), salaryestimation.AttemptStep_ATTEMPT_STEP_DATA_SHARING, salaryestimation.AttemptStatus_ATTEMPT_STATUS_IN_PROGRESS, &salaryestimation.AttemptInfo{
	//	//	LatestValidOneTimeConsentId: getOneTimeConsentId(hasValidOneTimeConsent),
	//	//}); err != nil {
	//	//	return nil, errors.Wrap(err, "failed to update attempt for data sharing in progress")
	//	//}
	//	//logger.Info(ctx, "data pull in progress for connected accounts", zap.String(logger.CLIENT_REQUEST_ID, attempt.GetClientReqId()))
	//	//return s.getLatestAnalysisStatusScreenForDataPullInProgressWithAttempt(ctx, attempt)
	//} else {
	//	//// Data pull failed, update status to FAILED
	//	//logger.Info(ctx, "data pull failed for connected accounts", zap.String(logger.CLIENT_REQUEST_ID, attempt.GetClientReqId()))
	//	//if err := s.updateAttemptStepStatusAndInfo(ctx, attempt.GetId(), salaryestimation.AttemptStep_ATTEMPT_STEP_DATA_SHARING, salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED, &salaryestimation.AttemptInfo{
	//	//	LatestValidOneTimeConsentId: getOneTimeConsentId(hasValidOneTimeConsent),
	//	//}); err != nil {
	//	//	return nil, errors.Wrap(err, "failed to update attempt for data sharing failure")
	//	//}
	//	//return s.getAnalysisStatusScreen(ctx, attempt, "", salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	//}
}

func (s *Service) handleAnalysisInitiationAndStatus(
	ctx context.Context,
	attempt *salaryestimation.SalaryEstimationAttempt,
	dataSharingRecord *datasharingTypes.DataSharingRecord,
) (*deeplink.Deeplink, error) {
	var files []*analytics.File
	for _, sharedFile := range dataSharingRecord.GetData().GetAaAccountsData().GetFiles() {
		file := &analytics.File{
			FilePath: sharedFile.GetUrl(),
			FileMetadata: &analytics.FileMetadata{
				AccountsMetadata: []*analytics.AccountMetadata{
					{
						AccountId:           sharedFile.GetMetadata().GetAaAccountFileMetadata().GetAccountId(),
						OldestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetOldestTransactionTs(),
						LatestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetLatestTransactionTs(),
					},
				},
			},
		}
		files = append(files, file)
	}
	analysisRes, err := s.caDataAnalyticsClient.InitiateAnalysis(ctx, &caDataAnalytics.InitiateAnalysisRequest{
		ClientReqId: attempt.GetClientReqId(),
		ActorId:     attempt.GetActorId(),
		RequestParams: &caDataAnalytics.AnalysisRequestParams{
			DataExchangeRecord: &analytics.DataExchangeRecord{
				ClientReqId: dataSharingRecord.GetClientRequestId(),
				ActorId:     dataSharingRecord.GetActorId(),
				Owner:       dataSharingRecord.GetClientOwnership(),
				Data: &analytics.Data{
					Files: files,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(analysisRes, err); err != nil && !analysisRes.GetStatus().IsAlreadyExists() {
		return nil, errors.Wrap(err, "error initiating analysis")
	}
	err = s.updateAttemptInfo(ctx, attempt.GetId(), &salaryestimation.AttemptInfo{AnalysisClientReqId: attempt.GetClientReqId()})
	if err != nil {
		return nil, errors.Wrap(err, "failed to update attempt with analysis client req id")
	}
	analysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(analysisRes.GetAnalysisStatus())
	switch analysisStatus {
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL:
		if err = s.updateAttemptStepStatus(ctx, attempt.GetId(), salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS, salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL); err != nil {
			return nil, errors.Wrap(err, "failed to update attempt for successful analysis")
		}
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED:
		if err = s.updateAttemptStepStatus(ctx, attempt.GetId(), salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS, salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED); err != nil {
			return nil, errors.Wrap(err, "failed to update attempt for failed analysis")
		}
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS:
		if err = s.updateAttemptStepStatus(ctx, attempt.GetId(), salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS, salaryestimation.AttemptStatus_ATTEMPT_STATUS_IN_PROGRESS); err != nil {
			return nil, errors.Wrap(err, "failed to update attempt for in progress analysis")
		}
	default:
		return nil, errors.Errorf("unhandled analysis status: %s", analysisStatus.String())
	}
	return s.getAnalysisStatusScreen(ctx, attempt, analysisStatus)
}

func (s *Service) getFiDataRangeFromForSalaryEstimation() time.Time {
	// Return 6 months from now for salary estimation flow
	return time.Now().AddDate(0, -6, 0)
}

func (s *Service) convertSourceToAttemptSource(source salaryEstimationTypes.Source) (salaryestimation.Source, error) {
	switch source {
	case salaryEstimationTypes.Source_SOURCE_AA:
		return salaryestimation.Source_SOURCE_AA, nil
	default:
		return salaryestimation.Source_SOURCE_UNSPECIFIED, errors.Errorf("unhandled source: %s", source.String())
	}
}

func (s *Service) getAnalysisSuccessfulScreen(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	switch attempt.GetClientParams().GetClient() {
	case salaryestimation.Client_CLIENT_LOANS:
		clientRedirectionScreen, err := s.getClientRedirectionScreen(ctx, attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting client redirection screen")
		}
		return clientRedirectionScreen, nil
	default:
		return nil, errors.Errorf("unknown client: %s", attempt.GetClientParams().GetClient().String())
	}
}

func (s *Service) getAnalysisInProgressOrFailedScreen(attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	// TODO(Brijesh): Get redirection URL from loans
	switch attempt.GetClientParams().GetClient() {
	case salaryestimation.Client_CLIENT_LOANS:
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		}, nil
	default:
		return nil, errors.Errorf("unknown client: %s", attempt.GetClientParams().GetClient().String())
	}
}

func (s *Service) getClientRedirectionScreen(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*deeplink.Deeplink, error) {
	res, err := s.preApprovedLoanClient.GetRedirectDL(ctx, &palPb.GetRedirectDLRequest{
		ActorId:         attempt.GetActorId(),
		ClientRequestId: attempt.GetClientReqId(),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "client req id not found in loans", zap.String(logger.CLIENT_REQUEST_ID, attempt.GetClientReqId()))
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			}, nil
		}
		return nil, errors.Wrap(err, "error getting loans redirect dl")
	}
	return res.GetRedirectDeeplink(), nil
}

type getAccountConnectionScreenReq struct {
	attempt                *salaryestimation.SalaryEstimationAttempt
	hasPerpetualConsent    bool
	hasValidOneTimeConsent bool
}

func (s *Service) getAccountConnectionScreen(ctx context.Context, req *getAccountConnectionScreenReq) (*deeplink.Deeplink, error) {
	var consentCheckboxes []*widget.CheckboxItem
	if !req.hasPerpetualConsent && !req.hasValidOneTimeConsent {
		consentCheckboxes = append(consentCheckboxes, getCheckboxForOneTimeDataSharingConsent())
	}
	consentCheckboxes = append(consentCheckboxes, getCheckboxForEpifiWealthAndFinvuTnc())
	// TODO(Brijesh): Fix all data pull failure deeplinks
	dataPullFailureDeeplink, err := s.getAnalysisStatusScreen(ctx, req.attempt, salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	if err != nil {
		return nil, errors.Wrap(err, "error getting data pull failure deeplink")
	}
	caConnectionFlowRes, err := s.connectedAccountClient.StartConnectionFlow(ctx, &caPb.StartConnectionFlowRequest{
		ClientReqId: req.attempt.GetClientReqId(),
		ActorId:     req.attempt.GetActorId(),
		CaFlowName:  enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
		CaFlowParams: &caPb.CAFlowParams{
			DataPullSuccessRedirectionDeeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
					Client:      req.attempt.GetClientParams().GetClient().String(),
					ClientReqId: req.attempt.GetClientReqId(),
					Source:      req.attempt.GetSource().String(),
				}),
			},
			DataPullFailureRedirectionDeeplink: dataPullFailureDeeplink,
		},
	})
	if err = epifigrpc.RPCError(caConnectionFlowRes, err); err != nil {
		return nil, errors.Wrap(err, "error starting connection flow")
	}
	accountConnectionScreen, err := s.createAccountConnectionScreen(ctx, req.attempt, consentCheckboxes, caConnectionFlowRes.GetConnectionFlow().GetId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting verify income home screen")
	}
	return accountConnectionScreen, nil
}

func (s *Service) getAccountSelectionScreen(
	ctx context.Context,
	attempt *salaryestimation.SalaryEstimationAttempt,
	accountBlocks []*salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock,
	hasValidOneTimeConsent bool,
) (*deeplink.Deeplink, error) {
	var consentCheckboxes []*widget.CheckboxItem
	if hasValidOneTimeConsent {
		consentCheckboxes = append(consentCheckboxes, getCheckboxForOneTimeDataSharingConsent())
	}
	consentCheckboxes = append(consentCheckboxes, getCheckboxForEpifiWealthAndFinvuTnc())
	// TODO(Brijesh): Fix all data pull failure deeplinks
	dataPullFailureDeeplink, err := s.getAnalysisStatusScreen(ctx, attempt, salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	if err != nil {
		return nil, errors.Wrap(err, "error getting data pull failure deeplink")
	}
	caConnectionFlowRes, err := s.connectedAccountClient.StartConnectionFlow(ctx, &caPb.StartConnectionFlowRequest{
		ClientReqId: attempt.GetClientReqId(),
		ActorId:     attempt.GetActorId(),
		CaFlowName:  enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
		CaFlowParams: &caPb.CAFlowParams{
			DataPullSuccessRedirectionDeeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
					Client:      attempt.GetClientParams().GetClient().String(),
					ClientReqId: attempt.GetClientReqId(),
					Source:      attempt.GetSource().String(),
				}),
			},
			DataPullFailureRedirectionDeeplink: dataPullFailureDeeplink,
		},
	})
	if err = epifigrpc.RPCError(caConnectionFlowRes, err); err != nil {
		return nil, errors.Wrap(err, "error starting connection flow")
	}
	accSelectionScreen, err := s.createAccountSelectionScreen(ctx, attempt, accountBlocks, consentCheckboxes, caConnectionFlowRes.GetConnectionFlow().GetId())
	if err != nil {
		return nil, errors.Wrap(err, "error creating account selection screen")
	}
	return accSelectionScreen, nil
}

func (s *Service) createAccountConnectionScreen(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt, consentCheckboxes []*widget.CheckboxItem, caFlowId string) (*deeplink.Deeplink, error) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), seEvents.NewSalaryEstimationEvent(attempt.GetActorId(), seEvents.NewAccountConnectionScreenLoadedEventName))
	})
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_VERIFY_INCOME_HOME_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.VerifyIncomeHomeScreenOptions{
			BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
			VtsHeaderComponent: &widget.VisualElementTitleSubtitleElement{
				VisualElement: common.
					GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/green-chain-link.png").
					WithProperties(&common.VisualElementProperties{Width: 150, Height: 100}),
				TitleText: common.GetPlainStringText("Connect your account to verify salary").
					WithFontColor(colors.ColorOnLightHighEmphasis).WithFontStyle(common.FontStyle_HEADLINE_XL),
				SubtitleText: common.GetPlainStringText("Our partner needs your salary proof").
					WithFontColor(colors.ColorOnDarkLowEmphasis).WithFontStyle(common.FontStyle_BODY_S),
				BackgroundColor: colors.ColorSnow,
			},
			AdditionalInfos: []*ui.IconTextComponent{
				ui.NewITC().
					WithLeftVisualElement(
						common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/rupee-bank-building.png").
							WithProperties(&common.VisualElementProperties{Width: 36, Height: 36}),
					).
					WithLeftImagePadding(12).
					WithTexts(common.GetPlainStringText("Choose your salary account").
						WithFontStyle(common.FontStyle_SUBTITLE_M).WithFontColor("#383838")),
				ui.NewITC().
					WithLeftVisualElement(
						common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/bank-statement-paper.png").
							WithProperties(&common.VisualElementProperties{Width: 36, Height: 36}),
					).
					WithLeftImagePadding(12).
					WithTexts(common.GetPlainStringText("Fetch your account statement").
						WithFontStyle(common.FontStyle_SUBTITLE_M).WithFontColor("#383838")),
			},
			Consents: getTncComponent(consentCheckboxes),
			Cta: &deeplink.Cta{
				Type:         deeplink.Cta_CONTINUE,
				DisplayTheme: deeplink.Cta_PRIMARY,
				Text:         "Connect salary account",
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&caScreenOptions.BenefitsScreenOptions{
						CaFlowName: enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION.String(),
						CaFlowId:   caFlowId,
					}),
				},
			},
			FooterLabel: moreVerificationMethodsFooter,
			Client:      attempt.GetClientParams().GetClient().String(),
			ClientReqId: attempt.GetClientReqId(),
			Source:      attempt.GetSource().String(),
		}),
	}, nil
}

func (s *Service) createAccountSelectionScreen(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt, bankAccountBlocks []*salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock, consentCheckboxes []*widget.CheckboxItem, caFlowId string) (*deeplink.Deeplink, error) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), seEvents.NewSalaryEstimationEvent(attempt.GetActorId(), seEvents.AccountSelectionScreenLoadedEventName))
	})
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
			BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
			VtsHeaderComponent: &widget.VisualElementTitleSubtitleElement{
				VisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/rupee-gold-bg.png").
					WithProperties(&common.VisualElementProperties{Width: 125, Height: 125}),
				TitleText: common.GetPlainStringText("Confirm if all your sources of income are listed here").
					WithFontStyle(common.FontStyle_HEADLINE_L).WithFontColor(colors.ColorNight),
				SubtitleText: common.GetPlainStringText("This is required to verify your salary").
					WithFontStyle(common.FontStyle_BODY_S).WithFontColor(colors.ColorLead),
				BackgroundColor: colors.ColorSnow,
			},
			BankAccountBlocks: bankAccountBlocks,
			PartnerLogo: ui.NewITC().
				WithLeftVisualElement(common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/powered-by-epifi-wealth.png").
					WithProperties(&common.VisualElementProperties{Width: 106, Height: 16})),
			Consents: getTncComponent(consentCheckboxes),
			PrimaryCta: &deeplink.Cta{
				Type:         deeplink.Cta_CONTINUE,
				DisplayTheme: deeplink.Cta_PRIMARY,
				Text:         "Proceed",
				// No deeplink is sent as the next action is computed dynamically by calling ComputeSalary with account IDs
			},
			SecondaryCta: &deeplink.Cta{
				Type:         deeplink.Cta_CONTINUE,
				DisplayTheme: deeplink.Cta_SECONDARY,
				Text:         "Connect account",
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&caScreenOptions.BenefitsScreenOptions{
						CaFlowName: enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION.String(),
						CaFlowId:   caFlowId,
					}),
				},
			},
			FooterLabel: moreVerificationMethodsFooter,
			Client:      attempt.GetClientParams().GetClient().String(),
			ClientReqId: attempt.GetClientReqId(),
			Source:      attempt.GetSource().String(),
		}),
	}, nil
}

func (s *Service) getAnalysisStatusScreen(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt, latestAnalysisStatus salaryEstimationTypes.AnalysisStatus) (*deeplink.Deeplink, error) {
	commonScreenOptions := &salaryEstimationScreenOptions.IncomeAnalysisStatusScreen{
		BackgroundColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
		VtsHeaderComponent: &widget.VisualElementTitleSubtitleElement{
			BackgroundColor: colors.ColorSnow,
		},
		Client:      attempt.GetClientParams().GetClient().String(),
		ClientReqId: attempt.GetClientReqId(),
		Source:      attempt.GetSource().String(),
		AnalysisParams: &salaryEstimationTypes.AnalysisParams{
			AttemptId: attempt.GetClientReqId(),
			Status:    latestAnalysisStatus,
		},
	}
	switch latestAnalysisStatus {
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS:
		commonScreenOptions.VtsHeaderComponent.VisualElement = common.
			GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/blue-hourglass.png").
			WithProperties(&common.VisualElementProperties{Width: 200, Height: 200})
		commonScreenOptions.VtsHeaderComponent.TitleText = common.GetPlainStringText("Reviewing your account to verify income").
			WithFontStyle(common.FontStyle_HEADLINE_L).WithFontColor(colors.ColorNight)
		commonScreenOptions.VtsHeaderComponent.SubtitleText = common.GetPlainStringText("This usually takes a few minutes").
			WithFontStyle(common.FontStyle_BODY_S).WithFontColor(colors.ColorLead)
		analysisInProgressScreen, err := s.getAnalysisInProgressOrFailedScreen(attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action for in progress analysis")
		}
		commonScreenOptions.Cta = &deeplink.Cta{
			Type:     deeplink.Cta_DONE,
			Text:     "Ok, got it!",
			Deeplink: analysisInProgressScreen,
		}
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL:
		commonScreenOptions.VtsHeaderComponent.VisualElement = common.
			GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/white-ticker-green-bg.png").
			WithProperties(&common.VisualElementProperties{Width: 180, Height: 180})
		commonScreenOptions.VtsHeaderComponent.TitleText = common.GetPlainStringText("Verified successfully").
			WithFontStyle(common.FontStyle_HEADLINE_XL).WithFontColor(colors.ColorOnLightHighEmphasis)
		analysisSuccessfulScreen, err := s.getAnalysisSuccessfulScreen(ctx, attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action for successful analysis")
		}
		commonScreenOptions.Cta = &deeplink.Cta{
			Type:     deeplink.Cta_DONE,
			Text:     "Proceed",
			Deeplink: analysisSuccessfulScreen,
		}
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED:
		commonScreenOptions.VtsHeaderComponent.VisualElement = common.
			GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryestimation/white-exclamation-red-bg.png").
			WithProperties(&common.VisualElementProperties{Width: 200, Height: 200})
		commonScreenOptions.VtsHeaderComponent.TitleText = common.GetPlainStringText("Could not verify").
			WithFontStyle(common.FontStyle_HEADLINE_XL).WithFontColor(colors.ColorOnLightHighEmphasis)
		analysisFailedScreen, err := s.getAnalysisInProgressOrFailedScreen(attempt)
		if err != nil {
			return nil, errors.Wrap(err, "error getting next action for failed analysis")
		}
		commonScreenOptions.Cta = &deeplink.Cta{
			Type:     deeplink.Cta_DONE,
			Text:     "Okay",
			Deeplink: analysisFailedScreen,
		}
	default:
		return nil, errors.Errorf("unknown analysis status: %s", latestAnalysisStatus)
	}
	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_INCOME_ANALYSIS_STATUS_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(commonScreenOptions),
	}, nil
}
