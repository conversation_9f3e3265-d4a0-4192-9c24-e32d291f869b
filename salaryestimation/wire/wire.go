//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/events"
	palPb "github.com/epifi/gamma/api/preapprovedloan"

	caPb "github.com/epifi/gamma/api/connected_account"
	caAnalytics "github.com/epifi/gamma/api/connected_account/analytics"
	caDataAnalytics "github.com/epifi/gamma/api/connected_account/data_analytics"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/datasharing"
	"github.com/epifi/gamma/salaryestimation"
	genConf "github.com/epifi/gamma/salaryestimation/config/genconf"
)

func InitialiseSalaryEstimationService(
	conf *genConf.Config,
	caAnalyticsClient caAnalytics.AnalyticsClient,
	caDataAnalyticsClient caDataAnalytics.DataAnalyticsClient,
	consentClient consent.ConsentClient,
	dataSharingClient datasharing.DataSharingClient,
	connectedAccountClient caPb.ConnectedAccountClient,
	preApprovedLoanClient palPb.PreApprovedLoanClient,
	eventBroker events.Broker,
) *salaryestimation.Service {
	wire.Build(salaryestimation.NewService)
	return &salaryestimation.Service{}
}
