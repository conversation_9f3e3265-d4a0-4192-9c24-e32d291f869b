// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/analytics"
	"github.com/epifi/gamma/api/connected_account/data_analytics"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/datasharing"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/salaryestimation"
	"github.com/epifi/gamma/salaryestimation/config/genconf"
)

// Injectors from wire.go:

func InitialiseSalaryEstimationService(conf *genconf.Config, caAnalyticsClient analytics.AnalyticsClient, caDataAnalyticsClient data_analytics.DataAnalyticsClient, consentClient consent.ConsentClient, dataSharingClient datasharing.DataSharingClient, connectedAccountClient connected_account.ConnectedAccountClient, preApprovedLoanClient preapprovedloan.PreApprovedLoanClient, eventBroker events.Broker) *salaryestimation.Service {
	service := salaryestimation.NewService(conf, caAnalyticsClient, caDataAnalyticsClient, consentClient, dataSharingClient, connectedAccountClient, preApprovedLoanClient, eventBroker)
	return service
}
