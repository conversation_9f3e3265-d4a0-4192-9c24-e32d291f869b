//go:generate mockgen -source=evaluator.go -destination=../../test/mocks/ticket/csat/mock_evaluator.go
package csat

import (
	"context"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	icPb "github.com/epifi/gamma/api/cx/issue_config"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/issue_config/dao"
	dao2 "github.com/epifi/gamma/cx/ticket/dao"
)

var (
	CSATAlreadyRecorded = errors.New("CSAT already recorded")
)

var EvaluatorWireSet = wire.NewSet(NewResolutionCsatEvaluator, wire.Bind(new(Evaluator), new(*ResolutionCsatEvaluator)))

// Evaluator exposes methods to evaluate eligibility of a ticket for a CSAT survey
type Evaluator interface {
	IsTicketEligibleForCsat(ctx context.Context, ticket *ticketPb.TicketDetails, eventId string) (bool, *ticketPb.TicketDetails, error)
}

type ResolutionCsatEvaluator struct {
	supportTicketDao dao2.ISupportTicketDao
	issueConfDao     dao.IssueConfigDao
	inAppCsatDao     dao2.IInAppCsatResponseDao
	csatConfig       *cxGenConf.CsatConfig
}

func NewResolutionCsatEvaluator(supportTicketDao dao2.ISupportTicketDao, issueConfDao dao.IssueConfigDao,
	inAppCsatDao dao2.IInAppCsatResponseDao, csatConfig *cxGenConf.CsatConfig) *ResolutionCsatEvaluator {
	return &ResolutionCsatEvaluator{
		supportTicketDao: supportTicketDao,
		issueConfDao:     issueConfDao,
		inAppCsatDao:     inAppCsatDao,
		csatConfig:       csatConfig,
	}
}

var _ Evaluator = &ResolutionCsatEvaluator{}

func (r *ResolutionCsatEvaluator) IsTicketEligibleForCsat(ctx context.Context, ticket *ticketPb.TicketDetails, eventId string) (bool, *ticketPb.TicketDetails, error) {
	if !lo.Contains(r.csatConfig.AllowedTicketStatusesForCsat().ToStringArray(), ticket.GetStatus().String()) {
		return false, nil, nil
	}

	updatedTicket, getLatestTicketMetaErr := r.GetLatestTicketMeta(ctx, ticket, eventId)
	if getLatestTicketMetaErr != nil {
		logger.Error(ctx, "error fetching latest resolution", zap.Int64("ticket_id", ticket.GetId()), zap.Error(getLatestTicketMetaErr))
		return false, nil, errors.Wrap(getLatestTicketMetaErr, "error fetching latest resolution")
	}
	csatSurveys := updatedTicket.GetTicketMeta().GetSurveyDetails()
	if len(csatSurveys) == 0 {
		logger.Info(ctx, "no triggered surveys found for ticket", zap.Int64("ticket_id", ticket.GetId()))
		return false, nil, nil
	}
	latestSurvey, _ := lo.Last(csatSurveys)
	if lo.Contains([]ticketPb.SurveyStatus{ticketPb.SurveyStatus_SURVEY_STATUS_PARTIALLY_SUBMITTED, ticketPb.SurveyStatus_SURVEY_STATUS_SUBMITTED}, latestSurvey.GetStatus()) {
		logger.Info(ctx, "csat is already given for this ticket", zap.Int64("ticket_id", ticket.GetId()), zap.String("latestResolutionStatus", latestSurvey.GetStatus().String()))
		return false, nil, CSATAlreadyRecorded
	}

	// Resolution Survey Expiry
	if time.Since(latestSurvey.GetCreatedAt().AsTime()) > r.csatConfig.CsatEligibilityWindow() {
		return false, nil, errors.Errorf("ticket is out of csat eligibility window %q", zap.String("csat_eligibility_window", r.csatConfig.CsatEligibilityWindow().String()))
	}

	conf, err := r.issueConfDao.Get(ctx, ticket.GetIssueCategoryId(), icPb.ConfigType_CONFIG_TYPE_ISSUE_RESOLUTION_CSAT_SURVEY)
	if err != nil {
		return false, nil, errors.Wrap(err, "error in fetching issue config")
	}
	return conf.GetConfigPayload().GetIssueResolutionCsatSurveyConfig().GetIsCsatSurveyEnabled(), updatedTicket, nil
}

// GetLatestTicketMeta fetches the latest ticket meta for a given ticket ID
// It also creates a new resolved at entry if it doesn't exist
func (r *ResolutionCsatEvaluator) GetLatestTicketMeta(ctx context.Context, ticket *ticketPb.TicketDetails, eventId string) (*ticketPb.TicketDetails, error) {
	currentSupportTicket, getSupportTicketErr := r.supportTicketDao.GetById(ctx, ticket.GetId())
	if getSupportTicketErr != nil {
		return nil, errors.Wrap(getSupportTicketErr, "error fetching current support ticket")
	}

	ticketMeta := currentSupportTicket.GetTicketMeta()
	updatedSupportTicket := currentSupportTicket
	if len(ticketMeta.GetSurveyDetails()) == 0 {
		ticketMeta.SurveyDetails = []*ticketPb.SurveyDetail{
			{
				EventId:   eventId,
				CreatedAt: currentSupportTicket.GetTicketUpdatedAt(),
				Status:    ticketPb.SurveyStatus_SURVEY_STATUS_WAITING_ON_USER,
			},
		}
		if updatedSupportTicket, getSupportTicketErr = r.supportTicketDao.Update(ctx, currentSupportTicket, []ticketPb.SupportTicketFieldMask{ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_TICKET_META}); getSupportTicketErr != nil {
			logger.Error(ctx, "error while updating ticket meta", zap.Int64("ticket_id", currentSupportTicket.GetId()), zap.Error(getSupportTicketErr))
			return nil, errors.Wrap(getSupportTicketErr, "error while updating ticket meta")
		}
	}

	return updatedSupportTicket, nil
}
