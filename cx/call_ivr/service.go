//nolint:funlen,dupl
package call_ivr

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/mask"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/card/control"
	"github.com/epifi/gamma/api/card/provisioning"
	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	riskProfilePb "github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/cx/call/blocker"
	"github.com/epifi/gamma/cx/call_ivr/dao"
	"github.com/epifi/gamma/cx/call_ivr/metrics"
	"github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"

	commsPb "github.com/epifi/gamma/api/comms"

	savingsPb "github.com/epifi/gamma/api/savings"

	"github.com/epifi/be-common/pkg/epifigrpc"

	userPb "github.com/epifi/gamma/api/user"

	"github.com/epifi/gamma/api/user/onboarding"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	ffPb "github.com/epifi/gamma/api/firefly"

	callIvrPb "github.com/epifi/gamma/api/cx/call_ivr"
)

const invalidOptionKey = "-1"

type Service struct {
	genConf                *genconf.Config
	onboardingClient       onboarding.OnboardingClient
	userClient             userPb.UsersClient
	savingsClient          savingsPb.SavingsClient
	commsClient            commsPb.CommsClient
	cacheStorage           cache.CacheStorage
	callIvrDao             dao.CallIvrDetailsDao
	callBlocker            blocker.Blocker
	riskProfileClient      riskProfilePb.ProfileClient
	releaseEvaluator       release.IEvaluator
	cardControlClient      control.CardControlClient
	cardProvisioningClient provisioning.CardProvisioningClient
	fireflyClient          ffPb.FireflyClient
	eventBroker            events.Broker
}

func NewCallIvrService(genConf *genconf.Config, onboardingClient onboarding.OnboardingClient,
	userClient userPb.UsersClient, savingsClient savingsPb.SavingsClient,
	commsClient commsPb.CommsClient, cacheStorage cache.CacheStorage, callIvrDao dao.CallIvrDetailsDao,
	callBlocker blocker.Blocker, riskProfileClient riskProfilePb.ProfileClient, releaseEvaluator release.IEvaluator,
	cardControlClient control.CardControlClient, cardProvisioningClient provisioning.CardProvisioningClient,
	fireflyClient ffPb.FireflyClient, eventBroker events.Broker) *Service {
	return &Service{
		genConf:                genConf,
		onboardingClient:       onboardingClient,
		userClient:             userClient,
		savingsClient:          savingsClient,
		commsClient:            commsClient,
		cacheStorage:           cacheStorage,
		callIvrDao:             callIvrDao,
		callBlocker:            callBlocker,
		riskProfileClient:      riskProfileClient,
		releaseEvaluator:       releaseEvaluator,
		cardControlClient:      cardControlClient,
		cardProvisioningClient: cardProvisioningClient,
		fireflyClient:          fireflyClient,
		eventBroker:            eventBroker,
	}
}

var _ callIvrPb.IvrServer = &Service{}

func (s *Service) InitiateIvr(ctx context.Context, request *callIvrPb.InitiateIvrRequest) (*callIvrPb.InitiateIvrResponse, error) {
	if request.GetPhoneNumber().ToStringNationalNumber() == "" || request.GetMonitorUcid() == "" {
		logger.Info(ctx, "phone number and monitor ucid are mandatory in request", zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()))
		return &callIvrPb.InitiateIvrResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	// check if user is a registered user
	minimalUserResp, err := s.userClient.GetMinimalUser(ctx, &userPb.GetMinimalUserRequest{
		Identifier: &userPb.GetMinimalUserRequest_PhoneNumber{
			PhoneNumber: request.GetPhoneNumber(),
		},
	})
	isRegisteredUser := true
	if te := epifigrpc.RPCError(minimalUserResp, err); te != nil {
		if minimalUserResp.GetStatus().IsRecordNotFound() {
			isRegisteredUser = false
		} else {
			logger.Error(ctx, "error while checking if user is registered", zap.Error(te), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()))
			// move to not able to authenticate state
			// update ivr details in db
			s.updateIvrDetailsInDb(ctx, &callIvrPb.CallIvrDetail{
				CurrentIvrType:     callIvrPb.IvrType_IVR_TYPE_AUTH,
				PreviousIvrType:    callIvrPb.IvrType_IVR_TYPE_UNSPECIFIED,
				CurrentQuestionId:  5,
				PreviousQuestionId: 0,
				MonitorUcid:        request.GetMonitorUcid(),
				ActorId:            "", // actor id not available in this case
				CurrentIvrState:    callIvrPb.IvrState_IVR_STATE_ONGOING,
				PreviousIvrState:   callIvrPb.IvrState_IVR_STATE_UNSPECIFIED,
			})
			return &callIvrPb.InitiateIvrResponse{
				Status: rpcPb.StatusOk(),
				IvrStateInfo: &callIvrPb.IvrStateInfo{
					IvrType:       callIvrPb.IvrType_IVR_TYPE_AUTH,
					IvrQuestionId: callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_AUTHENTICATION_FAILED.String()],
					IvrState:      callIvrPb.IvrState_IVR_STATE_ONGOING,
					RecordingId:   0,
					WaitDuration:  0,
				},
			}, nil
		}
	}
	if !isRegisteredUser {
		// update ivr details in db
		s.updateIvrDetailsInDb(ctx, &callIvrPb.CallIvrDetail{
			CurrentIvrType:     callIvrPb.IvrType_IVR_TYPE_AUTH,
			PreviousIvrType:    callIvrPb.IvrType_IVR_TYPE_UNSPECIFIED,
			CurrentQuestionId:  callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_ENTER_REGISTERED_MOBILE_NUMBER.String()],
			PreviousQuestionId: callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_UNSPECIFIED.String()],
			MonitorUcid:        request.GetMonitorUcid(),
			ActorId:            "", // actor id is not available for unregistered user
			CurrentIvrState:    callIvrPb.IvrState_IVR_STATE_UNSPECIFIED,
			PreviousIvrState:   callIvrPb.IvrState_IVR_STATE_ONGOING,
		})
		return &callIvrPb.InitiateIvrResponse{
			Status: rpcPb.StatusOk(),
			IvrStateInfo: &callIvrPb.IvrStateInfo{
				IvrType:       callIvrPb.IvrType_IVR_TYPE_AUTH,
				IvrQuestionId: callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_ENTER_REGISTERED_MOBILE_NUMBER.String()],
				IvrState:      callIvrPb.IvrState_IVR_STATE_ONGOING,
				RecordingId:   0,
				WaitDuration:  0,
			},
		}, nil
	}
	childCtx, childCancelFunc := context.WithTimeout(ctx, 3*time.Second)
	defer childCancelFunc()
	// check if the user is flagged as a risk. If yes, then start the risk IVR flow instead of the auth flow.
	riskUserProfileResp, riskUserProfileErr := s.riskProfileClient.GetDetailedUserProfile(childCtx, &riskProfilePb.GetDetailedUserProfileRequest{ActorId: minimalUserResp.GetMinimalUser().GetActorId()})
	if te := epifigrpc.RPCError(riskUserProfileResp, riskUserProfileErr); te != nil {
		logger.Error(childCtx, "error while fetching risk related profile for actor", zap.Error(te), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()))
	}
	isRiskIvrEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesv2.Feature_FEATURE_CX_CALL_RISK_IVR_FLOW).WithActorId(minimalUserResp.GetMinimalUser().GetActorId()))
	if err != nil {
		logger.Error(ctx, "error while checking for risk ivr flow enabled for user group", zap.Error(err), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()), zap.String(logger.ACTOR_ID_V2, minimalUserResp.GetMinimalUser().GetActorId()))
	}

	callIvrType := callIvrPb.IvrType_IVR_TYPE_AUTH
	currentQuestionId := callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_AUTHENTICATE.String()]
	if isRiskIvrEnabled && riskUserProfileResp.ShouldPlayCallRecording() {
		callIvrType = callIvrPb.IvrType_IVR_TYPE_RISK_BLOCK
		currentQuestionId = callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_SELECT_LANGUAGE.String()]
	}

	// record the call ivr type
	metrics.RecordCallIvrType(callIvrType.String())

	// handling for registered user
	// update ivr details in db
	s.updateIvrDetailsInDb(ctx, &callIvrPb.CallIvrDetail{
		CurrentIvrType:     callIvrType,
		PreviousIvrType:    callIvrPb.IvrType_IVR_TYPE_UNSPECIFIED,
		CurrentQuestionId:  currentQuestionId,
		PreviousQuestionId: callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_UNSPECIFIED.String()],
		WaitDuration:       0,
		MonitorUcid:        request.GetMonitorUcid(),
		ActorId:            minimalUserResp.GetMinimalUser().GetActorId(), // actor id is empty as this is an unregistered user
		CurrentIvrState:    callIvrPb.IvrState_IVR_STATE_ONGOING,
		PreviousIvrState:   callIvrPb.IvrState_IVR_STATE_UNSPECIFIED,
	})
	return &callIvrPb.InitiateIvrResponse{
		Status: rpcPb.StatusOk(),
		IvrStateInfo: &callIvrPb.IvrStateInfo{
			IvrType:           callIvrType,
			IvrQuestionId:     currentQuestionId,
			IvrState:          callIvrPb.IvrState_IVR_STATE_ONGOING,
			RecordingId:       0,
			WaitDuration:      0,
			PreferredLanguage: typesv2.Language_LANGUAGE_ENGLISH,
		},
	}, nil
}

func validateProcessIvrUserInput(request *callIvrPb.ProcessIvrUserInputRequest) error {
	switch {
	case request.GetMonitorUcid() == "":
		return errors.New("monitor ucid cannot be empty for processing user input")
	case request.GetPhoneNumber().ToStringNationalNumber() == "":
		return errors.New("phone number cannot be empty for processing user input")
	case request.GetCurrentIvrQuestionId() == 0:
		return errors.New("current IVR question id cannot be empty for processing user input")
	case request.GetCurrentIvrType() == callIvrPb.IvrType_IVR_TYPE_UNSPECIFIED:
		return errors.New("IVR type cannot be unspecified for processing user input")
	default:
		return nil
	}
}

func (s *Service) ProcessIvrUserInput(ctx context.Context, request *callIvrPb.ProcessIvrUserInputRequest) (*callIvrPb.ProcessIvrUserInputResponse, error) {
	validateErr := validateProcessIvrUserInput(request)
	if validateErr != nil {
		logger.Error(ctx, "invalid request", zap.Error(validateErr), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()), zap.Int32(logger.QUESTION_ID, request.GetCurrentIvrQuestionId()))
		return &callIvrPb.ProcessIvrUserInputResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	questionString := callIvrPb.IvrQuestion_name[request.GetCurrentIvrQuestionId()]
	questionEnum := callIvrPb.IvrQuestion(callIvrPb.IvrQuestion_value[questionString])
	switch request.GetCurrentIvrType() {
	case callIvrPb.IvrType_IVR_TYPE_AUTH:
		return s.handleAuthIvrType(ctx, request, questionEnum)
	case callIvrPb.IvrType_IVR_TYPE_RISK_BLOCK:
		return s.handleRiskUserIvrType(ctx, request, questionEnum)
	case callIvrPb.IvrType_IVR_TYPE_DC_BLOCK:
		return s.handleDCBlockIvrType(ctx, request, questionEnum)
	case callIvrPb.IvrType_IVR_TYPE_CC_PRESENCE_CHECK:
		return s.handleCCPresenceCheck(ctx, request, questionEnum)
	default:
		logger.Info(ctx, "invalid ivr type in request", zap.String("ivrType", request.GetCurrentIvrType().String()), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()), zap.Int32(logger.QUESTION_ID, request.GetCurrentIvrQuestionId()))
		return &callIvrPb.ProcessIvrUserInputResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

}

func (s *Service) sendIvrAuthComms(ctx context.Context, getUserResp *userPb.GetUserResponse, request *callIvrPb.ProcessIvrUserInputRequest) {
	inAppNotificationErr := s.sendIvrAuthNotification(ctx, getUserResp, request.GetPhoneNumber())
	if inAppNotificationErr != nil {
		logger.Error(ctx, "error while sending ivr in-app notification", zap.Error(inAppNotificationErr), zap.String(logger.ACTOR_ID_V2, getUserResp.GetUser().GetActorId()))
	}
	emailErr := s.sendIvrAuthEmail(ctx, getUserResp)
	if emailErr != nil {
		logger.Error(ctx, "error while sending email", zap.Error(emailErr), zap.String(logger.ACTOR_ID_V2, getUserResp.GetUser().GetActorId()))
	}
	// sending both email and notification failed
	if inAppNotificationErr != nil && emailErr != nil {
		logger.Info(ctx, "sending both email and in app notification failed", zap.String(logger.ACTOR_ID_V2, getUserResp.GetUser().GetActorId()), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()))
	}
}

func validateGetIvrStateRequest(request *callIvrPb.GetIvrStateRequest) error {
	switch {
	case request.GetMonitorUcid() == "":
		return errors.New("monitor ucid cannot be empty")
	case request.GetCurrentIvrType() == callIvrPb.IvrType_IVR_TYPE_UNSPECIFIED:
		return errors.New("ivr type cannot be unspecified")
	case request.GetCurrentIvrQuestionId() == 0:
		return errors.New("question id cannot be empty")
	case request.GetPhoneNumber().ToStringNationalNumber() == "":
		return errors.New("phone number cannot be empty")
	default:
		return nil
	}
}

func (s *Service) GetIvrState(ctx context.Context, request *callIvrPb.GetIvrStateRequest) (*callIvrPb.GetIvrStateResponse, error) {
	validateErr := validateGetIvrStateRequest(request)
	if validateErr != nil {
		logger.Error(ctx, "invalid request", zap.Error(validateErr))
		return &callIvrPb.GetIvrStateResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	// get the actor id from phone number
	getMinimalUserResp, err := s.userClient.GetMinimalUser(ctx, &userPb.GetMinimalUserRequest{Identifier: &userPb.GetMinimalUserRequest_PhoneNumber{
		PhoneNumber: request.GetPhoneNumber(),
	}})
	if te := epifigrpc.RPCError(getMinimalUserResp, err); te != nil {
		logger.Error(ctx, "error while getting details for onboarded user", zap.Error(te), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()), zap.String("ivrType", request.GetCurrentIvrType().String()), zap.Int32(logger.QUESTION_ID, request.GetCurrentIvrQuestionId()))
		return &callIvrPb.GetIvrStateResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// check the number of polls done for this actor, if not found mark this as first poll,
	// poll counting is done on best effort basis, in the worst case, after polling for a limited time
	// user will get connected to an agent but without auth
	pollCountCacheKey := s.getCacheKeyForAuthPolls(getMinimalUserResp.GetMinimalUser().GetActorId())
	pollCountCachedData, err := s.cacheStorage.Get(ctx, pollCountCacheKey)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			// as the poll count was not found, mark this as the first poll
			cacheErr := s.cacheStorage.Set(ctx, pollCountCacheKey, "1", s.genConf.CallIvrConfig().IvrPollCountCacheDuration())
			if cacheErr != nil {
				logger.Error(ctx, "error while storing first poll attempt in cache", zap.Error(cacheErr), zap.String(logger.REDIS_KEY, pollCountCacheKey))
			}
		} else {
			logger.Error(ctx, "error while fetching poll count cache data", zap.Error(err), zap.String(logger.REDIS_KEY, pollCountCacheKey))
		}
	} else {
		// if there is an error while parsing, treating poll count as 0
		pollCount, _ := strconv.ParseInt(pollCountCachedData, 10, 32)
		// we were able to successfully fetch poll count data
		// check if this is the last poll if yes, inform the user we were unable to authenticate them in case of fi lite user
		isFiLiteUser := false
		featureDetailsResp, featureDetailsErr := s.onboardingClient.GetFeatureDetails(ctx, &onboarding.GetFeatureDetailsRequest{
			ActorId: getMinimalUserResp.GetMinimalUser().GetActorId(),
			Feature: onboarding.Feature_FEATURE_FI_LITE,
		})
		if te := epifigrpc.RPCError(featureDetailsResp, featureDetailsErr); te != nil {
			logger.Error(ctx, "error while checking if user is fi lite user", zap.Error(te), zap.String(logger.ACTOR_ID_V2, getMinimalUserResp.GetMinimalUser().GetActorId()))
		} else {
			if featureDetailsResp.GetIsFiLiteUser() {
				isFiLiteUser = true
			}
		}

		if int32(pollCount) >= s.genConf.CallIvrConfig().IvrPollCountThreshold() {
			// if the user is fi lite user, inform them that we were unable to authenticate them
			// by moving to terminal state, post which the call will be dropped
			if isFiLiteUser {
				// update ivr details in db
				s.updateIvrDetailsInDb(ctx, &callIvrPb.CallIvrDetail{
					CurrentIvrType:     callIvrPb.IvrType_IVR_TYPE_AUTH,
					PreviousIvrType:    request.GetCurrentIvrType(),
					CurrentQuestionId:  callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_AUTHENTICATION_FAILED.String()],
					PreviousQuestionId: request.GetCurrentIvrQuestionId(),
					MonitorUcid:        request.GetMonitorUcid(),
					ActorId:            getMinimalUserResp.GetMinimalUser().GetActorId(),
					CurrentIvrState:    callIvrPb.IvrState_IVR_STATE_ONGOING,
					PreviousIvrState:   callIvrPb.IvrState_IVR_STATE_WAITING_ON_RESPONSE,
				})
				return &callIvrPb.GetIvrStateResponse{
					Status: rpcPb.StatusOk(),
					IvrStateInfo: &callIvrPb.IvrStateInfo{
						IvrType:       callIvrPb.IvrType_IVR_TYPE_AUTH,
						IvrQuestionId: callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_AUTHENTICATION_FAILED.String()],
						IvrState:      callIvrPb.IvrState_IVR_STATE_ONGOING,
						RecordingId:   0,
						WaitDuration:  0,
					},
				}, nil
			}
			// update ivr details in db
			s.updateIvrDetailsInDb(ctx, &callIvrPb.CallIvrDetail{
				CurrentIvrType:     callIvrPb.IvrType_IVR_TYPE_AUTH,
				PreviousIvrType:    request.GetCurrentIvrType(),
				CurrentQuestionId:  callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_ENTER_SAVINGS_ACCOUNT_DIGITS.String()],
				PreviousQuestionId: request.GetCurrentIvrQuestionId(),
				MonitorUcid:        request.GetMonitorUcid(),
				ActorId:            getMinimalUserResp.GetMinimalUser().GetActorId(),
				CurrentIvrState:    callIvrPb.IvrState_IVR_STATE_ONGOING,
				PreviousIvrState:   callIvrPb.IvrState_IVR_STATE_WAITING_ON_RESPONSE,
			})
			// otherwise try performing a lower level auth based on savings account number for non-fi lite user
			return &callIvrPb.GetIvrStateResponse{
				Status: rpcPb.StatusOk(),
				IvrStateInfo: &callIvrPb.IvrStateInfo{
					IvrType:       callIvrPb.IvrType_IVR_TYPE_AUTH,
					IvrQuestionId: callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_ENTER_SAVINGS_ACCOUNT_DIGITS.String()],
					IvrState:      callIvrPb.IvrState_IVR_STATE_ONGOING,
					RecordingId:   0,
					WaitDuration:  0,
				},
			}, nil
		}
		// increment and store the poll count back to cache again
		cacheErr := s.cacheStorage.Set(ctx, pollCountCacheKey, strconv.Itoa(int(pollCount+1)), s.genConf.CallIvrConfig().IvrPollCountCacheDuration())
		if cacheErr != nil {
			logger.Error(ctx, "error while storing first poll attempt in cache", zap.Error(cacheErr), zap.String(logger.REDIS_KEY, pollCountCacheKey))
		}
	}
	// checking if there is a valid cache key against this actor id for a auth factor
	cacheKey := s.getCacheKeyForAuthFactor(getMinimalUserResp.GetMinimalUser().GetActorId())
	cachedData, err := s.cacheStorage.Get(ctx, cacheKey)
	if err != nil {
		logger.Error(ctx, "error while fetching ivr state from cache", zap.Error(err), zap.String(logger.REDIS_KEY, cacheKey))
		// if the key is not found, assume that the auth is still not done
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &callIvrPb.GetIvrStateResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &callIvrPb.GetIvrStateResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	authDetails := &caPb.CachedAuthDetails{}
	unmarshalErr := protojson.Unmarshal([]byte(cachedData), authDetails)
	if unmarshalErr != nil {
		logger.Error(ctx, "error while unmarshalling auth details", zap.Error(unmarshalErr), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()))
		return &callIvrPb.GetIvrStateResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if authDetails.GetAuthFactorStatus() != caPb.AuthFactorStatus_VERIFICATION_SUCCESS {
		logger.Info(ctx, "auth factor status is not successful in cache", zap.String(logger.ACTOR_ID_V2, getMinimalUserResp.GetMinimalUser().GetActorId()), zap.String(logger.CX_AUTH_FACTOR, authDetails.GetAuthFactor().String()), zap.String(logger.STATUS, authDetails.GetAuthFactorStatus().String()), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()))
		// as the auth was explicitly not agreed on by the user, move to not able to verify auth state
		// update ivr details in db
		s.updateIvrDetailsInDb(ctx, &callIvrPb.CallIvrDetail{
			CurrentIvrType:     callIvrPb.IvrType_IVR_TYPE_AUTH,
			PreviousIvrType:    request.GetCurrentIvrType(),
			CurrentQuestionId:  callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_AUTHENTICATION_FAILED.String()],
			PreviousQuestionId: request.GetCurrentIvrQuestionId(),
			MonitorUcid:        request.GetMonitorUcid(),
			ActorId:            getMinimalUserResp.GetMinimalUser().GetActorId(),
			CurrentIvrState:    callIvrPb.IvrState_IVR_STATE_ONGOING,
			PreviousIvrState:   callIvrPb.IvrState_IVR_STATE_WAITING_ON_RESPONSE,
		})
		return &callIvrPb.GetIvrStateResponse{
			Status: rpcPb.StatusOk(),
			IvrStateInfo: &callIvrPb.IvrStateInfo{
				IvrType:       callIvrPb.IvrType_IVR_TYPE_AUTH,
				IvrQuestionId: callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_AUTHENTICATION_FAILED.String()],
				IvrState:      callIvrPb.IvrState_IVR_STATE_ONGOING,
				RecordingId:   0,
				WaitDuration:  0,
			},
		}, nil
	}
	switch authDetails.GetAuthFactor() {
	case caPb.AuthFactor_MOBILE_PROMPT, caPb.AuthFactor_EMAIL_VERIFICATION:
		logger.Info(ctx, "user successfully authenticated through IVR", zap.String(logger.CX_AUTH_FACTOR, authDetails.GetAuthFactor().String()), zap.String(logger.ACTOR_ID_V2, getMinimalUserResp.GetMinimalUser().GetActorId()), zap.String(logger.OZONETEL_MONITOR_UCID, request.GetMonitorUcid()))
		// update ivr details in db
		s.updateIvrDetailsInDb(ctx, &callIvrPb.CallIvrDetail{
			CurrentIvrType:     callIvrPb.IvrType_IVR_TYPE_AUTH,
			PreviousIvrType:    request.GetCurrentIvrType(),
			CurrentQuestionId:  callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_AUTHENTICATE.String()],
			PreviousQuestionId: request.GetCurrentIvrQuestionId(),
			MonitorUcid:        request.GetMonitorUcid(),
			ActorId:            getMinimalUserResp.GetMinimalUser().GetActorId(),
			CurrentIvrState:    callIvrPb.IvrState_IVR_STATE_CONNECT_TO_AGENT,
			PreviousIvrState:   callIvrPb.IvrState_IVR_STATE_WAITING_ON_RESPONSE,
		})
		return &callIvrPb.GetIvrStateResponse{
			Status: rpcPb.StatusOk(),
			IvrStateInfo: &callIvrPb.IvrStateInfo{
				IvrType:       callIvrPb.IvrType_IVR_TYPE_AUTH,
				IvrQuestionId: callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_AUTHENTICATE.String()],
				IvrState:      callIvrPb.IvrState_IVR_STATE_CONNECT_TO_AGENT,
				RecordingId:   0,
				WaitDuration:  0,
			},
		}, nil
	default:
		// as no auth related details were found for the actor, we are returning not found here
		return &callIvrPb.GetIvrStateResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
}

func (s *Service) getCacheKeyForAuthFactor(actorId string) string {
	return fmt.Sprintf(s.genConf.CustomerAuth().AuthFactorCacheKey(), actorId)
}

func (s *Service) getCacheKeyForAuthPolls(actorId string) string {
	return fmt.Sprintf(s.genConf.CallIvrConfig().IvrPollCountCacheKey(), actorId)
}

func (s *Service) setAuthDetailsInCache(ctx context.Context, actorId string, authDetails *caPb.CachedAuthDetails) error {
	authDetailsBytes, marshalErr := protojson.Marshal(authDetails)
	if marshalErr != nil {
		return errors.Wrap(marshalErr, "error while marshalling auth details")
	}
	cacheEmailAuthFactorErr := s.cacheStorage.Set(ctx, s.getCacheKeyForAuthFactor(actorId), string(authDetailsBytes),
		s.genConf.CustomerAuth().AuthFactorCacheValidityDuration())
	return cacheEmailAuthFactorErr
}

func (s *Service) updateIvrDetailsInDb(ctx context.Context, callIvrDetails *callIvrPb.CallIvrDetail) {
	goroutine.Run(ctx, 3*time.Second, func(gCtx context.Context) {
		// masking user input which might contain Pll data such as phone numbers and account numbers.
		// and we are piping these details to the datalake for agents.
		if len(callIvrDetails.GetPreviousQuestionResponse()) > 5 {
			callIvrDetails.PreviousQuestionResponse = mask.MaskLastNDigits(callIvrDetails.GetPreviousQuestionResponse(), 4, "X")
		}
		err := s.callIvrDao.Update(gCtx, callIvrDetails, []callIvrPb.CallIvrDetailFieldMask{callIvrPb.CallIvrDetailFieldMask_CALL_IVR_DETAIL_FIELD_MASK_PREVIOUS_QUESTION_RESPONSE}, []storagev2.FilterOption{dao.WithPreviousQuestionId(callIvrDetails.GetPreviousQuestionId()), dao.WithMonitorUcid(callIvrDetails.GetMonitorUcid())})
		if err != nil && errors.Is(err, epifierrors.ErrRowNotUpdated) {
			_, err = s.callIvrDao.Create(gCtx, callIvrDetails)
			if err != nil {
				logger.Error(gCtx, "error while creating call ivr details", zap.Error(err), zap.String(logger.OZONETEL_MONITOR_UCID, callIvrDetails.GetMonitorUcid()), zap.String(logger.ACTOR_ID_V2, callIvrDetails.GetActorId()), zap.String("ivrState", callIvrDetails.GetCurrentIvrState().String()))
			}
		} else if err != nil {
			logger.Error(gCtx, "error while inserting call ivr details in db", zap.Error(err), zap.String(logger.OZONETEL_MONITOR_UCID, callIvrDetails.GetMonitorUcid()), zap.String(logger.ACTOR_ID_V2, callIvrDetails.GetActorId()), zap.String("ivrState", callIvrDetails.GetCurrentIvrState().String()))
		}
	})
}

// nolint:gocritic
func (s *Service) setOrUpdateIvrDetailsInCache(ctx context.Context, monitorUcid string, invalidInput, repeatInput bool, preferredLanguage typesv2.Language) error {
	cacheResp, cacheErr := s.cacheStorage.Get(ctx, fmt.Sprintf("cx_%s_%s", monitorUcid, "ivr_details"))
	callDetail := &callIvrPb.CacheIvrDetails{}
	if cacheErr != nil && !errors.Is(cacheErr, epifierrors.ErrRecordNotFound) {
		return errors.Wrap(cacheErr, "error while fetching ivr details from cache")
	}
	if len(cacheResp) != 0 {
		err := json.Unmarshal([]byte(cacheResp), callDetail)
		if err != nil {
			return errors.Wrap(err, "error while unmarshalling IVR details")
		}
	}
	if invalidInput {
		callDetail.InvalidInputCount++
	}
	if repeatInput {
		callDetail.RepeatInputCount++
	}
	if preferredLanguage != typesv2.Language_LANGUAGE_UNSPECIFIED {
		callDetail.PreferredLanguage = preferredLanguage
	}

	ivrDetailsBytes, err := protojson.Marshal(callDetail)
	if err != nil {
		logger.Error(ctx, "error while marshalling IVR details", zap.Error(err))
		return err
	}

	// set the call details in cache
	cacheErr = s.cacheStorage.Set(ctx, fmt.Sprintf("cx_%s_%s", monitorUcid, "ivr_details"), string(ivrDetailsBytes), 3*time.Minute)
	if cacheErr != nil {
		logger.Error(ctx, "error while caching preferred language", zap.Error(cacheErr), zap.String(logger.OZONETEL_MONITOR_UCID, monitorUcid))
		return cacheErr
	}
	return nil
}

func (s *Service) getIvrDetailsFromCache(ctx context.Context, monitorUcid string) (*callIvrPb.CacheIvrDetails, error) {
	cacheResp, cacheErr := s.cacheStorage.Get(ctx, fmt.Sprintf("cx_%s_%s", monitorUcid, "ivr_details"))
	callDetail := &callIvrPb.CacheIvrDetails{}
	if cacheErr != nil {
		return nil, errors.Wrap(cacheErr, "error while fetching ivr details from cache")
	}
	err := protojson.Unmarshal([]byte(cacheResp), callDetail)
	if err != nil {
		return nil, errors.Wrap(err, "error while unmarshalling IVR details")
	}
	return callDetail, nil
}
func (s *Service) handleMaxInvalidInput(ctx context.Context, request *callIvrPb.ProcessIvrUserInputRequest, language typesv2.Language) (*callIvrPb.ProcessIvrUserInputResponse, error) {
	s.updateIvrDetailsInDb(ctx, &callIvrPb.CallIvrDetail{
		CurrentIvrType:           callIvrPb.IvrType_IVR_TYPE_RISK_BLOCK,
		PreviousIvrType:          request.GetCurrentIvrType(),
		CurrentQuestionId:        callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_UNSPECIFIED.String()],
		PreviousQuestionId:       request.GetCurrentIvrQuestionId(),
		PreviousQuestionResponse: request.GetCurrentIvrQuestionResponse(),
		MonitorUcid:              request.GetMonitorUcid(),
		CurrentIvrState:          callIvrPb.IvrState_IVR_STATE_COMPLETED,
		PreviousIvrState:         callIvrPb.IvrState_IVR_STATE_ONGOING,
	})
	return &callIvrPb.ProcessIvrUserInputResponse{
		Status: rpcPb.StatusOk(),
		IvrStateInfo: &callIvrPb.IvrStateInfo{
			IvrType:           callIvrPb.IvrType_IVR_TYPE_RISK_BLOCK,
			IvrQuestionId:     callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_UNSPECIFIED.String()],
			IvrState:          callIvrPb.IvrState_IVR_STATE_COMPLETED,
			RecordingId:       0,
			WaitDuration:      0,
			PreferredLanguage: language,
		},
	}, nil
}
func (s *Service) handleMaxRepeatInput(ctx context.Context, request *callIvrPb.ProcessIvrUserInputRequest, language typesv2.Language) (*callIvrPb.ProcessIvrUserInputResponse, error) {
	s.updateIvrDetailsInDb(ctx, &callIvrPb.CallIvrDetail{
		CurrentIvrType:           callIvrPb.IvrType_IVR_TYPE_RISK_BLOCK,
		PreviousIvrType:          request.GetCurrentIvrType(),
		CurrentQuestionId:        callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_UNSPECIFIED.String()],
		PreviousQuestionId:       request.GetCurrentIvrQuestionId(),
		PreviousQuestionResponse: request.GetCurrentIvrQuestionResponse(),
		MonitorUcid:              request.GetMonitorUcid(),
		CurrentIvrState:          callIvrPb.IvrState_IVR_STATE_COMPLETED,
		PreviousIvrState:         callIvrPb.IvrState_IVR_STATE_ONGOING,
	})
	return &callIvrPb.ProcessIvrUserInputResponse{
		Status: rpcPb.StatusOk(),
		IvrStateInfo: &callIvrPb.IvrStateInfo{
			IvrType:           callIvrPb.IvrType_IVR_TYPE_RISK_BLOCK,
			IvrQuestionId:     callIvrPb.IvrQuestion_value[callIvrPb.IvrQuestion_IVR_QUESTION_UNSPECIFIED.String()],
			IvrState:          callIvrPb.IvrState_IVR_STATE_COMPLETED,
			RecordingId:       0,
			WaitDuration:      0,
			PreferredLanguage: language,
		},
	}, nil
}
