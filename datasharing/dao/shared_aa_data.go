package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/api/datasharing"
	dataprepPb "github.com/epifi/gamma/api/datasharing/dataprep"
	dataSharingTypes "github.com/epifi/gamma/api/typesv2/datasharing"
	"github.com/epifi/gamma/datasharing/dao/model"
	"github.com/epifi/gamma/datasharing/dataprep"
	"github.com/epifi/gamma/datasharing/wire/types"
)

type SharedAADataDaoS3 struct {
	sharedDataStorageS3Client types.SharedDataStorageS3Client
	dataPrepFactory           dataprep.IFactory
}

func NewSharedAADataDaoS3(
	sharedDataStorageS3Client types.SharedDataStorageS3Client,
	dataPrepFactory dataprep.IFactory,
) *SharedAADataDaoS3 {
	return &SharedAADataDaoS3{
		sharedDataStorageS3Client: sharedDataStorageS3Client,
		dataPrepFactory:           dataPrepFactory,
	}
}

const maxS3ObjectPreSignedUrlExpiryDuration = 60 * 60 * 24 * 7

func (s *SharedAADataDaoS3) Download(ctx context.Context, req *datasharing.DownloadDataRequest) (*dataSharingTypes.DataSharingRecord, error) {
	defer metric_util.TrackDuration("datasharing/dao", "SharedAADataDaoS3", "Download", time.Now())
	var files []*dataSharingTypes.File
	for _, accountId := range req.GetDataRequestParams().GetAaAccountsDataQueryParams().GetAccountIds() {
		file, err := s.downloadDataForAccount(ctx, req, accountId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, "no data found for account", zap.String(logger.ACCOUNT_ID, accountId))
				continue
			}
			return nil, errors.Wrap(err, "error getting or downloading file")
		}
		files = append(files, file)
	}
	return &dataSharingTypes.DataSharingRecord{
		Client:          req.GetClient(),
		ClientRequestId: req.GetClientReqId(),
		ActorId:         req.GetActorId(),
		ClientOwnership: req.GetClientOwnership(),
		DataType:        req.GetDataType(),
		Data: &dataSharingTypes.Data{
			Data: &dataSharingTypes.Data_AaAccountsData{
				AaAccountsData: &dataSharingTypes.AaAccountsData{
					Files: files,
				},
			},
		},
		ConsentId: req.GetDataRequestParams().GetAaAccountsDataQueryParams().GetConsentId(),
	}, nil
}

func (s *SharedAADataDaoS3) downloadDataForAccount(ctx context.Context, req *datasharing.DownloadDataRequest, accountId string) (*dataSharingTypes.File, error) {
	filePath, err := getExpectedAccountFilePath(&getExpectedAccountFilePathReq{
		owner:       common.Owner_OWNER_EPIFI_WEALTH,
		actorId:     req.GetActorId(),
		accountId:   accountId,
		clientReqId: req.GetClientReqId(),
		consentId:   req.GetDataRequestParams().GetAaAccountsDataQueryParams().GetConsentId(),
	})
	if err != nil {
		return nil, errors.Wrap(err, "error getting file path")
	}
	fileData, err := s.sharedDataStorageS3Client.ReadObject(ctx, filePath)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			file, downloadErr := s.downloadAndStoreAccountData(ctx, req, accountId, filePath)
			if downloadErr != nil {
				return nil, errors.Wrap(downloadErr, "error downloading and storing download AA account data")
			}
			return file, nil
		}
		return nil, errors.Wrap(err, "error reading file from S3")
	}
	sharedAAData := &model.SharedAAData{}
	err = json.Unmarshal(fileData, sharedAAData)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling data with metadata")
	}
	preSignedUrl, err := s.sharedDataStorageS3Client.GetPreSignedUrl(ctx, filePath, maxS3ObjectPreSignedUrlExpiryDuration)
	if err != nil {
		return nil, errors.Wrap(err, "error getting pre-signed URL")
	}
	return &dataSharingTypes.File{
		Url: preSignedUrl,
		Metadata: &dataSharingTypes.FileMetadata{
			FileTypeMetadata: &dataSharingTypes.FileMetadata_AaAccountFileMetadata{
				AaAccountFileMetadata: sharedAAData.Metadata,
			},
		},
	}, nil
}

func (s *SharedAADataDaoS3) downloadAndStoreAccountData(ctx context.Context, req *datasharing.DownloadDataRequest, accountId, filePath string) (*dataSharingTypes.File, error) {
	aaAccountsRawDataPrep, err := s.dataPrepFactory.GetDataPrepImpl(dataprepPb.DataKind_DATA_KIND_AA_ACCOUNTS_RAW)
	if err != nil {
		return nil, errors.Wrap(err, "error getting data prep implementation")
	}
	prepareDataRes, err := aaAccountsRawDataPrep.PrepareData(ctx, &dataprepPb.PrepareDataRequest{
		DataKind: dataprepPb.DataKind_DATA_KIND_AA_ACCOUNTS_RAW,
		Data: &dataprepPb.PrepareDataRequest_AaAccountsRawRequestData{
			AaAccountsRawRequestData: &dataprepPb.AaAccountsRawRequestData{
				ActorId:   req.GetActorId(),
				AccountId: accountId,
				StartTime: req.GetDataRequestParams().GetAaAccountsDataQueryParams().GetOldestTransactionTs(),
				EndTime:   req.GetDataRequestParams().GetAaAccountsDataQueryParams().GetLatestTransactionTs(),
			},
		},
	})
	if err != nil {
		return nil, errors.Wrapf(err, "error preparing data for account %s", accountId)
	}
	b, err := getFileDataFromURL(prepareDataRes.GetAaAccountsRawData().GetFileUrl())
	if err != nil {
		return nil, errors.Wrap(err, "error getting file data from URL")
	}
	metadata := &dataSharingTypes.AaAccountFileMetadata{
		AccountId:           accountId,
		OldestTransactionTs: prepareDataRes.GetAaAccountsRawData().GetAccountMetadata().GetOldestTransactionTs(),
		LatestTransactionTs: prepareDataRes.GetAaAccountsRawData().GetAccountMetadata().GetLatestTransactionTs(),
	}
	sharedAAData := &model.SharedAAData{
		FileData: b,
		Metadata: metadata,
	}
	dataWithMetadataJson, err := json.Marshal(sharedAAData)
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling data with metadata")
	}
	preSignedUrl, err := s.sharedDataStorageS3Client.WriteAndGetPreSignedUrl(ctx, filePath, dataWithMetadataJson, maxS3ObjectPreSignedUrlExpiryDuration)
	if err != nil {
		return nil, errors.Wrap(err, "error writing file to S3")
	}
	return &dataSharingTypes.File{
		Url: preSignedUrl,
		Metadata: &dataSharingTypes.FileMetadata{
			FileTypeMetadata: &dataSharingTypes.FileMetadata_AaAccountFileMetadata{
				AaAccountFileMetadata: sharedAAData.Metadata,
			},
		},
	}, nil
}

func (s *SharedAADataDaoS3) Upload(ctx context.Context, req *dataSharingTypes.DataSharingRecord) (*dataSharingTypes.DataSharingRecord, error) {
	defer metric_util.TrackDuration("datasharing/dao", "SharedAADataDaoS3", "Upload", time.Now())
	var files []*dataSharingTypes.File
	for _, sharedFile := range req.GetData().GetAaAccountsData().GetFiles() {
		filePath, err := getExpectedAccountFilePath(&getExpectedAccountFilePathReq{
			owner:       common.Owner_OWNER_EPIFI_TECH,
			actorId:     req.GetActorId(),
			accountId:   sharedFile.GetMetadata().GetAaAccountFileMetadata().GetAccountId(),
			clientReqId: req.GetClientRequestId(),
			consentId:   req.GetConsentId(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "error getting file path")
		}
		fileData, err := getFileDataFromURL(sharedFile.GetUrl())
		if err != nil {
			return nil, errors.Wrap(err, "error getting file data from URL")
		}
		sharedAAData := &model.SharedAAData{}
		err = json.Unmarshal(fileData, sharedAAData)
		if err != nil {
			return nil, errors.Wrap(err, "error unmarshalling data with metadata")
		}
		dataWithMetadataJson, err := json.Marshal(sharedAAData)
		if err != nil {
			return nil, errors.Wrap(err, "error marshalling data with metadata")
		}
		preSignedUrl, err := s.sharedDataStorageS3Client.WriteAndGetPreSignedUrl(ctx, filePath, dataWithMetadataJson, maxS3ObjectPreSignedUrlExpiryDuration)
		if err != nil {
			return nil, errors.Wrap(err, "error getting pre-signed URL")
		}
		file := &dataSharingTypes.File{
			Url: preSignedUrl,
			Metadata: &dataSharingTypes.FileMetadata{
				FileTypeMetadata: &dataSharingTypes.FileMetadata_AaAccountFileMetadata{
					AaAccountFileMetadata: sharedAAData.Metadata,
				},
			},
		}
		files = append(files, file)
	}
	return &dataSharingTypes.DataSharingRecord{
		Client:          req.GetClient(),
		ClientRequestId: req.GetClientRequestId(),
		ActorId:         req.GetActorId(),
		ClientOwnership: req.GetClientOwnership(),
		DataType:        req.GetDataType(),
		Data: &dataSharingTypes.Data{
			Data: &dataSharingTypes.Data_AaAccountsData{
				AaAccountsData: &dataSharingTypes.AaAccountsData{Files: files},
			},
		},
		ConsentId: req.GetConsentId(),
	}, nil
}

type getExpectedAccountFilePathReq struct {
	owner       common.Owner
	actorId     string
	accountId   string
	clientReqId string
	consentId   string
}

func getExpectedAccountFilePath(req *getExpectedAccountFilePathReq) (string, error) {
	var ownerFilePathPrefix string
	switch req.owner {
	case common.Owner_OWNER_EPIFI_WEALTH:
		ownerFilePathPrefix = "epifi-wealth-aa-shared-data"
	case common.Owner_OWNER_EPIFI_TECH:
		ownerFilePathPrefix = "epifi-tech-aa-shared-data"
	default:
		return "", errors.Errorf("unsupported owner %s", req.owner.String())
	}
	d := datetime.TimestampToDateInLoc(timestamp.Now(), datetime.IST)
	dateStr := fmt.Sprintf("%d-%02d-%02d", d.GetYear(), d.GetMonth(), d.GetDay())
	filePath := fmt.Sprintf("%s/%s/%s/%s/%s_%s.bin", ownerFilePathPrefix, req.actorId, req.accountId, dateStr, req.clientReqId, req.consentId)
	return filePath, nil
}

func getFileDataFromURL(url string) (b []byte, err error) {
	// nolint: gosec
	fileContent, err := http.Get(url)
	if err != nil {
		return nil, errors.Wrap(err, "error getting file content")
	}
	defer func() {
		err = fileContent.Body.Close()
		if err != nil {
			err = errors.Wrap(err, "error closing file content body")
			return
		}
	}()
	b, err = io.ReadAll(fileContent.Body)
	if err != nil {
		return nil, errors.Wrap(err, "error reading file content body")
	}
	return b, nil
}
