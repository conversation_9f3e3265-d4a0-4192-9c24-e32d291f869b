Application:
  Environment: "qa"
  Name: "wealthonboarding"

Server:
  Ports:
    GrpcPort: 8089
    GrpcSecurePort: 9507
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  AppName: "wealthonboarding"
  StatementTimeout: 1s
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

VendorRequestProducer:
  StreamName: "qa-wealthonboarding-vendor-request-publish-stream"

AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: true
  VendorRequestStore: "kinesis"   # options are "kinesis" "crdb" "crdb_and_kinesis"
  EnableStepStatusUpdateValidations: false

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "qa/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "qa/gcloud/profiling-service-account-key"
    OfficerSignature: "qa/connectedaccount/docket-verification-officer-signature"

RefreshFaceMatchStatusSqsPublisher:
  QueueName: "qa-wo-refresh-fm-status-delay-queue"

RefreshFaceMatchStatusSqsSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-wo-refresh-fm-status-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

FetchFMStatusInitialDelayDuration: 60s

UpdateTxnStateSqsPublisher:
  QueueName: "qa-wo-e-sign-txn-state-delay-queue"

UpdateTxnStateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-wo-e-sign-txn-state-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 10
      TimeUnit: "Second"

UpdateTxnStateDelayDuration: 5s

InitWealthOnboardingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-wo-onboarding-stage-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 15
        Period: 1m
    Namespace: "connectedaccount"

MinNameMatchScore: 1

CvlKra:
  UploadPath: "/data"
  PdfExpiryTime: 86400
  VerificationTimeInHours: 0

S3Conf:
  Bucket: "epifi-qa-wealth-onboarding"
  LivenessPath: "liveness"
  DownloadedKraDocS3Path: "downloaded_kra_doc"

VendorRequestS3Conf:
  Bucket: "epifi-qa-wealthonboarding-vendor-request"

Digio:
  BaseSignPath: "https://ext.digio.in/#/gateway/login"

LivenessConfig:
  OTPThreshold: 50
  LivenessThreshold: 50
  LivenessStalenessDuration: 72h

KraDocketSignConfig:
  OfficerName: "Jolly Joseph"
  EmployeeCode: "EPIFI_007"
  OfficerDesignation: "ASST MANAGER"
  IntermediaryCode: "IC_007"
  CallBackUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

AgreementPdf:
  RelativePath: "data/sample-agreement.pdf"

OCRThresholdScoreConfig:
    DOCUMENT_PROOF_TYPE_PAN: 40.0
    DOCUMENT_PROOF_TYPE_DRIVING_LICENSE: 100.0
    DOCUMENT_PROOF_TYPE_PASSPORT: 100.0
    DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR: 100.0
    DOCUMENT_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION: 100.0

DaysToExpiry: 180

WealthOnboardingStepsRetrySqsCustomDelayPublisher:
  DestQueueName: "qa-wo-steps-retry-delay-queue"
  OrchestratorSqsPublisher:
    QueueName: "qa-custom-delay-orchestrator-queue"

WealthOnboardingStepsRetryDelaySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-wo-steps-retry-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

StepsRetryStrategy:
  "ONBOARDING_STEP_DOWNLOAD_KRA_DOC":
    "STEP_STATUS_IN_PROGRESS":
      "STEP_SUB_STATUS_KRA_DOCKET_DOWNLOAD_NOT_READY_YET":
        RegularInterval:
          Interval: 30
          MaxAttempts: 10
          TimeUnit: "Minute"
  "ALLSTEPS":
    "STEP_STATUS_TRANSIENT_FAILURE":
      "ALLSUBSTATUS":
        RegularInterval:
          Interval: 1
          MaxAttempts: 24
          TimeUnit: "Hour"
    "STEP_STATUS_IN_PROGRESS":
      "STEP_SUB_STATUS_KYC_NOT_VALIDATED_YET":
        RegularInterval:
          Interval: 1
          MaxAttempts: 3
          TimeUnit: "Minute"
      "STEP_SUB_STATUS_VENDOR_DOWNTIME":
        RegularInterval:
          Interval: 1
          MaxAttempts: 3
          TimeUnit: "Minute"

FeatureControlledReleaseConfig:
  WEALTH_ONBOARDING_FEATURE_TNC_V2:
    AppVersionConstraintConfig:
      MinAndroidVersion: 318
      MinIOSVersion: 2001
  WEALTH_ONBOARDING_FEATURE_PAN_DOB_SUBMIT_SCREEN:
    AppVersionConstraintConfig:
      MinAndroidVersion: 318
      MinIOSVersion: 2001
  WEALTH_ONBOARDING_FEATURE_NSDL_PAN_INQUIRY_WITH_NAME:
    UserGroupConstraintConfig:
      UserGroup: "INTERNAL"
    AppVersionConstraintConfig:
      MinAndroidVersion: 332
      MinIOSVersion: 2172
  WEALTH_ONBOARDING_FEATURE_RISK_PROFILING:
    AppVersionConstraintConfig:
      MinAndroidVersion: 332
      MinIOSVersion: 2172
  WEALTH_ONBOARDING_FEATURE_MANDATORY_LIVENESS:
    AppVersionConstraintConfig:
      MinAndroidVersion: 374
      MinIOSVersion: 528
  WEALTH_ONBOARDING_FEATURE_UPDATE_NOMINEE_DETAILS:
    AppVersionConstraintConfig:
      MinAndroidVersion: 452
      MinIOSVersion: 2874
    AppPlatformConstraintConfig:
      Android: true
      Ios: true

NotifyMeFix:
  MaxAndroidVersion: 9999
  MaxIOSVersion: 446

DigilockerConfig:
  ClientId: ""
  LoginUrl: "%vhttps://epifi-redirection.s3.ap-south-1.amazonaws.com/redirected-page.html?redirectURL=%v?code=dummyCode&redirectLatency=3000&redirectParamKey=redirectURL&contextText=Dummy_digilocker_page"
  CallbackUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

KraDowntime:
  DowntimeFrom: "2022-03-11 17:05:00.000"
  DowntimeTo: "2022-03-11 17:10:00.000"

Tracing:
  Enable: true

LambdaFunctions:
  ImageConverterName: "ImageConverterFunction"

MaxPanUploadAttempts: 5

WealthAuthFactorUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-wealth-onb-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

AuthFactorUpdateWealthPublisher:
  TopicName: "qa-auth-factor-update-wealth-topic"

UserCommsPublisher:
  QueueName: "qa-wonb-user-comms-delay-queue"

UserCommsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-wonb-user-comms-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

# Notifications to send users based on step, status and sub-status combination
# Step, Status, SubStatus and Campaign must be valid strings based on their respective enums
# Step: wealthonboarding.OnboardingStep, Status: wealthonboarding.OnboardingStepStatus, SubStatus: wealthonboarding.OnboardingStepSubStatus, Campaign: comms.CampaignName
# Delay must be a valid time.Duration
UserComms:
  # PAN card verification of user failed because of DOB mismatch
  - Step: "ONBOARDING_STEP_DOWNLOAD_KRA_DOC"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_EMPTY_DOWNLOAD_API_RESPONSE"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_DOB_MISMATCH_PAN_NEEDED"
        NotificationData:
          Title: "ID Needed! 📸"
          Body: "Please upload a copy of your PAN card to start investing"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "1m"
  # User visited dropped off from missing data input screen
  # TODO(Brijesh): This PN was meant for users dropping off from first visit screen
  - Step: "ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_USER_INPUT_NEEDED"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_START_SCREEN_DROP_OFF_T15MIN"
        NotificationData:
          Title: "Reasons to invest from Fi 🥰 ⬇️"
          Body: "🗓 Daily, weekly and monthly SIPs\n0️⃣ Commission Fees\n⏳ 3-minutes joining process\n\nTap to get started!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "1m"
      - Campaign: "CAMPAIGN_NAME_WONB_START_SCREEN_DROP_OFF_T24HR"
        NotificationData:
          Title: "Unlock the next level of Fi ⭐️⭐️🌟"
          Body: "Complete a quick 3-minute joining process to start investing directly from Fi."
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "2m"
  # PAN card verification not completed due to technical error and user doesn't retry
  # Note: This doesn't cater to the DOB mismatch case while downloading KRA docs, separate PN is present for that
  - Step: "ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_USER_INPUT_INCL_PAN_REUPLOAD_NEEDED"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_PAN_DROP_OFF_T15MIN"
        NotificationData:
          Title: "🤏 You're this close to the finish line"
          Body: "Give your PAN verification another shot! Make sure you hold the camera still while it captures your PAN card."
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "1m"
      - Campaign: "CAMPAIGN_NAME_WONB_PAN_DROP_OFF_T24HR"
        NotificationData:
          Title: "Almost there! Verify your PAN details ⬇️"
          Body: "Pick up where you left off! Give your PAN verification another shot and get closer to investing directly from Fi."
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "2m"
  # Video verification not completed due to error and user doesn't retry
  # TODO(Brijesh): Uncomment after testing successfully in non-prod
  #  - Step: "ONBOARDING_STEP_LIVENESS"
  #    Status: "STEP_STATUS_IN_PROGRESS"
  #    SubStatus: "STEP_SUB_STATUS_LIVENESS_FAILED_WITH_RETRY"
  #    Notifications:
  #      - Campaign: "CAMPAIGN_NAME_WONB_VIDEO_DROP_OFF_T15MIN"
  #        NotificationData:
  #          Title: "Complete your video verification now! ⬇️"
  #          Body: "You've got another chance to retry video verification! Make sure you are in a well-lit area and read out the 4-digits clearly to complete it."
  #          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
  #        Delay: "1m"
  #      - Campaign: "CAMPAIGN_NAME_WONB_VIDEO_DROP_OFF_T24HR"
  #        NotificationData:
  #          Title: "Ready to retry video verification?"
  #          Body: "Your video verification couldn't be completed last time. But, no worries! Tap now to retry ➡️"
  #          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
  #        Delay: "2m"
  # User has completed wealth onboarding but drops off before e-signing Aadhaar
  - Step: "ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_CUSTOMER_SIGN_PENDING"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_AADHAAR_SIGN_DROP_OFF_T30MIN"
        NotificationData:
          Title: "Seal it with a sign ✍️️"
          Body: "Complete your joining process and start investing on Fi now!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "1m"
      - Campaign: "CAMPAIGN_NAME_WONB_AADHAAR_SIGN_DROP_OFF_T24HR"
        NotificationData:
          Title: "Give us a sign 👀 ✍️"
          Body: "Complete your joining process and start investing on Fi now!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "2m"
  # User drops-off before accepting terms and conditions
  - Step: "ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_UNSPECIFIED"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_TNC_DROP_OFF_T15MIN"
        NotificationData:
          Title: "One last step! 💪️️"
          Body: "Read through and accept the T&Cs to start investing on Fi!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "1m"
      - Campaign: "CAMPAIGN_NAME_WONB_TNC_DROP_OFF_T24HR"
        NotificationData:
          Title: "Start Mutual Fund investments on Fi 💸️"
          Body: "Invest directly and enjoy 0 commission, 0 brokerage and 100% convenience."
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "2m"
  # User drops-off on visiting first wealth onboarding screen or digilocker CTAs
  # TODO(Brijesh): Use diff notification for drop-offs from first wealth onboarding screen
  - Step: "ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER"
    Status: "STEP_STATUS_PENDING"
    SubStatus: "STEP_SUB_STATUS_DIGILOCKER_ACCOUNT_AVAILABILITY_UNKNOWN"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_DIGILOCKER_MODAL_DROP_OFF_T15MIN"
        NotificationData:
          Title: "This will only take a few minutes ⏳️"
          Body: "You are just a few steps away, connect your Digilocker account and start investing"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "1m"
      - Campaign: "CAMPAIGN_NAME_WONB_DIGILOCKER_MODAL_DROP_OFF_T24HR"
        NotificationData:
          Title: "🤏 You're this close to investing on Fi"
          Body: "Finish your joining process now!"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "2m"
  # User started digilocker flow but dropped off before completing it
  - Step: "ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER"
    Status: "STEP_STATUS_IN_PROGRESS"
    SubStatus: "STEP_SUB_STATUS_UNSPECIFIED"
    Notifications:
      - Campaign: "CAMPAIGN_NAME_WONB_DIGILOCKER_FLOW_DROP_OFF_T30MIN"
        NotificationData:
          Title: "Get one step closer to investing on Fi!"
          Body: "Your documents need a quick verification before you can hop on to investing on Fi. Complete the process now ➡️"
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "1m"
      - Campaign: "CAMPAIGN_NAME_WONB_DIGILOCKER_FLOW_DROP_OFF_T24HR"
        NotificationData:
          Title: "Take it to the finish line 🏁"
          Body: "You're so close! Finish your joining process to invest on Fi now."
          IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Delay: "2m"

InhouseNameMatchThreshold: 0.8

# in non-prod, we are using common redis cluster with a different db
RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 7

AdvisoryAgreement:
  UnsignedDocS3Path: "common/legal/EpiFi_Investment_Advisory_Services_Agreement.pdf"

Manch:
  DocketTemplateKey: "TMPTS01578"
  AdvisoryAgreementTemplateKey: "TMPTS02309"

InvestmentsFaqCategoryId: "***********"
MaxPanDobSubmitAttempts: 3
DisableCKYC: false
