// nolint
package jarvisAws

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	aws "github.com/aws/aws-sdk-go-v2/aws"
	awsec2 "github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"

	"github.com/epifi/be-common/pkg/aws/v2/autoscaling"
	"github.com/epifi/be-common/pkg/aws/v2/ec2"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/jarvis/config"
	"github.com/epifi/gamma/jarvis/metrics"
)

func GetLaunchPermissions(ctx context.Context, ec2_client *ec2.Client, id *string, attribute *string) ([]types.LaunchPermission, error) {
	defer metrics.RecordJarvisAws("ec2", "GetLaunchPermissions", "ok", time.Now())
	launchPermissions, err := ec2.GetLaunchPermissions(ctx, ec2_client, id, *attribute)
	if err != nil {
		return nil, err
	}
	return launchPermissions, nil
}

func GetEc2Images(ctx context.Context, ec2_client *ec2.Client, ami_id_list []string) ([]types.Image, error) {
	defer metrics.RecordJarvisAws("ec2", "GetEc2Images", "ok", time.Now())
	ec2Images, err := ec2.GetEc2Images(ctx, ec2_client, ami_id_list)
	if err != nil {
		return nil, err
	}
	return ec2Images, nil
}

func GetEc2ImagesWithFilters(ctx context.Context, ec2_client *ec2.Client, filter []types.Filter, owners []string) ([]types.Image, error) {
	defer metrics.RecordJarvisAws("ec2", "GetEc2ImagesWithFilters", "ok", time.Now())
	ec2ImagesWithFilters, err := ec2.GetEc2ImagesWithFilters(ctx, ec2_client, filter, owners)
	if err != nil {
		return nil, err
	}
	return ec2ImagesWithFilters, nil
}

func ModifyImageAttribute(ctx context.Context, ec2_client *ec2.Client, id *string, attribute *string, launch_permission *types.LaunchPermissionModifications) (*awsec2.ModifyImageAttributeOutput, error) {
	defer metrics.RecordJarvisAws("ec2", "ModifyImageAttribute", "ok", time.Now())
	modifyImageAttribute, err := ec2.ModifyImageAttribute(ctx, ec2_client, id, attribute, launch_permission)
	if err != nil {
		return nil, err
	}
	return modifyImageAttribute, nil
}

func Ec2CreateTags(ctx context.Context, ec2_client *ec2.Client, resources []string, tags []types.Tag) (*awsec2.CreateTagsOutput, error) {
	defer metrics.RecordJarvisAws("ec2", "Ec2CreateTags", "ok", time.Now())
	return ec2.Ec2CreateTags(ctx, ec2_client, resources, tags)
}

func (jarvisAws *AwsService) GetProdAmiStatus(ctx context.Context, imageId *string, ec2_client *ec2.Client) string {

	launch_permission := "launchPermission"
	permission_list, _ := GetLaunchPermissions(ctx, ec2_client, imageId, &launch_permission)
	amiSharedStatus := "false"
	for _, account := range permission_list {
		temp, err := strconv.ParseInt(*account.UserId, 10, 64)
		if err == nil {
			if jarvisAws.conf.ProdAwsAccount == int(temp) {
				amiSharedStatus = "true"
			}
		}
	}
	return amiSharedStatus
}

func (jarvisAws *AwsService) ServiceBuildInfo(service_name string, buildId int, env, tenant string) (map[string]interface{}, string, error) {

	final := make(map[string]interface{})
	var result string

	logger.InfoNoCtx("fetching Jenkins build info using the HTTP APIs..")
	if cfg.IsProdEnv(env) {
		logger.InfoNoCtx("cannot fetch jenkins build info for prod env")
		return nil, "", errors.New("cannot fetch jenkins build info for prod env")
	}
	// TODO(Sundeep): Update this path once the jenkins job is moved to the appropriate path
	jenkinsDirPath := service_name
	url_name := jarvisAws.conf.JenkinsUrlDeploy + env + "/job/" + jenkinsDirPath + "/" + strconv.Itoa(buildId) + "/api/json"

	client := http.Client{Timeout: 5 * time.Second}
	r, err := http.NewRequest(http.MethodGet, url_name, nil)
	if err != nil {
		logger.ErrorNoCtx("error forming new request", zap.Error(err))
		return nil, "", err
	}

	r.SetBasicAuth(jarvisAws.conf.JenkinsUsername, jarvisAws.conf.Secrets.Ids[config.JenkinsTokenNonProd])
	r.Header.Add("Content-Type", "application/json")

	res, err := client.Do(r)
	if err != nil {
		logger.ErrorNoCtx("error performing jenkins request", zap.Error(err))
		return nil, "", err
	}
	defer func() {
		err = res.Body.Close()
		if err != nil {
			logger.ErrorNoCtx("failed to close response body", zap.Error(err))
			return
		}
	}()

	logger.InfoNoCtx(
		"http response received from jenkins",
		zap.String("http_status", res.Status),
		zap.Int("http_status_code", res.StatusCode),
	)

	var jsonRespBody BuildResponse

	switch res.StatusCode {
	case 200:
		logger.InfoNoCtx("http response received is valid")
		resBody, err := io.ReadAll(res.Body)
		if err != nil {
			logger.ErrorNoCtx("failed to read response body", zap.Error(err))
			return nil, "", err
		}

		err = json.Unmarshal([]byte(resBody), &jsonRespBody)
		if err != nil {
			logger.ErrorNoCtx("failed to unmarshal response", zap.Error(err))
			return nil, "", err
		}
	case 404:
		// 404 case:
		logger.ErrorNoCtx("http response code indicates: job not found", zap.String("jenkins_url", url_name))
		return nil, "", errors.New("jenkins job not found")
	default:
		logger.ErrorNoCtx("http response received is invalid", zap.String("jenkins_url", url_name))
		return nil, "", errors.New("invalid http response from jenkins")
	}

	logger.InfoNoCtx("ami: service_build_info jenkins info fetched")

	releaseMessage := make(map[string]string)
	final["fullDisplayName"] = jsonRespBody.FullDisplayName
	final["buildUrl"] = jsonRespBody.URL
	final["buildId"] = jsonRespBody.ID
	final["result"] = jsonRespBody.Result
	result = jsonRespBody.Result
	commit_url := ""
	commit_id := ""
	org, repo, err := jarvisAws.getRepo(env, service_name)
	if err != nil {
		logger.ErrorNoCtx("error getting repo", zap.Error(err))
		return nil, "", err
	}
	remoteUrl := "https://github.com/" + org + "/" + repo + ".git"
	logger.InfoNoCtx(remoteUrl)
	final["timestamp"] = jsonRespBody.Timestamp

	user_data := make([]map[string]interface{}, 0)
	for _, item := range jsonRespBody.Actions {
		if item.Class == "hudson.model.ParametersAction" && item.Parameters != nil {
			for _, param := range item.Parameters {
				var value interface{}
				if err := json.Unmarshal(param.Value, &value); err != nil {
					logger.ErrorNoCtx("Error unmarshalling:", zap.Error(err))
					continue
				}

				switch v := value.(type) {
				case bool:
					final[param.Name] = v
				case string:
					final[param.Name] = v
				default:
					logger.InfoNoCtx("Unhandled type:", zap.Any("type", v))
				}
			}
		}
		if item.Class == "hudson.model.CauseAction" && item.Causes != nil {
			type temp map[string]interface{}
			final["user_info"] = make([]temp, 10)
			for _, cause := range item.Causes {
				user_data = append(user_data, map[string]interface{}{"user_id": cause.UserID, "user_name": cause.UserName})
			}
		}
		if item.Class == "hudson.plugins.git.util.BuildData" && item.RemoteUrls != nil {
			for _, url := range item.RemoteUrls {
				if url == remoteUrl {
					branches := item.BuildsByBranchName
					buildId_int64, _ := strconv.ParseInt(final["buildId"].(string), 10, 64)
					for key, _ := range branches {
						if branches[key].BuildNumber == int(buildId_int64) {
							commit_id = branches[key].Marked.Sha1
						}
					}
				}
			}
		}
	}
	final["user_info"] = user_data
	final["commit_id"] = commit_id
	commit_url, err = jarvisAws.CreateValidCommitUrl(env, service_name, commit_id)
	if err != nil {
		logger.ErrorNoCtx("failed to determine commit url, continuing..", zap.Error(err))
	}
	final["commit_url"] = commit_url

	for _, changeSets := range jsonRespBody.ChangeSets {
		first_commit_id := changeSets.Items[0].CommitID
		commit_url, err = jarvisAws.CreateValidCommitUrl(env, service_name, first_commit_id)
		if err != nil {
			logger.ErrorNoCtx("failed to determine commit url, continuing..", zap.Error(err))
		}
		commit_url_response := commit_url
		if commit_url_response != "Wrong Commit" {
			if final["commit_url"] == "Wrong Commit" {
				final["commit_url"] = commit_url_response
			}
			for _, messages := range changeSets.Items {
				releaseMessage[messages.CommitID] = messages.Msg
			}
		}
		if len(releaseMessage) > 5 {
			break
		}
		final["releaseMessage"] = releaseMessage
	}
	return final, result, nil
}

type AMIMetaData struct {
	buildId       int
	version       string
	shared_from   string
	deployId      int
	base_ami_id   string
	base_ami_name string
	imageEnv      string
	isServergen   string
	healthy       string
	tenant        string
}

func (jarvisAws *AwsService) GetAmiMetadata(ami_data *types.Image) (*AMIMetaData, error) {
	metadata := &AMIMetaData{
		buildId:       -1,
		deployId:      -1,
		version:       "_",
		shared_from:   "",
		base_ami_id:   "",
		base_ami_name: "",
		imageEnv:      "",
		isServergen:   "false",
		healthy:       "false",
		tenant:        string(cfg.EpifiTechTenant),
	}

	image_tags := ami_data.Tags
	for _, tag := range image_tags {
		switch *tag.Key {
		case "BUILD_REF":
			temp, err := strconv.ParseInt(*tag.Value, 10, 64)
			if err == nil {
				metadata.buildId = int(temp)
			}
		case "version":
			metadata.version = *tag.Value
		case "SHARED_FROM":
			metadata.shared_from = *tag.Value
		case "Base_AMI_ID":
			metadata.base_ami_id = *tag.Value
		case "Base_AMI_Name":
			metadata.base_ami_name = *tag.Value
		case "origin":
			metadata.imageEnv = *tag.Value
		case "isServergen":
			metadata.isServergen = *tag.Value
		case "healthy":
			metadata.healthy = *tag.Value
		case "DEPLOY_REF":
			temp, err := strconv.ParseInt(*tag.Value, 10, 64)
			if err == nil {
				metadata.deployId = int(temp)
			}
		case "tenant":
			metadata.tenant = *tag.Value
		}
	}
	return metadata, nil
}

func (jarvisAws *AwsService) GetAmiTag(ami_data *types.Image, tag_key string) string {

	var val string
	image_tags := ami_data.Tags
	for _, tag := range image_tags {
		if *tag.Key == tag_key {
			val = *tag.Value
		}
	}
	return val
}

func (jarvisAws *AwsService) GetAmiMetadataByImageId(ctx context.Context, ami_id *string, ec2_client *ec2.Client, env string) (int, string, string, int, string, string, string, string, string, error) {

	buildId := -1
	deployId := -1
	version := "_"
	shared_from := ""
	base_ami_id := ""
	base_ami_name := ""
	imageEnv := ""
	isServergen := "false"
	healthy := "false"

	ami_id_list := []string{
		*ami_id,
	}
	response, err := GetEc2Images(ctx, ec2_client, ami_id_list)
	if err != nil {
		logger.ErrorNoCtx("Error getting ec2 images ", zap.Error(err))
		return -1, "", "", -1, "", "", "", "", "", err
	}
	if len(response) != 0 {
		image_tags := response[0].Tags
		for _, tag := range image_tags {
			switch *tag.Key {
			case "BUILD_REF":
				temp, err := strconv.ParseInt(*tag.Value, 10, 64)
				if err == nil {
					buildId = int(temp)
				}
			case "version":
				version = *tag.Value
			case "SHARED_FROM":
				shared_from = *tag.Value
			case "Base_AMI_ID":
				base_ami_id = *tag.Value
			case "Base_AMI_Name":
				base_ami_name = *tag.Value
			case "origin":
				imageEnv = *tag.Value
			case "isServergen":
				isServergen = *tag.Value
			case "healthy":
				healthy = *tag.Value
			case "DEPLOY_REF_" + env:
				temp, err := strconv.ParseInt(*tag.Value, 10, 64)
				if err == nil {
					deployId = int(temp)
				}
			}
		}
	}
	return buildId, version, shared_from, deployId, base_ami_id, base_ami_name, imageEnv, isServergen, healthy, nil
}

func (jarvisAws *AwsService) GetImagesByTag(ctx context.Context, tagkey string, tagvalue string) ([]types.Image, error) {

	var client *ec2.Client
	var err error
	if jarvisAws.conf.Application.Environment == "deploy" {
		client, err = ec2.InitEC2Client(jarvisAws.awsConfig)
		if err != nil {
			logger.ErrorNoCtx("failed to initialize client", zap.Error(err))
			return nil, err
		}
	} else {
		creds := jarvisAws.GetAwsCreds(string(cfg.EpifiTechTenant), "deploy")
		client, err = ec2.InitEC2ClientWithConfig(jarvisAws.awsConfig, &creds)
		if err != nil {
			logger.ErrorNoCtx("failed to initialize client", zap.Error(err))
			return nil, err
		}
	}
	deploy_aws_account_list := []string{
		strconv.Itoa(jarvisAws.conf.DeployAwsAccount),
	}
	var filter []types.Filter
	tag_key2 := "tag:" + tagkey
	tag_value_list := []string{tagvalue}
	filter1 := types.Filter{
		Name:   &tag_key2,
		Values: tag_value_list,
	}
	filter = append(filter, filter1)
	amis, err := GetEc2ImagesWithFilters(ctx, client, filter, deploy_aws_account_list)
	if err != nil {
		logger.ErrorNoCtx("Error getting ec2 images with filter", zap.Error(err))
		return nil, err
	}

	// TODO: sort amis

	return amis, nil
}

// GetAmiInfo retrieves metadata and image_id about a specified Image.
func (jarvisAws *AwsService) GetAmiInfo(ami_data *types.Image, service_name string) (map[string]interface{}, string, error) {

	var err error
	image_id := ami_data.ImageId
	amiMetadataMap := make(map[string]interface{})
	metadata, err := jarvisAws.GetAmiMetadata(ami_data)
	if err != nil {
		return nil, "", errors.Wrap(err, "unable get AMI metadata")
	}
	commitId := strings.Split(metadata.version, "_")[0]
	branch := strings.Split(metadata.version, "_")[1]
	amiMetadataMap["share_key"] = jarvisAws.GetAmiTag(ami_data, "SHARE_KEY")
	tags_data := make(map[string]interface{}, 0)
	image_tags := ami_data.Tags
	for _, tag := range image_tags {
		tags_data[*tag.Key] = *tag.Value
	}
	amiMetadataMap["amiTags"] = tags_data
	if jarvisAws.conf.Application.Environment != "deploy" && metadata.shared_from == "" {
		return nil, "", nil
	}
	amiMetadataMap["shared_from"] = metadata.shared_from
	amiMetadataMap["env"] = metadata.imageEnv
	amiMetadataMap["isServergen"] = metadata.isServergen
	amiMetadataMap["baseAmiId"] = metadata.base_ami_id
	amiMetadataMap["AmiName"] = *image_id
	amiMetadataMap["healthy"] = metadata.healthy
	amiMetadataMap["baseAmiName"] = metadata.base_ami_name
	amiMetadataMap["buildId"] = metadata.buildId
	amiMetadataMap["commitId"] = commitId
	amiMetadataMap["branch"] = branch
	amiMetadataMap["CreationDate"] = ami_data.CreationDate
	amiMetadataMap["jenkins"], _, err = jarvisAws.ServiceBuildInfo(service_name, metadata.buildId, metadata.imageEnv, metadata.tenant)
	if err != nil {
		logger.ErrorNoCtx("error while getting service build info. continuing..", zap.Error(err))
	}
	amiMetadataMap["commitUrl"], err = jarvisAws.CreateValidCommitUrl(metadata.imageEnv, service_name, commitId)
	if err != nil {
		logger.ErrorNoCtx("failed to determine commit url", zap.Error(err))
	}
	if amiMetadataMap["healthy"] == "true" && jarvisAws.isQaAmi(service_name, metadata.imageEnv) {
		amiMetadataMap["imageShareable"] = "true"
	} else {
		amiMetadataMap["imageShareable"] = "false"
	}
	amiMetadataMap["tenant"] = metadata.tenant

	return amiMetadataMap, *image_id, nil
}

func (jarvisAws *AwsService) isQaAmi(serviceName string, envName string) bool {

	// if its a cx service, don't check for servergen & qa env
	if serviceName == "web" || serviceName == "sherlock" {
		return true
	}
	if envName == "qa" {
		return true
	}
	return false
}

func (jarvisAws *AwsService) getProdAwsAccountStrForTenant(tenant string) string {
	prod_aws_account_string := strconv.Itoa(jarvisAws.conf.ProdAwsAccount)
	if tenant == string(cfg.EpifiCapTenant) {
		prod_aws_account_string = strconv.Itoa(jarvisAws.conf.StockGuardianProdAwsAccount)
	}
	return prod_aws_account_string
}

func (jarvisAws *AwsService) ShareAmiWithProd(ctx context.Context, imageId string, env string, share_key string) (string, error) {
	status := "Failed"
	if jarvisAws.conf.Application.Environment == "prod" {
		logger.InfoNoCtx("Share to prod cannot be used in prod")
		return status, nil
	}

	status = "Cannot Find AMI"
	ec2_client, err := ec2.InitEC2Client(jarvisAws.awsConfig)
	if err != nil {
		logger.ErrorNoCtx("failed to initialize EC2 client", zap.Error(err))
		return "", err
	}

	ami_id_list := []string{
		imageId,
	}
	ami_data_list, err := GetEc2Images(ctx, ec2_client, ami_id_list)
	if err != nil {
		logger.ErrorNoCtx("Error getting ec2 images", zap.Error(err))
		return "", err
	}
	if len(ami_data_list) == 0 {
		logger.InfoNoCtx("Cannot find ami for given ID")
		return status, nil
	}
	ami_data := ami_data_list[0]
	status = "Cannot Share this AMI"

	// get ami tags
	serviceName := jarvisAws.GetAmiTag(&ami_data, "serviceName")
	buildId := jarvisAws.GetAmiTag(&ami_data, "BUILD_REF")
	envName := jarvisAws.GetAmiTag(&ami_data, "origin")
	tenant := jarvisAws.GetAmiTag(&ami_data, "tenant")
	branch := jarvisAws.GetAmiTag(&ami_data, "branch")
	isServergen := jarvisAws.GetAmiTag(&ami_data, "isServergen")

	buildId_int64, err := strconv.ParseInt(buildId, 10, 64)
	if err != nil {
		logger.ErrorNoCtx("Error parsing buikd ID", zap.Error(err))
		return "", err
	}
	_, buildResult, err := jarvisAws.ServiceBuildInfo(serviceName, int(buildId_int64), env, tenant)
	if err != nil {
		logger.ErrorNoCtx("error while getting service build info", zap.Error(err))
		return "", err
	}

	isProdBuildOk, err := jarvisAws.isProdBuild(serviceName, &ami_data)
	if err != nil {
		return "", err
	}
	if buildResult == "SUCCESS" && jarvisAws.isQaAmi(serviceName, envName) && isProdBuildOk {
		status = "Failed"
		attribute := "launchPermission"

		prod_aws_account_string := jarvisAws.getProdAwsAccountStrForTenant(tenant)
		launchPermission := types.LaunchPermission{
			UserId: &prod_aws_account_string,
		}
		launchPermission_list := []types.LaunchPermission{
			launchPermission,
		}
		launch_permission := types.LaunchPermissionModifications{
			Add: launchPermission_list,
		}
		_, err = ModifyImageAttribute(ctx, ec2_client, &imageId, &attribute, &launch_permission)
		if err != nil {
			return status, nil
		}
		is_tag_added, _ := jarvisAws.AmiUpdateTag(ctx, imageId, env, share_key)
		if is_tag_added {
			status = "Shared"
		}
		imageTags := map[string]string{
			"branch":      branch,
			"tenant":      tenant,
			"serviceName": serviceName,
			"isServergen": isServergen,
		}

		err = jarvisAws.addAmiTagInProd(ctx, imageTags, imageId, tenant)
		if err != nil {
			logger.ErrorNoCtx("error while adding tags in prod", zap.Error(err))
			return "", err
		}
	}
	return status, nil
}

// ShareAmiWithProdV2 shares the specified AMI to the prod environment and returns the status as either "Shared" or "Failed".
func (jarvisAws *AwsService) ShareAmiWithProdV2(ctx context.Context, imageId string, env string, share_key string) (string, error) {
	status := "Cannot Find AMI"
	var ec2_client *ec2.Client
	var err error
	switch jarvisAws.conf.Application.Environment {
	case "prod":
		logger.InfoNoCtx("Share to prod cannot be used in prod")
		return "Failed", nil
	case "deploy", "local-prod":
		ec2_client, err = ec2.InitEC2Client(jarvisAws.awsConfig)
		if err != nil {
			logger.ErrorNoCtx("failed to initialize EC2 client", zap.Error(err))
			return "", err
		}
	default:
		ec2_client, err = ec2.InitEC2Client(jarvisAws.awsConfig)
		if err != nil {
			logger.ErrorNoCtx("failed to initialize EC2 client", zap.Error(err))
			return "", err
		}
	}

	ami_id_list := []string{
		imageId,
	}
	ami_data_list, err := GetEc2Images(ctx, ec2_client, ami_id_list)
	if err != nil {
		logger.ErrorNoCtx("Error getting ec2 images", zap.Error(err))
		return "", err
	}
	if len(ami_data_list) == 0 {
		logger.InfoNoCtx("Cannot find ami for given ID %s", zap.String("ami_id", imageId))
		return status, nil
	}
	ami_data := ami_data_list[0]
	serviceName := jarvisAws.GetAmiTag(&ami_data, "serviceName")
	envName := jarvisAws.GetAmiTag(&ami_data, "origin")
	tenant := jarvisAws.GetAmiTag(&ami_data, "tenant")
	branch := jarvisAws.GetAmiTag(&ami_data, "branch")
	isServergen := jarvisAws.GetAmiTag(&ami_data, "isServergen")
	status = "Cannot Share this AMI"

	// get ami tags
	healthStatus := jarvisAws.GetAmiTag(&ami_data, "healthy")

	isProdBuildOk, err := jarvisAws.isProdBuild(serviceName, &ami_data)
	if err != nil {
		return "", err
	}
	if healthStatus == "true" && jarvisAws.isQaAmi(serviceName, envName) && isProdBuildOk {
		status = "Failed"
		if jarvisAws.conf.Application.Environment == "prod" {
			logger.InfoNoCtx("Share to prod cannot be used in prod")
			return status, errors.New("Cannot share the ami")
		}
		ec2_client, _ = ec2.InitEC2Client(jarvisAws.awsConfig)
		attribute := "launchPermission"
		prod_aws_account_string := jarvisAws.getProdAwsAccountStrForTenant(tenant)
		launchPermission := types.LaunchPermission{
			UserId: &prod_aws_account_string,
		}
		launchPermission_list := []types.LaunchPermission{
			launchPermission,
		}
		launch_permission := types.LaunchPermissionModifications{
			Add: launchPermission_list,
		}
		_, err := ModifyImageAttribute(ctx, ec2_client, &imageId, &attribute, &launch_permission)
		if err != nil {
			return status, nil
		}
		is_tag_added, err := jarvisAws.AmiUpdateTag(ctx, imageId, env, share_key)
		if err != nil {
			logger.ErrorNoCtx("error while updating ami tags", zap.Error(err))
			return "", err
		}
		if is_tag_added {
			status = "Shared"
		}
		imageTags := map[string]string{
			"branch":      branch,
			"tenant":      tenant,
			"serviceName": serviceName,
			"isServergen": isServergen,
		}

		err = jarvisAws.addAmiTagInProd(ctx, imageTags, imageId, tenant)
		if err != nil {
			logger.ErrorNoCtx("error while adding tags in prod", zap.Error(err))
			return "", err
		}
	}
	return status, nil
}

func (jarvisAws *AwsService) AmiUpdateTag(ctx context.Context, imageId string, env string, share_key string) (bool, error) {

	ec2_client, _ := ec2.InitEC2Client(jarvisAws.awsConfig)
	ami_id_list := []string{
		imageId,
	}
	response, err := GetEc2Images(ctx, ec2_client, ami_id_list)
	if err != nil {
		logger.ErrorNoCtx("error while getting ec2 images", zap.Error(err))
		return false, err
	}
	image_tags := response[0].Tags
	env_tag := env
	for _, tag := range image_tags {
		if *tag.Key == "SHARED_FROM" {
			tags := strings.Split(*tag.Value, ",")
			env_tag = *tag.Value + "," + env
			for _, temp := range tags {
				if env == temp {
					env_tag = *tag.Value
				}
			}
		}
	}
	resources := []string{
		imageId,
	}
	if share_key != "" {
		tags := []types.Tag{
			{
				Key:   aws.String("SHARED_FROM"),
				Value: aws.String(env_tag),
			},
			{
				Key:   aws.String("SHARE_KEY"),
				Value: aws.String(share_key),
			},
		}
		_, err = ec2.Ec2CreateTags(ctx, ec2_client, resources, tags)
	} else {
		tags := []types.Tag{
			{
				Key:   aws.String("SHARED_FROM"),
				Value: aws.String(env_tag),
			},
		}
		_, err = Ec2CreateTags(ctx, ec2_client, resources, tags)
	}
	if err == nil {
		return true, nil
	} else {
		logger.ErrorNoCtx("error while creating ec2 tags", zap.Error(err))
		return false, err
	}
}

// getTenantAndEnvForAccount maintains all the mapping of aws account to corresponding environment.
func (jarvisAws *AwsService) getTenant(env, service_name string) (string, error) {
	if !jarvisAws.isGolangEc2Service(service_name) {
		// as of now, all non golang services are in epifi tech tenant.
		return string(cfg.EpifiTechTenant), nil
	}
	envConf, exist := jarvisAws.envServergenConfig[env]
	if !exist {
		return "", fmt.Errorf("environment %q not found", env)
	}
	serverConf, ok := envConf.Servers[service_name]
	if !ok {
		return "", fmt.Errorf("service %q not found in env %q", service_name, env)
	}
	return string(serverConf.DeploymentAccountAlias), nil
}

func (jarvisAws *AwsService) GetDeployedAmiMetadata(ctx context.Context, env string, app string) ([]map[string]interface{}, error) {
	tenant, err := jarvisAws.getTenant(env, app)
	if err != nil {
		logger.ErrorNoCtx("error while getting environment for account", zap.String("env", env), zap.Error(err))
		return nil, err
	}

	logger.InfoNoCtx("asg: fetching metadata")
	var ec2_client *ec2.Client
	var autoscaling_client *autoscaling.Client
	switch {
	case jarvisAws.conf.Application.Environment == "prod" && (env == "qa" || env == "demo" || env == "staging" || env == "uat" || env == "prod"):
		ec2_client, err = ec2.InitEC2Client(jarvisAws.awsConfig)
		if err != nil {
			logger.ErrorNoCtx("failed to initialize EC2 client", zap.Error(err))
			return nil, err
		}
		autoscaling_client, err = autoscaling.InitAutoScalingClient(jarvisAws.awsConfig)
		if err != nil {
			logger.ErrorNoCtx("failed to initialize ASG client", zap.Error(err))
			return nil, err
		}
	default:
		creds := jarvisAws.GetAwsCreds(tenant, env)
		ec2_client, err = ec2.InitEC2ClientWithConfig(jarvisAws.awsConfig, &creds)
		if err != nil {
			logger.ErrorNoCtx("failed to initialize EC2 client", zap.Error(err))
			return nil, err
		}
		autoscaling_client, err = autoscaling.InitAutoScalingClientWithConfig(jarvisAws.awsConfig, &creds)
		if err != nil {
			logger.ErrorNoCtx("failed to initialize ASG client", zap.Error(err))
			return nil, err
		}
	}

	green_asg := env + "-" + app + "-green"
	blue_asg := env + "-" + app + "-blue"
	var asg_names []string
	var version string
	asg_names = append(asg_names, green_asg, blue_asg)

	var boto_ec2_client *ec2.Client
	switch jarvisAws.conf.Application.Environment {
	case "prod":
		if env == "qa" || env == "demo" || env == "staging" || env == "uat" || env == "prod" {
			boto_ec2_client, err = ec2.InitEC2Client(jarvisAws.awsConfig)
			if err != nil {
				logger.ErrorNoCtx("failed to initialize EC2 client", zap.Error(err))
				return nil, err
			}
		} else {
			creds := jarvisAws.GetAwsCreds(tenant, env)
			boto_ec2_client, err = ec2.InitEC2ClientWithConfig(jarvisAws.awsConfig, &creds)
			if err != nil {
				logger.ErrorNoCtx("failed to initialize EC2 client", zap.Error(err))
				return nil, err
			}
		}
	case "deploy", "local-prod":
		boto_ec2_client, err = ec2.InitEC2Client(jarvisAws.awsConfig)
		if err != nil {
			logger.ErrorNoCtx("failed to initialize EC2 client", zap.Error(err))
			return nil, err
		}
	default:
		boto_ec2_client, err = ec2.InitEC2Client(jarvisAws.awsConfig)
		if err != nil {
			logger.ErrorNoCtx("failed to initialize EC2 client", zap.Error(err))
			return nil, err
		}
	}
	asgs := make([]map[string]interface{}, 0)
	if app == "test" {
		return asgs, nil
	}
	for _, asg_name := range asg_names {
		asg_list := []string{
			asg_name,
		}
		response_asg, err := GetAutoScalingGroups(ctx, autoscaling_client, asg_list)
		if err != nil {
			logger.ErrorNoCtx("error while getting auto scaling groups images", zap.Error(err))
			return nil, err
		}
		if len(response_asg) == 0 {
			continue
		}
		response_asg_new := response_asg[0]

		// Get launch template information using the utility function
		ltInfo, err := GetLaunchTemplateInfo(ctx, response_asg_new, ec2_client)
		if err != nil {
			logger.ErrorNoCtx("error getting launch template info", zap.Error(err))
			return nil, err
		}

		data := make(map[string]interface{})
		data["ImageId_latest"] = ltInfo.ImageID
		data["ImageId"] = ltInfo.ImageID
		data["InstanceType"] = ltInfo.InstanceType
		data["env"] = env

		data["AutoScalingGroupName"] = response_asg_new.AutoScalingGroupName
		data["AutoScalingGroupARN"] = response_asg_new.AutoScalingGroupARN
		data["LaunchTemplate"] = response_asg_new.LaunchTemplate
		data["MinSize"] = response_asg_new.MinSize
		data["MaxSize"] = response_asg_new.MaxSize
		data["DesiredCapacity"] = response_asg_new.DesiredCapacity
		data["DefaultCooldown"] = response_asg_new.DefaultCooldown
		data["AvailabilityZones"] = response_asg_new.AvailabilityZones
		data["LoadBalancerNames"] = response_asg_new.LoadBalancerNames
		data["TargetGroupARNs"] = response_asg_new.TargetGroupARNs
		data["HealthCheckType"] = response_asg_new.HealthCheckType
		data["HealthCheckGracePeriod"] = response_asg_new.HealthCheckGracePeriod
		data["Instances"] = response_asg_new.Instances
		data["CreatedTime"] = response_asg_new.CreatedTime
		data["SuspendedProcesses"] = response_asg_new.SuspendedProcesses
		data["VPCZoneIdentifier"] = response_asg_new.VPCZoneIdentifier
		data["EnabledMetrics"] = response_asg_new.EnabledMetrics
		data["Tags"] = response_asg_new.Tags
		data["TerminationPolicies"] = response_asg_new.TerminationPolicies
		data["NewInstancesProtectedFromScaleIn"] = response_asg_new.NewInstancesProtectedFromScaleIn
		data["ServiceLinkedRoleARN"] = response_asg_new.ServiceLinkedRoleARN
		data["TrafficSources"] = response_asg_new.TrafficSources
		data["TerminationPolicies"] = response_asg_new.TerminationPolicies

		if ltInfo.ImageID == nil {
			logger.ErrorNoCtx("ImageID is nil in launch template info")
			return nil, fmt.Errorf("ImageID is nil in launch template info")
		}

		data["buildId"], version, data["shared_from"], data["deployId"], data["baseAmiId"], data["baseAmiName"], data["imageEnv"], data["isServergen"], data["healthy"], err = jarvisAws.GetAmiMetadataByImageId(ctx, ltInfo.ImageID, boto_ec2_client, env)
		if err != nil {
			logger.ErrorNoCtx("error while getting ami metadata by imageId", zap.Error(err))
			return nil, err
		}
		_, result, err := jarvisAws.ServiceBuildInfo(app, data["buildId"].(int), env, tenant)
		if err != nil {
			logger.ErrorNoCtx("error while getting service build info", zap.Error(err))
			return nil, err
		}
		if result == "SUCCESS" && jarvisAws.isQaAmi(app, data["imageEnv"].(string)) {
			data["image_shared_with_prod"] = jarvisAws.GetProdAmiStatus(ctx, ltInfo.ImageID, boto_ec2_client)
		} else {
			data["image_shared_with_prod"] = "Cannot be shared"
		}
		data["version"] = version
		data["commitId"] = strings.Split(version, "_")[0]
		data["branch"] = strings.Split(version, "_")[1]
		asgs = append(asgs, data)
	}
	return asgs, nil
}

func (jarvisAws *AwsService) addAmiTagInProd(ctx context.Context, tags map[string]string, imageId, tenant string) error {
	roleArn, exist := jarvisAws.conf.TenantEC2TagManagerRoleArnMap[tenant]
	if !exist {
		return errors.New(fmt.Sprintf("role arn not found for %s", tenant))
	}
	ec2Client, err := ec2.InitEC2ClientAssumingRole(jarvisAws.awsConfig, roleArn)
	if err != nil {
		logger.ErrorNoCtx("error while initializing ec2 client", zap.Error(err))
		return err
	}
	imageTags := convertMapToEc2Tags(tags)

	_, err = ec2.Ec2CreateTags(ctx, ec2Client, []string{imageId}, imageTags)
	if err != nil {
		logger.ErrorNoCtx("error while creating tags in prod", zap.Error(err))
		return err
	}
	return nil
}

func convertMapToEc2Tags(imageTags map[string]string) []types.Tag {
	tags := make([]types.Tag, 0)
	for key, value := range imageTags {
		tags = append(tags, types.Tag{
			Key:   aws.String(key),
			Value: aws.String(value),
		})
	}
	return tags
}

func (jarvisAws *AwsService) isProdBuild(serviceName string, ami_data *types.Image) (bool, error) {
	if serviceName == "web" {
		isProdBuild := jarvisAws.GetAmiTag(ami_data, "IS_PROD_BUILD")

		if isProdBuild != "true" {
			return false, fmt.Errorf("Cannot share 'web' AMI to prod: IS_PROD_BUILD tag is not true (current value: %s) for imageId: %s", isProdBuild, *ami_data.ImageId)
		}
	}
	return true, nil
}
