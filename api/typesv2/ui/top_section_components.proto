syntax = "proto3";

package api.typesv2.ui;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/deeplink_screen_option/preapprovedloans/components.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui";
option java_package = "com.github.epifi.gamma.api.typesv2.ui";

// UI element that contains a visual Element, title, and subtitle fields in vertical order
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-64946&t=IGmJANLtILqYrUaN-4
message TopSection {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text subtitle = 2;
  api.typesv2.common.VisualElement image = 3;
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 4;
  repeated api.typesv2.deeplink_screen_option.preapprovedloans.ProgressVisualisation progress_visualisation = 5;
}

// UI element that contains a ITC and scrolls vertically
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-64954&t=IGmJANLtILqYrUaN-4
message USPCarouselComponent {
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
  repeated api.typesv2.ui.IconTextComponent usp_messages = 2;
  bool hide_indicators = 3;
}
