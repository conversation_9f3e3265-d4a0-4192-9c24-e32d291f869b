syntax = "proto3";

package api.typesv2.deeplink_screen_option.preapprovedloans;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/preapprovedloan/pal_enums/enums.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/preapprovedloans/enums.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "google/protobuf/any.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.preapprovedloans";


// LoansScreenUiComponents contains common components that can be used across different loans screen.
// List of these components can be used to create the complete screen.
message LoansScreenUiComponents {
  oneof component {
    ProductFeaturesStackedViewComponent product_features_stacked_view_component = 1;
    ProductFeaturesScrollableCardsViewComponent product_features_scrollable_cards_view_component = 2;
    FaqsScrollableCardsViewComponent faqs_scrollable_cards_view_component = 3;
    ProductIntroViewComponent product_intro_view_component = 4;
    LoanOfferTicketViewComponent loan_offer_ticket_view_component = 5;
    ClickableTileComponent clickable_tile_component = 6;
    AddAccountComponent add_account_component = 7;
    VisualElementComponent visual_element = 8;
    IconTextWithMarginComponent icon_texts_component = 9;
    VerticalItcListWithTopMarginComponent vertical_itc_list_with_top_margin_component = 10;
    FooterComponent footer_component = 11;
    HeaderInfoComponent header_info_component = 12;
    LoansRewardsComponent loans_rewards_component = 14;
    LoansAutoScrollBannerComponent loans_auto_scroll_banner_component = 15;
    LoanOfferCardComponent loan_offer_card_component = 16;
    ViewMoreOfferCardComponent view_more_offer_card_component = 17;
  }
}

// Single Vendor Multi-Offer flow - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=56007-58456&t=8rConJwvmGM4m7Sp-4
// Multi Vendor Multi-Offer flow - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=56140-24395&t=cdQvt4pt9O7BKGGi-4
message ViewMoreOfferCardComponent {
  typesv2.ui.VerticalKeyValuePair offer_description = 1;
  typesv2.common.VisualElement action_icon = 2;
  typesv2.common.ui.widget.BackgroundColour bg_color = 3;
}

// https://www.figma.com/design/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?node-id=18492-58437&t=EzswwQkda35aorxd-4
message PartnershipComponent {
  typesv2.common.Text title = 1;
  repeated typesv2.common.VisualElement partner_logos = 2;
}

// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=2402-54184&mode=design&t=Jk9pgIbXxoyAn0rm-4
message ProductFeaturesStackedViewComponent {
  typesv2.common.Text component_title = 1;
  repeated FeatureComponent features = 2;
  // top margin to be added with a component relative to above component on screen
  int32 top_margin = 3;
}

// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=2402-54200&mode=dev
message ProductFeaturesScrollableCardsViewComponent {
  typesv2.common.Text component_title = 1;
  repeated FeatureComponent features = 2;
  // top margin to be added with a component relative to above component on screen
  int32 top_margin = 3;
}

// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=2402-54219&mode=dev
message FaqsScrollableCardsViewComponent {
  typesv2.common.Text component_title = 1;
  repeated frontend.deeplink.InfoItemV3 faqs = 2;
  // top margin to be added with a component relative to above component on screen
  int32 top_margin = 3;
  // deeplink to redirect user to full list FAQs.
  typesv2.ui.IconTextComponent view_more = 4;
}

message ProductIntroViewComponent {
  frontend.deeplink.InfoItemV3 title = 1;
  typesv2.common.VisualElement logo = 2;
  int32 top_margin = 3;
}

// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=2402-54614&mode=dev
message LoanOfferTicketViewComponent {
  TicketBox offer_details_card = 1;
  // top margin to be added with a component relative to above component on screen
  int32 top_margin = 2;
  // lottie/image to be shown in background in the ticket
  typesv2.common.VisualElement bg_visual = 3;

  message TicketBox {
    typesv2.common.Text title = 1;
    // deprecated in favor of offer_amount_text field.
    typesv2.Money offer_amount = 2 [deprecated = true];
    // deprecated in favor of interest_section field.
    typesv2.common.Text interest = 3 [deprecated = true];
    // clickable field that will redirect user to learn_more screen.
    typesv2.ui.IconTextComponent learn_more_button = 4;
    typesv2.common.ui.widget.BackgroundColour ticket_box_bg_color = 5;
    LottieDetails lottie_details = 6;
    // animation type for lottie to be shown
    TextAnimationType animation = 7;
    typesv2.ui.IconTextComponent offer_amount_text = 8;
    typesv2.ui.IconTextComponent interest_section = 9;

    // starting frame and ending frame for lottie to be shown
    message LottieDetails {
      int32 start_frame = 1;
      int32 end_frame = 2;
    }
    // text animation on the lottie to be shown
    enum TextAnimationType {
      TEXT_ANIMATION_TYPE_UNSPECIFIED = 0;
      TEXT_ANIMATION_TYPE_BOTTOM_UP = 1;
    }
  }
}

// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=3618-5440&mode=design&t=zDEIJ3ibmtWj8Z1E-4
message ClickableTileComponent {
  typesv2.common.Text title = 1;
  typesv2.common.VisualElement primary_icon = 2;
  typesv2.common.VisualElement next_icon = 3;
  // screen deeplink to which user will navigate upon clicking on the "next" icon
  typesv2.ui.IconTextComponent cta = 4;
  int32 top_margin = 5;
}

message FeatureComponent {
  ui.IconTextComponent feature_text = 1;
  typesv2.common.VisualElement feature_icon = 2;
}

message AmountWithExpandableBreakupComponent {
  ui.IconTextComponent title = 1;
  // deprecated in favor of amount_text field
  typesv2.Money amount = 2 [deprecated = true];
  ui.IconTextComponent view_breakup_text = 3;
  Breakup breakup = 4;
  typesv2.ui.IconTextComponent amount_text = 5;

  message Breakup {
    repeated KeyValueRow items = 1;
  }
}

message KeyValueRow {
  typesv2.ui.IconTextComponent key = 1;
  typesv2.ui.IconTextComponent value = 2;
  string bg_color = 3;
  typesv2.ui.IconTextComponent tag = 4;
}

// KeyValueRowsComponent can be used to list key value pairs. It can have a header which is separated by a horizontal line. The whole component can also have a heading.
// Figma for reference: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-15163&mode=design&t=aJw6WUIHPZthdPaS-0
message KeyValueRowsComponent {
  typesv2.ui.IconTextComponent heading = 1;
  ItemList item_list = 2;
  // Header to show above all list of key value pairs. It will be seperated a horizontal line.
  KeyValueRow header = 3;
  // bg color for key value component
  typesv2.common.ui.widget.BackgroundColour bg_color = 4;

  message ItemList {
    repeated KeyValueRow items = 1;
    // to show a separator between the list items.
    bool show_separator = 2;
    // color of the separator
    string separator_color = 3;
  }
}

message KeyValueRowCollapsedState {
  typesv2.common.Text key = 1;
  typesv2.ui.IconTextComponent value = 2;
  typesv2.common.Image icon = 3;
}

message PrincipleSelectionComponent {
  typesv2.common.Text title = 1;
  typesv2.Money value = 2;
  typesv2.common.Text other_details = 3;
  typesv2.Money min_allowed = 4;
  typesv2.Money max_allowed = 5;
  typesv2.ui.IconTextComponent footer = 6;
  KeyValueRowCollapsedState collapsed_state = 7;
  // Color in #aarrggbb format
  string bg_color = 8;
  string border_color = 9;
}

message MutualFundSelectionComponent {
  typesv2.common.Text title = 1;
  typesv2.common.Text description_text = 2;
  typesv2.common.Text mutual_fund_title = 3;
  typesv2.common.Text value_title = 4;
  repeated MutualFund mutual_funds = 5;
  typesv2.ui.IconTextComponent footer = 6;
  KeyValueRowCollapsedState collapsed_state = 7;
  // Color in #aarrggbb format
  string bg_color = 8;
  string border_color = 9;

  message MutualFund {
    string isin = 1;
    typesv2.common.Text name = 2;
    typesv2.common.Image icon = 3;
    // deprecated in favor of `value_text` field
    typesv2.Money value = 4 [deprecated = true];
    typesv2.common.Text value_text = 7;
    bool selected = 5;
    double quantity = 6;
  }
}

message LoanTenureSelectionComponent {
  typesv2.common.Text title = 1;
  uint32 value = 2;
  uint32 min_allowed = 4;
  uint32 max_allowed = 5;
  repeated typesv2.ui.VerticalKeyValuePair other_details = 6;
  typesv2.ui.IconTextComponent footer = 7;
  KeyValueRowCollapsedState collapsed_state = 8;
  // Color in #aarrggbb format
  string bg_color = 9;
  string border_color = 10;
}

message DropdownSelectionOptionComponent {
  typesv2.common.Text title = 2;
  repeated Option options = 3;
  message Option {
    typesv2.common.Text display_text = 1;
    // value to be passed for the input to FE on selection
    string value = 2;
  }
}

// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=573-17300&mode=design&t=mInBSuWNBqaS3jIe-0
message HorizontalListComponent {
  typesv2.ui.IconTextComponent heading = 1;
  repeated typesv2.common.ui.widget.ImageTitleSubtitleElement columns = 2;
  typesv2.common.ui.widget.BackgroundColour bg_color = 3;
}

message BulletListComponent {
  // From IconTextComponent use left_visual_element (visual element) to show the preceding image
  // From IconTextComponent use texts ([]typesv2.common.Text[0]) to show the bullet point text
  repeated typesv2.ui.IconTextComponent bullet_points = 1;
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=30915-43149&mode=design&t=4UzdfUfGxpKzUweZ-4
message SectionTypeProgress {
  repeated SectionTypeProgressComponent progress_components = 1;
  message SectionTypeProgressComponent {
    typesv2.common.VisualElement icon = 1;
    typesv2.common.Text text = 2;
    // used to track finished steps, client can put dotted lines between two progress components for which is_completed != true, and all other components after that
    bool is_completed = 3;
    string follow_up_line_colour = 4;
  }
}

message ProgressVisualisation {
  int64 current_progress = 1;
  int64 max_progress = 2;
  int64 track_width = 3;
  int64 progress_bar_width = 4;
  typesv2.common.ui.widget.BackgroundColour track_color = 5;
  typesv2.common.ui.widget.BackgroundColour progress_bar_color = 6;
  ProgressVisualisationType visualisation_type = 7;
  oneof visualisation {
    SemiCircleProgressVisualisation semi_circle_progress_visualisation = 8;
    CircleProgressVisualisation circle_progress_visualisation = 9;
    LineProgressVisualisation line_progress_visualisation = 10;
  }
}

// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=447-10393&mode=design&t=hUQ1W9x7CV6BJTYF-0
message SemiCircleProgressVisualisation {
}

// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16392&mode=design&t=hUQ1W9x7CV6BJTYF-0
message CircleProgressVisualisation {
}

// LineProgressVisualisation is a visualisation which can be used to show progress on a line. The line has a gradient color.
// There is also label which is shown below the progress.
// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16458&mode=design&t=7vaja8dLnmbHqoOM-0
message LineProgressVisualisation {
  typesv2.ui.IconTextComponent progress_value = 1;
  int64 progress_bar_height = 2;
}

// Semi circle progress visualisation can be used to show progress on a semi circle as shown in the figma below.
// The progress line will have gradient color. The title and value will be showin within the semi circle.
// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16392&mode=design&t=7vaja8dLnmbHqoOM-0
message CardSemiCircularProgress {
  ProgressVisualisation progress_visualisation = 1;
  // title and value to show in the middle and bottom inside the semi circle
  typesv2.ui.IconTextComponent visualisation_title = 2;
  typesv2.ui.IconTextComponent visualisation_value = 3;
  // itc to represent last row
  typesv2.ui.IconTextComponent visualisation_tag = 4;
}

// Vertical visualisation can be used to show an image, a title and its description
// figma: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41121-22469&mode=design&t=PXOPRVc6BEJbzHDi-4
message CardVerticalImageTitleDesc {
  typesv2.ui.IconTextComponent visualisation_icon = 1;
  typesv2.ui.IconTextComponent visualisation_title = 2;
  typesv2.ui.IconTextComponent visualisation_description = 3;
}

// VisualisationLineProgressWithCta is a card which shows the progress on a line with gradient color.
// Above the line the card contains title and value and to the right of value is cta. It is shown in the figma.
// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16458&mode=design&t=7vaja8dLnmbHqoOM-0
message CardWithLineProgressAndCta {
  // title to show on top left of card
  typesv2.ui.IconTextComponent title = 1;
  // value to show below the card title
  typesv2.ui.IconTextComponent value = 2;
  // cta to show on the right of the value
  typesv2.ui.IconTextComponent cta = 3;
  // Line progress visualisation to show below the value and cta
  ProgressVisualisation progress_visualisation = 4;
}

// Example screen: Loan in processing card on lamf dashboard.
// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=447-10393&mode=design&t=7vaja8dLnmbHqoOM-0
message CardWithStageProgress {
  VerticalTitleValuePair vertical_title_value_pair = 1;
  typesv2.ui.IconTextComponent stage_progress_title = 2;
  SectionTypeProgress stage_progress = 3;
  // to show vendor icon and name
  // in reference with https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41234-54293&mode=design&t=PXOPRVc6BEJbzHDi-4
  typesv2.ui.IconTextComponent vendor_title_top_row = 4;
  // content list - https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=54466-46483&t=cfiBff7SU9PUgvS7-4
  CardWithMultiColumnsAndRows card_with_multi_columns_and_rows = 5;
}

// Example screen: Loans card showing emi progress in lamf dashboard
// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16392&mode=design&t=7vaja8dLnmbHqoOM-0
message CardWithCircleProgress {
  VerticalTitleValuePair vertical_title_value_pair_row_1 = 1;
  VerticalTitleValuePair vertical_title_value_pair_row_2 = 2;
  CtaRow cta_row = 3;
  ProgressVisualisation progress_visualisation = 4;
  // The title and value to show within the circle
  typesv2.ui.IconTextComponent visualisation_title = 5;
  typesv2.ui.IconTextComponent visualisation_value = 6;
  frontend.deeplink.Deeplink deeplink = 7;

  message CtaRow {
    typesv2.ui.IconTextComponent title = 1;
    typesv2.ui.IconTextComponent value = 2;
    typesv2.ui.IconTextComponent cta = 3;
    string bg_color = 4;
  }
}

// Example screen: Loans card showing emi progress in loans dashboard in linear manner
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41131-45011&mode=design&t=nWsv4Jyycg6F2JxP-4
message CardWithLineProgress {
  // banner top clickable row
  CtaRow cta_title_row = 1;
  // to show the card rows
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41131-45044&mode=design&t=PXOPRVc6BEJbzHDi-4
  repeated Row rows = 2;
  string bg_color = 3;
  // banners for prepay
  CtaRow cta_bottom_row = 4;
  // bottom banner to show stage progress
  BannerWithStageProgress banner_with_stage_progress = 5;

  message ColumnItem {
    // to show type of column item
    oneof Item {
      VerticalTitleValuePair vertical_title_value_pair = 1;
      LineProgressVisualiser line_progress_visualiser = 2;
    }
  }

  message Row {
    // to show 2 columns in a row
    ColumnItem left_item = 1;
    ColumnItem right_item = 2;
  }

  message LineProgressVisualiser {
    // title above progress bar
    typesv2.ui.IconTextComponent progress_visualisation_title = 1;
    // description along with the progress bar
    typesv2.ui.IconTextComponent progress_visualisation_value = 2;
    // meta data to load progress bar with
    ProgressVisualisation progress_visualisation = 3;
  }

  message BannerWithStageProgress {
    // banner bg_color
    string bg_color = 1;
    // banner title
    typesv2.ui.IconTextComponent title = 2;
    // stages and is completed data to show icon and line combination
    SectionTypeProgress section_type_progress = 3;
  }

  message CtaRow {
    // vendor logo and vendor text
    typesv2.ui.IconTextComponent title = 1;
    // cta text with deeplink
    typesv2.ui.IconTextComponent cta = 2;
    // top row color (if different)
    string bg_color = 3;
    // right most hamburger icon/text when on click opens list of clickable options
    // if list of display item is nil of list of options is zero, do not show anything
    // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=42652-15026&mode=design&t=VBD155dYlKlB7E7z-4
    HamburgerComponent right_hamburger_component = 4;
  }
}

// Example screen: Loan payments card in lamf loan details page
// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16458&mode=design&t=7vaja8dLnmbHqoOM-0
message CardWithCalenderProgress {
  repeated VerticalTitleValuePair vertical_title_value_pairs = 1;
  int32 num_cols = 2;
  CalenderTypeProgress calender_progress = 3;
  frontend.deeplink.Deeplink deeplink = 4;
}

message CalenderTypeProgress {
  repeated typesv2.ui.VerticalIconTextComponent steps = 1;
}

// CardWithMultiColumnsAndRowsDetails can be used to list some title and value pairs in a grid format. The pairs will start filing the grid from left to right.
// The the pair will move to new row once all the cols are filled for the row
// Example screen: loan details section in lamf loan details page.
// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16458&mode=design&t=7vaja8dLnmbHqoOM-0
message CardWithMultiColumnsAndRows {
  int32 num_cols = 1;
  repeated VerticalTitleValuePair vertical_title_value_pairs = 2;
  frontend.deeplink.Deeplink deeplink = 3;
}

message VerticalTitleValuePair {
  typesv2.ui.IconTextComponent title = 1;
  typesv2.ui.IconTextComponent value = 2;
  // if not "" (empty string), on click, copy this text value. If "" empty, no need to show icon or copy text
  string text_to_be_copied = 3;
  typesv2.common.Text.TextAlignment content_alignment = 4;
}

// Example screen: banner shown for setting up auto pay in lamf loans dashboard
// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=555-16572&mode=design&t=7vaja8dLnmbHqoOM-0
message BannerWithLeftIconAndBottomRightCta {
  typesv2.common.VisualElement left_icon = 1;
  typesv2.ui.IconTextComponent title = 2;
  // can be used both as description text and as description cta
  typesv2.ui.IconTextComponent description = 3;
  // if cta is nil, do not show the arrow and no on click action in that case
  typesv2.ui.IconTextComponent bottom_right_cta = 4;
  // if cta is nil, do not show the arrow and no on click action in that case
  typesv2.ui.IconTextComponent bottom_right_secondary_cta = 5;
  // if not nil, makes the whole banner clickable
  frontend.deeplink.Deeplink deeplink = 6;
  string bg_color = 7;
}

// Example screen: banner shown on dashboard screen to show new loan offer
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=43300-50221&mode=design&t=VBD155dYlKlB7E7z-4
message BannerWithRightIconAndBottomLeftCta {
  typesv2.common.VisualElement right_icon = 1;
  typesv2.ui.IconTextComponent title = 2;
  // can be used both as description text and as description cta
  typesv2.ui.IconTextComponent description = 3;
  // if cta nil, do not show the button, else make this clickable over the banner
  typesv2.ui.IconTextComponent bottom_left_cta = 4;
  // if not nil, makes the whole banner clickable
  frontend.deeplink.Deeplink deeplink = 5;
  string bg_color = 6;
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=74513-37774&t=teezM9IsbRCsVGlb-0
message BannerWithLeftIconAndRightCta {
  typesv2.ui.IconTextComponent title = 1;
  // can be used both as description text and as description cta
  typesv2.ui.IconTextComponent description = 2;
  // if cta is nil, do not show the arrow and no on click action in that case
  typesv2.ui.IconTextComponent right_cta = 3;
  typesv2.common.VisualElement left_icon = 4;
  // if true, shows the action tag on the top side of the banner
  bool top_action_tag = 5;
  string bg_color = 6;
}

// SingleColumnLineItemsDetails can be for listing rows which have icon, title and description.
// Example screen: More section in lamf loans dashboard.
// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=447-10393&mode=design&t=7vaja8dLnmbHqoOM-0
message SingleColumnLineItems {
  repeated SingleColumnLineItem items = 1;

  message SingleColumnLineItem {
    typesv2.common.ui.widget.VisualElementTitleSubtitleElement element = 1;
    frontend.deeplink.Deeplink deeplink = 2;
  }
}

// DoubleColumnLineItemsDetails is a component which shows the list of rows with two column.
// Example screen: This is used in payment schedule screen in LAMF
// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=463-14178&mode=design&t=7vaja8dLnmbHqoOM-0
message DoubleColumnLineItemsDetails {
  repeated DoubleColumnLineItem items = 1;
  typesv2.ui.IconTextComponent footer = 2;
  typesv2.ui.IconTextComponent zero_state_text = 3;

  message DoubleColumnLineItem {
    // text to show in the top left of the line item
    typesv2.ui.IconTextComponent title = 1;
    // text to show below title
    typesv2.ui.IconTextComponent subtitle = 2;
    // text to show in the top right of the line item. If value_subtitle is missing then the value should be shown in the middle.
    typesv2.ui.IconTextComponent value = 3;
    // text to show below secondary_value.
    typesv2.ui.IconTextComponent value_subtitle = 4;
    // navigate to deeplink on tap of the line item.
    frontend.deeplink.Deeplink deeplink = 5;
  }
}

// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=1290-12865&mode=design&t=WDfFPySvpBiGCTT7-0
message AddAccountComponent {
  // field to show user details about the source of fetched funds
  typesv2.common.Text fetched_funds_details = 1;
  typesv2.common.Text fetch_more_funds_text = 2;
  typesv2.ui.IconTextComponent add_phone_number = 3;
  int32 top_margin = 4;
}

// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=1290-12865&mode=design&t=WDfFPySvpBiGCTT7-0
message VisualElementComponent {
  typesv2.common.VisualElement visual_element = 1;
  int32 top_margin = 2;
}

message IconTextWithMarginComponent {
  ui.IconTextComponent icon_text_component = 1;
  int32 top_margin = 2;
}

message MfSchemeListComponent {
  typesv2.ui.IconTextComponent heading = 1;
  typesv2.common.Text name_col_header = 2;
  typesv2.common.Text value_col_header = 3;
  repeated SchemeDetails scheme_details = 4;
  typesv2.common.ui.widget.BackgroundColour bg_color = 5;
  int32 top_margin = 6;

  message SchemeDetails {
    typesv2.common.Image icon = 1;
    typesv2.common.Text name = 2;
    // deprecated in favor of amount_text field
    typesv2.Money amount = 3 [deprecated = true];
    typesv2.common.Text amount_text = 5;
    frontend.deeplink.Deeplink deeplink = 4;
    // list of tags to be shown below the mf name.
    repeated typesv2.ui.IconTextComponent tags = 6;
  }
}

// AccountDetailsView encapsulates logic to use raw account details
// and info on how to render the account info on client side
message AccountDetailsView {
  // raw account details containing unmasked acc number, ifsc, etc
  // this can be used by client to auto-fill some field on client side
  MandateBankAccountDetails raw_account_details = 1;
  // this field defines how account info has to be rendered on client side
  AccountDisplayInfo account_display_info = 2;
  // user's eligible accounts (TPAP) should be fetched by client.
  // if false, client will send empty string in derived_account_id in Prepay request.
  bool fetch_user_accounts = 3;
}

// common bank account details to be used in mandate flow
// can be used in deeplink and FE request, so client can have straightforward mapping
message MandateBankAccountDetails {
  string account_number = 1;
  string account_holder_name = 2;
  string ifsc_code = 3;
  string bank_name = 4;
}

message AccountDisplayInfo {
  oneof display_info {
    // defines how account info has to be surface on new mandate initiate v2 screen
    // figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10445-46583&mode=design&t=qb0liToG5S7EFswc-0
    LeftVisualElementWithTextInfoAndCta left_visual_info = 1;
    // defines how account detail has to be surfaced on alternate account screen
    // figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10204-50117&mode=design&t=qb0liToG5S7EFswc-0
    IconTextComponentWithAdditionalInfo itc_info = 2;
  }
}

// LeftVisualElementWithTextInfoAndCta defines left visual icon with support for text/subtext and cta at right side
// figma: https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=10204-50117&mode=design&t=qb0liToG5S7EFswc-0
message LeftVisualElementWithTextInfoAndCta {
  typesv2.common.VisualElement left_icon = 1;
  typesv2.common.Text text = 2;
  typesv2.common.Text sub_text = 3;
  ui.IconTextComponent action = 4;
  typesv2.common.ui.widget.BackgroundColour bg_color = 5;
}

// IconTextComponentWithAdditionalInfo encapsulates itc with additional parameter with selected/unselected option
message IconTextComponentWithAdditionalInfo {
  ui.IconTextComponent info = 1;
  bool is_item_selected = 2;
}

message VerticalItcListWithTopMarginComponent {
  repeated ui.VerticalIconTextComponent vertical_icon_text_components = 1;
  int32 top_margin = 2;
}

// FooterComponent is used to show the bottom component in any screen (mostly CTA and related fields)
message FooterComponent {
  frontend.deeplink.Button cta = 1;
  ui.IconTextComponent banner = 2;
  typesv2.common.Text footer_text = 3;
  int32 top_margin = 4;
  typesv2.common.ui.widget.BackgroundColour footer_bg_color = 5;
  PartnershipComponent partnership_component = 6;
  // denotes the list of consents that needs to be mandatorily accepted by the user.
  // The client should not enable the cta/button click until all the required consents are accepted by the user.
  // Once all the consents are accepted by the user, the client should enable the cta and on cta click call the RecordConsent api first (with the checkbox ids passed as consent names)
  // and then serially (NOT parallelly) trigger the cta/button action.
  // figma : https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=47746-102676&t=UZ0VahKsaMOpS6gU-4
  repeated typesv2.common.ui.widget.CheckboxItem mandatory_consents = 7;
}

// HeaderInfoComponent is used to show the header info in any screen (title, desc, icon etc..) as one component
message HeaderInfoComponent {
  typesv2.common.Text title = 1;
  typesv2.common.Text subtitle = 2;
  // using ITC here as it has support for repeated typesv2.common.Text, that is useful if description has varying font styles/color etc..
  ui.IconTextComponent description = 3;
}

// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9747-163119&mode=dev
message CollapsedLoanAmountSelectionComponent {
  typesv2.common.Text title = 1;
  typesv2.Money loan_amount = 2;
  ui.IconTextComponent edit_amount_cta = 3;
}

// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?type=design&node-id=9747-163125&mode=dev
// This component has an enum value that will be used to decide whether client will show tenure/EMI selection view
message LoanTenureOrEmiSelectionComponent {
  ui.IconTextComponent banner = 1;
  typesv2.common.Text title = 2;
  ui.IconTextComponent interest_rate = 3;
  repeated typesv2.common.Text loan_details = 4;

  LoanDetailsSelectorType loan_details_selector_type = 5;

  // slider values if type is TENURE
  uint32 min_tenure = 6;
  uint32 max_tenure = 7;

  // slider values if type is EMI
  typesv2.Money min_emi = 8;
  typesv2.Money max_emi = 9;

  typesv2.common.Text slider_left_end_text = 10;
  typesv2.common.Text slider_right_end_text = 11;
  uint32 default_tenure = 12;

  frontend.deeplink.Button cta = 13;
  typesv2.common.VisualElement partner_logo = 14;
  typesv2.common.ui.widget.BackgroundColour bg_color = 15;

  // fields to be used to show as slider endpoints in case min and max tenure are coming as same.
  // in that case, we will show the loan offer tenures, with slider as disabled
  uint32 min_loan_offer_tenure = 16;
  uint32 max_loan_offer_tenure = 17;

  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=40493-12925&mode=design&t=GM2rVXe0tweYHThM-4
  typesv2.ui.IconTextComponent info_message = 18;
  bool is_slider_hidden = 19;
}

message TermsAndConditionsComponent {
  typesv2.ui.IconTextComponent heading = 1;
  repeated frontend.deeplink.TermInfo term_infos = 2;
  typesv2.common.ui.widget.BackgroundColour bg_color = 3;
}

// https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/%F0%9F%AA%99-Lending-%E2%80%A2-Workfile?node-id=10831%3A58369&mode=dev
message LoansRewardsComponent {
  ui.IconTextComponent offer_expiry = 1;
  typesv2.common.Text title = 2;
  typesv2.common.Text subtitle = 3;
  typesv2.common.ui.widget.BackgroundColour bg_color = 4;
  frontend.deeplink.Button cta = 5;
}

// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=40531-13169&mode=design&t=E8pQffeWou67PEJP-4
message LoansAutoScrollBannerComponent {
  repeated typesv2.common.VisualElement banners = 1;
  int32 top_margin = 2;
  int32 scroll_time_in_seconds = 3;
}

message VerticalKeyValuePairComponent {
  typesv2.ui.VerticalKeyValuePair key_value_pair = 1;
  int32 top_margin = 2;
  ContainerProperties container_properties = 3;

  message ContainerProperties {
    string bg_color = 1;
    int32 corner_radius = 3;
    int32 top_padding = 4;
    int32 bottom_padding = 5;
    int32 left_padding = 6;
    int32 right_padding = 7;
  }
}

message VisualElementTitleSubtitleComponent {
  typesv2.common.ui.widget.VisualElementTitleSubtitleElement component = 1;
  int32 top_margin = 2;
}

message ButtonComponent {
  frontend.deeplink.Button button = 1;
  int32 top_margin = 2;
}

// https://www.figma.com/file/aX6FHSGSFFM1YBtK3HNCJl/%F0%9F%92%B0-LAMF-%E2%80%A2-Workfile?type=design&node-id=5722-42359&mode=design&t=8vRP4uGE7RypGEdZ-0
message DualVerticalKeyValuePairComponent {
  typesv2.ui.VerticalKeyValuePair left = 1;
  typesv2.ui.VerticalKeyValuePair right = 2;
  typesv2.common.VisualElement icon = 3;
  typesv2.common.ui.widget.BackgroundColour bg_color = 4;
  bool strike_through = 5;
}

message ErrorBanner {
  // unique identifier for a banner
  string id = 1;
  typesv2.common.VisualElement icon = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text description = 4;
  typesv2.common.ui.widget.BackgroundColour bg_color = 5;

  // if true, client should only show the banner once to the user.
  // Banner is not shown to the user, if user comes back to this screen after seeing the banner once.
  // Client should use `id` of the banner to determine the uniqueness of a banner
  bool show_once = 6;

  // duration for which the component should be displayed
  // if zero/empty, client should assume that the component is to be shown
  // permanently on that screen.
  int32 display_duration_in_ms = 7;
}

// generic component to show 3 dots/bars which when not nil or list of options is more than zero,
// pops up list of ITCs which are all clickable and navigates to the respective deeplink
// https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=42772-54548&mode=design&t=VBD155dYlKlB7E7z-4
message HamburgerComponent {
  // title or icon or clickable item which when clicked opens the list
  ui.IconTextComponent display_item = 1;
  // list of option icon text pair
  // use deeplink inside of ITC for navigation
  repeated ui.IconTextComponent options = 2;
}

// loan plan component used to show different emi and tenure plans
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=46090-2596&t=QHGY3NC65qoWvFHS-4
message LoanPlanOption {
  // this will contain emi X tenure info
  ui.IconTextComponent plan_info_title = 1;
  ui.IconTextComponent top_tag = 2;
  typesv2.common.VisualElement right_icon = 3;
  frontend.deeplink.Deeplink deeplink = 4;
  string border_color = 5;
  string bg_color = 6;
  // these will be passed to BE in rpc call, according to user selection
  typesv2.Money emi_amount = 7;
  int32 tenure_in_months = 8;
  bool is_selected = 9;
}

message LoanOfferDetailsCard {
  CardTopBadge card_top_badge = 1;
  TopSection top_section = 2;
  BottomSection bottom_section = 3;
  string separator_bar_color = 4;
  frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 5;
  // do not show border if this is nil
  common.ui.widget.LinearGradient card_border_color_gradient = 6;
  // if empty, do not show shadow
  common.ui.widget.Shadow shadow = 7;
  // Background colour for the card
  string bg_color = 8;
  // to be passed to BE to identify which offer is selected
  string loan_offer_id = 9;

  // with configurable rpc calls and deeplink navigation
  LoansCta loans_cta = 10;

  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%9A%80-Lending-FFF?node-id=75523-46733&t=nxmxSGLBM1kmeBal-4
  repeated ui.IconTextComponent offer_message = 11;

  message CardTopBadge {
    typesv2.common.Text text = 1;
    common.ui.widget.LinearGradient bg_color_gradient = 2;
    string triangle_bg_color = 3;
  }
  message TopSection {
    oneof type {
      typesv2.common.VisualElement full_image = 1;
      ui.IconTextComponent left_icon_text = 2;
      OfferBanner offer_details_banner = 3;
    }
  }
  message OfferBanner {
    typesv2.ui.VerticalKeyValuePair offer_description = 1;
    typesv2.common.VisualElement background_image = 2;
    bool show_border = 3;
  }

  message BottomSection {
    repeated KeyValueRow key_value_rows = 1;
    // if nil, do not show this
    EmiSelector emi_selector = 2;
    // Show Border around BottomSection if this is true
    bool show_border = 3;

    message EmiSelector {
      string bg_color = 1;
      oneof type {
        Slider slider = 2;
        Message text = 3;
      }

      message Slider {
        typesv2.common.Text title = 1;
        int32 default_tenure_in_months = 2;
        int32 min_tenure_in_months = 3;
        int32 max_tenure_in_months = 4;
        typesv2.common.Text slider_min_label = 5;
        typesv2.common.Text slider_max_label = 6;
        int32 step_size = 7;
        typesv2.common.Text emi_tag_label = 8;
      }

      message Message {
        KeyValueRow key_value_row_title = 1;
        ui.IconTextComponent text = 2;
      }
    }
  }
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-%E2%80%A2-FFF?node-id=63498-76644&t=uZ2rmihLm2nJEoL4-4
message LoanOfferCardComponent {
  string card_bg_color = 1;
  typesv2.common.Text card_title = 2;
  typesv2.common.VisualElement background_image = 3;
  OfferDetailsCard offer_details_card = 4;
  OfferProgressCard offer_progress_stage_card = 5;

  message OfferDetailsCard {
    string bg_color = 1;
    typesv2.common.Text offer_title = 2 [deprecated = true];
    typesv2.Money offer_amount = 3 [deprecated = true];
    ui.IconTextComponent offer_description = 4 [deprecated = true];
    ui.IconTextComponent offer_title_v2 = 5;
    OfferAmountDetails offer_amount_details = 6;
    // Used to show the offer chips
    repeated ui.IconTextComponent offer_description_chips = 7;
  }

  message OfferProgressCard {
    string bg_color = 1;
    SectionTypeProgress stage_progress = 2;
    typesv2.common.VisualElement partner_logo = 3;
  }

  message OfferAmountDetails {
    ui.IconTextComponent partner_logo = 1;
    typesv2.common.Text offer_subtitle = 2;
    ui.IconTextComponent offer_amount = 3;
  }
}

// wrapper for CTA component similar to CTA
// can be used for both moving to a new screen or calling an RPC etc based on the action
message LoansCta {
  // metadata to be used from cta content for displaying ui, deeplink inside this to be kept empty
  frontend.deeplink.Cta cta_content = 1;
  // actual action to be taken on click of cta
  LoansCtaAction cta_action = 2;
}

message LoansCtaAction {
  oneof action {
    // move to a new screen using this deeplink on click of cta
    frontend.deeplink.Deeplink deeplink = 1;
    // call an RPC on click of cta
    CallRpc call_rpc = 2;
  }

  message CallRpc {
    RpcName rpc_name = 1;
    CommonMetaData common_meta_data = 2 [deprecated = true];
    message CommonMetaData {
      frontend.preapprovedloan.pal_enums.LoanHeader loan_header = 1;
      string lo_id = 2; // loan offer -> id
      string lr_id = 3; // loan request -> id
      string lse_id = 4; // loan step exexcution -> id
      string la_id = 5; // loan account -> id
      string loec_id = 6; // loan offer eligibility criteria -> id
      frontend.preapprovedloan.pal_enums.LoanStepExecutionFlow lse_flow = 7; // flow like eligibility,loan_application etc.
    }
    // if needed rpc specific request data can be added here using a oneof
    oneof RequestParams {
      LandingInfoParams landing_info_params = 8 [deprecated = true];
    }
    message LandingInfoParams {
      // Controls failed loan request fetching. Empty string enables fetch (shows dashboard),
      // non-empty disables fetch (routes to second look flow)
      repeated string landing_info_filters = 1;
    }
    // unpack to the request type corresponding to rpc_name and use
    google.protobuf.Any rpc_request = 9;
  }

  enum RpcName {
    RPC_NAME_UNSPECIFIED = 0;
    // GetOfferDetails rpc to be called when this cta is clicked
    RPC_NAME_OFFER_DETAILS = 1;
    // ApplyForLoan rpc to be called when this cta is clicked
    RPC_NAME_APPLY_FOR_LOAN = 2;
    // CollectData rpc
    RPC_COLLECT_DATA = 3;
    // GetLandingInfo
    RPC_GET_LANDING_INFO = 4;
    // CheckLoanEligibility
    RPC_NAME_CHECK_LOAN_ELIGIBILITY = 5;
  }

}
