syntax = "proto3";

package preapprovedloan.enums;

option go_package = "github.com/epifi/gamma/api/preapprovedloan/enums";
option java_package = "com.github.epifi.gamma.api.preapprovedloan.enums";

// represents different form fields collected from the user
// ref: https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-63839&t=AzSZYhZyj4avLgl4-0
enum FieldId {
  FIELD_ID_UNSPECIFIED = 0;
  NAME = 1;
  DOB = 2;
  PAN = 3;
  GENDER = 4;
  MARITAL_STATUS = 5;
  PINCODE = 6;
  ADDRESS_TYPE = 7;
  RENT = 8;
  LOAN_PURPOSE = 9;
  DESIRED_LOAN_AMOUNT = 10;
  EMPLOYMENT_TYPE = 11;
  EMPLOYER_NAME = 12;
  WORK_EMAIL = 13;
  MONTHLY_INCOME = 14;
  WORK_ADDRESS = 15;
  GSTIN = 16;
  ANNUAL_REVENUE = 17;
  MOTHER_NAME = 18;
  FATHER_NAME = 19;
}

enum LoanPurpose {
  LOAN_PURPOSE_UNSPECIFIED = 0;
  LOAN_PURPOSE_HOME_RENOVATION = 1;
  LOAN_PURPOSE_TRAVEL = 2;
  LOAN_PURPOSE_EDUCATION = 3;
  LOAN_PURPOSE_WEDDING = 4;
  LOAN_PURPOSE_MEDICAL = 5;
  LOAN_PURPOSE_RELOCATION = 6;
  LOAN_PURPOSE_CONSOLIDATE_EXISTING_LOANS = 7;
  LOAN_PURPOSE_INVESTMENT = 8;
  LOAN_PURPOSE_OTHER = 9;
}
