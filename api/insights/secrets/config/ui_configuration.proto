syntax = "proto3";

package insights.secrets.config;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/ui/icon_text_component.proto";


option go_package = "github.com/epifi/gamma/api/insights/secrets/config";
option java_package = "com.github.epifi.gamma.api.insights.secrets.config";


// Configuration powering api.typesv2.ui.assetsandanalysis.SecretSummaryCard.
message SecretSummaryCardConfiguration {
  ITCConfiguration title_config = 1;
  // Only configures the text styling of the value, we would not be configuring the complete ITC
  TextConfiguration value_config = 2;
  VisualisationConfiguration visualisation_config = 3;
  string bg_color = 4;
  string border_color = 5;
  // If the user does not provide data for the formation of the secret, this value will be used to display a zero state text, which will be shaded on the client side.
  string zero_state_secret_value = 6;
  // Used for configuring how we wish to show the user the secret summary if the secret is disabled or unavailable.
  SecretSummaryDisabledState secret_summary_disabled_state = 7;
}

message ITCConfiguration {
  VisualisationConfiguration left_visualisation_config = 1;
  repeated TextConfiguration text_configs = 2;
  VisualisationConfiguration right_visualisation_config = 3;
  message BorderConfig {
    string color = 1;
  }
  BorderConfig border_config = 4;
  string bg_color = 5;
  api.typesv2.ui.IconTextComponent.ContainerProperties container_properties = 6;
  frontend.deeplink.Deeplink deeplink = 7;
}

message VerticalITCConfig {
  ITCConfiguration title_config = 1;
  ITCConfiguration value_config = 2;
}

message TextConfiguration {
  // Color of text font
  string color = 1;
  api.typesv2.common.FontStyle font_style = 2;
  // optional text
  string text = 3;
  DisplayTemplate display_template = 4;

  // Background color of text
  string bg_color = 5;

  api.typesv2.common.Text.TextAlignment text_alignment = 6;
}

// This object helps replace dynamic content with the corresponding computed secret text.
message DisplayTemplate {
  // This text is used to replace a placeholder with a specific input value.
  // For example:
  //   expression_text := "your {input} is the fee"
  // It will be rendered in the UI as "your 0.5$ is the fee" if the input is 0.5$.
  // The placeholder {input} is replaced with the user-provided value.
  // note: currently replacing of text is done for {input} value only
  string text = 1;
}

message VisualisationConfiguration {
  string image_url = 1;

  // Height of image
  int32 height = 2;

  // Width of image
  int32 width = 3;
}

message VisualisationCardConfig {
  ITCConfiguration pro_tip_config = 1;
  oneof visualisation {
    NumberCardConfig number_card_config = 2;
    BarChartCardConfig bar_chart_card_config = 3;
    ImageCardConfig image_card_config = 4;
    AreaChartCardConfig area_chart_card_config = 5;
    GridVisualisationCardConfig grid_visualisation_card_config = 6;
    DonutCardConfig donut_card_config = 7;
  }
}

// Config powering Number Card
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16176-31409&t=aSPJa0l9AITF7Sr1-4
message NumberCardConfig {
  VerticalITCConfig title_value_config = 1;
  VisualisationConfiguration image_config = 2;
  string bg_color = 3;
}

message BarChartCardConfig {
  // Config for a title and its value, e.g. 'Your top-performing Mutual Fund' and 'ICICI Pru Dividend fund'
  VerticalITCConfig title_value_config = 1;

  // Config for a score or insight value, e.g. XIRR of the top performing MF '+24.5%'
  ITCConfiguration score_insight_config = 2;

  // Config for a bar chart
  BarChartConfig bar_chart_config = 3;

  // Background color of card
  string bg_color = 4;

  // refrence line
  ReferenceLine reference_line = 5;
}

message ReferenceLine {
  ITCConfiguration title_config = 1;
  string line_color = 2;
}

// Config for a bar chart
message BarChartConfig {
  BarConfig default_bar_config = 1;
  // ToDo: add config for individual bars if required (unlikely)
}

// Config for a single bar of the bar chart
message BarConfig {
  // Config for the component on top of the bar for a positive value
  // and below the bar for a negative value
  BarDisplayComponentConfig top_display_component_config = 1;

  // Config for the component below the bar axis for a positive value
  // and above the bar axis for a negative value
  BarDisplayComponentConfig bottom_display_component_config = 2;

  // Color of a bar
  string bar_color = 3;
}

message BarDisplayComponentConfig {
  oneof config {
    VisualisationConfiguration image_config = 1;
    ITCConfiguration display_text_config = 2;
  }
}

// Config to control styling and content of an image card
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16171-24623&t=x8vnu5NlxmgXddE9-4
message ImageCardConfig {
  ITCConfiguration title_config = 1;
  VisualisationConfiguration background_image_config = 2;
  string background_color = 3;

  message ValueComponentConfig {
    VisualisationConfiguration image_config = 1;
    VerticalITCConfig secret_value_pair_config = 2;
  }
  ValueComponentConfig value_component_config = 4;
}

// LineItemsConfig defines ui configuration of a group line items
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16175-29794&t=58SDplXF2rqHeMK6-4
message LineItemsConfig {
  LineItemConfig line_item_config = 1;

  // Config for left-side header row for line items
  TextConfiguration left_aligned_heading_config = 2;

  // Config for right-side header row for line items
  TextConfiguration right_aligned_heading_config = 3;
}

// LineItemConfig defines configuration of individual line item
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16175-29798&t=58SDplXF2rqHeMK6-4
message LineItemConfig {
  ITCConfiguration left_heading_config = 1;
  ITCConfiguration left_tags_config = 2;
  ITCConfiguration right_heading_config = 3;
  ITCConfiguration right_tags_config = 4;
  VisualisationConfiguration left_image_config = 5;
}

message LearnOnTheGoSectionConfig {
  TextConfiguration title_config = 1;
  repeated LearnOnTheGoSectionItemConfig section_items_config = 2;
}

message LearnOnTheGoSectionItemConfig {
  VisualisationConfiguration image_config = 1;
  ITCConfiguration info_tag_config = 2;
  // Color of the overlay around the image
  string overlay_color = 3;
  TextConfiguration description_config = 4;
  message RedirectionConfig {
    oneof redirection {
      frontend.deeplink.Deeplink deeplink = 1;
      string storifyme_url = 2;
    }
  }
  RedirectionConfig redirection_config = 5;
}

message TextExplainerSectionConfig {
  ITCConfiguration title = 1;
  repeated TextConfiguration descriptions = 2;
  string bg_color = 3;
  string border_color = 4;
  message ExpandableControlConfig {
    bool is_expanded = 1;
    VisualisationConfiguration toggle_image_config = 2;
  }
  ExpandableControlConfig expandable_control_config = 5;
  repeated ITCConfiguration cta_configs = 6;
}

message CardInfoComponentConfig {
  VerticalITCConfig title_value_config = 1;
  ITCConfiguration insight_config = 2;
}

message ChartDragIndicatorConfig {
  string locator_line_color = 1;
  ITCConfiguration indicatorLabelConfig = 2;
}

message ChartLineConfig {
  string line_color = 1;
  int32 line_width = 2;
}

message AreaChartCardConfig {
  CardInfoComponentConfig card_info_component_config = 1;
  string bg_color = 2;
  api.typesv2.common.ui.widget.BackgroundColour area_fill_color = 3;
  ChartDragIndicatorConfig chart_drag_indicator_config = 4;
  ChartLineConfig chart_line_config = 5;
  TextConfiguration horizontal_axis_label_config = 6;
}

// ActionableBannerConfig configures the ui for actionable banner component
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20349-35035&t=nVMbBK1YpBhtAc09-4
message ActionableBannerConfig {
  VisualisationConfiguration left_image_config = 1;
  TextConfiguration description_config = 2;
  ITCConfiguration right_component_config = 3;
  string bg_color = 4;
  frontend.deeplink.Deeplink deeplink = 5;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28110&t=9PLMrfTzO3vjKyor-4
message GridVisualisationCardConfig {
  VerticalITCConfig title_value_config = 1;
  GridComponentConfig grid_component_config = 2;
}

// GridComponentConfig configures the ui for grid component
// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28140&t=9PLMrfTzO3vjKyor-4
message GridComponentConfig {
  TextConfiguration title_config = 1;
  string bg_color = 2;
  GridCardConfig grid_card_config = 3;
}

// GridCardConfig configures the ui for grid card component
// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28142&t=9PLMrfTzO3vjKyor-4
message GridCardConfig {
  int32 column_count = 1;
  GridTileConfig grid_tile_config = 2;
}

// GridTileConfig configures the ui for individual grid tile
// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28226&t=9PLMrfTzO3vjKyor-4
message GridTileConfig {
  ITCConfiguration title_config = 1;
  ITCConfiguration value_config = 2;
  string bg_color = 3;
  VisualisationConfiguration icon_config = 4;
  GridComponentBottomCardConfig bottom_card_config = 5;

}

// GridComponentBottomCardConfig configures the ui for bottom card component in grid tile which is shown on tap of grid tile
// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28241&t=9PLMrfTzO3vjKyor-4
message GridComponentBottomCardConfig {
  ITCConfiguration title_config = 1;
  GridComponentBottomCardValueConfig value_config = 2;
}

message GridComponentBottomCardValueConfig {
  ITCConfiguration key_config = 1;
  ITCConfiguration value_config = 2;
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18363-11178&t=oa57kBmUiIJoXyzi-1
message DonutCardConfig {
  // Config for a title and its value, e.g. 'Your most invested fund' and 'HDFC'
  VerticalITCConfig title_value_config = 1;
  // config for donut slice
  DonutSliceConfig donut_slice_config = 2;
  // Background color of card
  string bg_color = 3;
}

message DonutSliceConfig {
  // default color of a donut slice, if not provided in code then this color will be used
  string donut_color = 1;
}

enum SecretSummaryDisabledState {
  SECRET_SUMMARY_DISABLED_STATE_UNSPECIFIED = 0;
  // Shows the secret with coming soon tag
  SSDS_COMING_SOON = 1;
  // Shows the locked secret with unlock secret tag
  SSDS_UNLOCK_SECRET = 2;
}

// ActionableV2BannerConfig configures the ui for actionable banner component
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-FFF?node-id=68069-73027&t=3WXp9aB2SYPlGJWW-4 (Loan banner in the figma)
message ActionableBannerV2Config {
  VisualisationConfiguration right_image_config = 1;
  TextConfiguration title_config = 2;
  ITCConfiguration cta_component_config = 3;
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 4;
  frontend.deeplink.Deeplink deeplink = 5;
}
