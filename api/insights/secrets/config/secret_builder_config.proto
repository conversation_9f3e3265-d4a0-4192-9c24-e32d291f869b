syntax = "proto3";

package insights.secrets.config;

import "api/analyser/variables/analysis_variables.proto";
import "api/categorizer/enums.proto";
import "api/insights/secrets/config/ui_configuration.proto";
import "api/investment/mutualfund/mutual_fund.proto";

option go_package = "github.com/epifi/gamma/api/insights/secrets/config";
option java_package = "com.github.epifi.gamma.api.insights.secrets.config";

// SecretBuilderDataConfig defines configurations required to run a secret builder
// This includes inherent filters applicable while preparing a secret such as category name
message SecretBuilderDataConfig {
  oneof data_config {
    CategoryAggregateDataConfig category_aggregate_config = 1;
    CreditReportSecretDataConfig credit_report_secret_data_config = 2;
    MfPortfolioSecretDataConfig mf_portfolio_secret_data_config = 3;
    EpfSecretDataConfig epf_secret_data_config = 4;
    AssetsSecretDataConfig assets_secret_data_config = 5;
  }
}

// CategoryAggregateDataConfig defines the config for SECRET_BUILDER_NAME_TRANSACTION_CATEGORY
message CategoryAggregateDataConfig {
  categorizer.DisplayCategory display_category = 1;
}

// this use case help in rendering secret data for SECRET_BUILDER_NAME_CREDIT_REPORT_SECRET secret
enum CreditReportSecretDimension {
  CREDIT_REPORT_SECRET_DIMENSION_UNSPECIFIED = 0;
  CREDIT_REPORT_SECRET_DIMENSION_HARD_ENQUIRY = 1;
}
// CreditReportSecretDataConfig defines the config for dimension to show credit report secretY
message CreditReportSecretDataConfig {
  CreditReportSecretDimension credit_report_secret_dimension = 1;
}

// MfPortfolioSecretDataConfig defines filters that can be applied while filtering the relevant mutual funds for a secret
message MfPortfolioSecretDataConfig {
  api.investment.mutualfund.MutualFundCategoryName mutual_fund_category_name = 1;
  api.investment.mutualfund.AssetClass asset_class = 2;
  AumRange aum_range = 3;
  message AumRange {
    double min = 1;
    double max = 2;
  }

  api.investment.mutualfund.Amc amc = 4;
  enum SecretMetric {
    SECRET_METRIC_UNSPECIFIED = 0;
    SECRET_METRIC_XIRR = 1;
    SECRET_METRIC_SHARPE_RATIO = 2;
    SECRET_METRIC_ALPHA = 3;
    SECRET_METRIC_HIT_RATE = 4;
    SECRET_METRIC_EXPENSE_RATIO = 5;
    SECRET_METRIC_BATTING_AVG = 6;
    SECRET_METRIC_UPSIDE_CAPTURE_RATIO = 7;
    SECRET_METRIC_DOWN_SIDE_CAPTURE_RATIO = 8;
  }
  // Represents the value of the mutual fund, which will be used for sorting and calculating metrics for secrets.
  SecretMetric secret_metric = 5;

  // Expected number of schemes with the secret metric available
  SchemeCount schemes_with_metric_count = 6;
  // Expected number of schemes in the secret. If the count of schemes donot fall in the range the secret will not be generated.
  message SchemeCount {
    int32 min = 1;
    int32 max = 2;
  }

  // collection names to be used for fetching the mutual funds in given collection and filtering on that basis
  repeated string collection_name = 7;
  // The flag to be passed as true when the mf analytics data to be sorted in ascending order
  bool is_ascending_order = 8;
  // The type of plan offer for the mutual fund
  api.investment.mutualfund.PlanType plan_type = 9;
  // data required to show the performance of the portfolio
  message PortfolioPerformanceData {
    double fixed_deposit = 1;
    double nifty50 = 2;
    double peers = 3;
  }
  PortfolioPerformanceData portfolio_performance_data = 10;
}

message EpfSecretDataConfig {
  EpfSecretMetric epf_secret_metric = 1;
  EpfSecretDimension epf_secret_dimension = 2;
}

enum EpfSecretMetric {
  EPF_SECRET_METRIC_UNSPECIFIED = 0;
  EPF_SECRET_METRIC_MEDICAL_WITHDRAWABLE_BALANCE = 1;
  EPF_SECRET_METRIC_MARRIAGE_WITHDRAWABLE_BALANCE = 2;
  EPF_SECRET_METRIC_EDUCATION_WITHDRAWABLE_BALANCE = 3;
  EPF_SECRET_METRIC_HOUSE_CONSTRUCTION_WITHDRAWABLE_BALANCE = 4;
  EPF_SECRET_METRIC_HOUSE_LOAN_REPAYMENT_WITHDRAWABLE_BALANCE = 5;
  EPF_SECRET_METRIC_HOUSE_RENOVATION_WITHDRAWABLE_BALANCE = 6;
  EPF_SECRET_METRIC_BALANCE = 7;
  EPF_SECRET_METRIC_LAND_PURCHASE_WITHDRAWABLE_BALANCE = 8;
}

// EpfSecretDimension defines different dimensions over which epf data can be shown
enum EpfSecretDimension {
  EPF_SECRET_DIMENSION_UNSPECIFIED = 0;
  EPF_SECRET_DIMENSION_CURRENT_PASSBOOK_BALANCE = 1;
  EPF_SECRET_DIMENSION_WITHDRAWABLE_BALANCE = 2;
  EPF_SECRET_DIMENSION_CONTRIBUTORS = 3;
  EPF_SECRET_DIMENSION_TRANSFERABLE_BALANCE = 4;
}

message AssetsSecretDataConfig {
  AssetsSecretMetric assets_secret_metric = 1;
}

enum AssetsSecretMetric {
  ASSETS_SECRET_METRIC_UNSPECIFIED = 0;
  ASSETS_SECRET_METRIC_ACTIVE_ASSET_RATIO = 1;
}

message SecretAnalyserUIConfig {
  string component_name = 1;

  oneof config {
    LineItemsConfig line_items_config = 2;
    // Configuration for the visualisation (number card, bar, line chart etc) shown in the secret
    VisualisationCardConfig visualisation_card_config = 3;
    LearnOnTheGoSectionConfig learn_on_the_go_section_config = 5;
    TextExplainerSectionConfig text_explainer_section_config = 6;
    ActionableBannerConfig actionable_banner_config = 8;
    // Added to avoid redundant UI configuration duplication for trivial elements,
    // e.g., during "learn-on-the-go" UI configuration population used in multiple places
    GlobalAnalyserUIComponentConfig global_analyser_ui_component_config = 9;
    // Configuration for action banner
    ActionableBannerV2Config actionable_banner_v2_config = 12;
  }
  // Optional theme to be applied to the secret analyser ui config. Theme is used as to set zero value fields with default values.
  SecretAnalyserUiTheme theme = 4;
  // The flag to be passed as true when the component can expect a null value as well
  bool is_optional = 7;
  // represent eligible state for the secret analyser ui component
  repeated SecretAnalyserState eligible_states = 10;

  repeated api.analyser.variables.AnalysisVariableName required_analysis_variables = 11;
}

message GlobalAnalyserUIComponentConfig {
  string global_config_id = 1;
}

// SecretAnalyserUiTheme is used to identify a predefined SecretAnalyserUIConfig
// Themes are used to define common configurations for a ui component that can be shared across multiple secrets
// A secret can override any field specified in a theme by defining a non zero value of the field in the secret config
enum SecretAnalyserUiTheme {
  SECRET_ANALYSER_UI_THEME_UNSPECIFIED = 0;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-10471&t=P4Bovzg8WuzAHcFq-4
  SECRET_ANALYSER_BAR_CHART_CARD_THEME_BERRY = 1;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-10821&t=P4Bovzg8WuzAHcFq-4
  SECRET_ANALYSER_BAR_CHART_CARD_THEME_INDIGO = 2;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-11207&t=P4Bovzg8WuzAHcFq-4
  SECRET_ANALYSER_BAR_CHART_CARD_THEME_AMBER = 3;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-12178&t=P4Bovzg8WuzAHcFq-4
  SECRET_ANALYSER_BAR_CHART_CARD_THEME_OCEAN = 4;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-12891&t=P4Bovzg8WuzAHcFq-4
  SECRET_ANALYSER_BAR_CHART_CARD_THEME_MOSS = 5;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13246&t=P4Bovzg8WuzAHcFq-4
  SECRET_ANALYSER_BAR_CHART_CARD_THEME_CHERRY = 6;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16176-31409&t=mfJ7JsMFE4wchGAY-4
  SECRET_ANALYSER_NUMBER_CARD_THEME_OCEAN = 7;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16171-23658&t=mfJ7JsMFE4wchGAY-4
  SECRET_ANALYSER_NUMBER_CARD_THEME_AMBER = 8;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16176-31970&t=mfJ7JsMFE4wchGAY-4
  SECRET_ANALYSER_NUMBER_CARD_THEME_HONEY = 9;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16176-31973&t=mfJ7JsMFE4wchGAY-4
  SECRET_ANALYSER_LINE_ITEMS_THEME_DEFAULT = 10;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-15157&t=u97bzoDwUVWKxH1T-4
  SECRET_ANALYSER_NUMBER_CARD_THEME_MOSS = 11;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-16293&t=u97bzoDwUVWKxH1T-4
  SECRET_ANALYSER_NUMBER_CARD_THEME_CHERRY = 12;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-16589&t=u97bzoDwUVWKxH1T-4
  SECRET_ANALYSER_NUMBER_CARD_THEME_BERRY = 13;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20249-35436&t=zebR3cdcBSHAN1qr-4
  SECRET_ANALYSER_TEXT_EXPLAINER_SECTION_THEME_DEFAULT = 14;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=21325-39061&t=IHYiPFIJ2rbOWhgd-4
  SECRET_ANALYSER_ACTIONABLE_REFRESH_BANNER_THEME_DEFAULT = 15;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20582-35088&t=nHS0eS3IVpAsh1TZ-4
  SECRET_ANALYSER_TEXT_EXPLAINER_SECTION_WITH_TOGGLE_THEME_DEFAULT = 16;
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=584-34006&t=ELxnMCaKE0UkQULY-4
  SECRET_ANALYSER_DONUT_CARD_THEME_OCEAN = 17;
  // Default theme defining fonts and colors of text in actionable banner which allows user to edit some value
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=355-27824&t=CSA4n2mM2davu1kY-4
  SECRET_ANALYSER_EDITABLE_VALUE_ACTIONABLE_BANNER_THEME_DEFAULT = 18;
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=584-34649&t=bVnPAh3ePhKtbrPK-4
  SECRET_ANALYSER_DONUT_CARD_THEME_MOSS = 19;
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=584-34513&t=hFSN60FxT2pCVd6p-4
  SECRET_ANALYSER_AREA_CHART_CARD_THEME_MOSS = 20;
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=409-8961&t=ULWGJvPOCEZUMFgN-4
  SECRET_ANALYSER_DONUT_CARD_THEME_BERRY = 21;
}

message SecretSummaryUIConfig {
  oneof config {
    SecretSummaryCardConfiguration secret_summary_card_configuration = 1;
  }
  // Optional theme to be applied to the secret summary ui config. Theme is used as to set zero value fields with default values.
  SecretSummaryUiTheme theme = 2;
}

// SecretSummaryUiTheme is used to identify a predefined SecretSummaryUIConfig
// Themes are used to define common configurations for secret summary that can be shared across multiple secrets
// A secret can override any field specified in a theme by defining a non zero value of the field in the secret config
enum SecretSummaryUiTheme {
  SECRET_SUMMARY_UI_THEME_UNSPECIFIED = 0;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13705&t=szhPPN3FsJXgv3kH-4
  SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_BERRY = 1;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13637&t=szhPPN3FsJXgv3kH-4
  SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_INDIGO = 2;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13610&t=szhPPN3FsJXgv3kH-4
  SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_AMBER = 3;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13743&t=szhPPN3FsJXgv3kH-4
  SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_OCEAN = 4;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13781&t=szhPPN3FsJXgv3kH-4
  SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_MOSS = 5;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13819&t=szhPPN3FsJXgv3kH-4
  SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_CHERRY = 6;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16276-15128&t=mfJ7JsMFE4wchGAY-4
  SECRET_SUMMARY_CARD_THEME_OCEAN = 7;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16276-15777&t=mfJ7JsMFE4wchGAY-4
  SECRET_SUMMARY_CARD_THEME_AMBER = 8;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16276-15840&t=mfJ7JsMFE4wchGAY-4
  SECRET_SUMMARY_CARD_THEME_HONEY = 9;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-16270&t=u97bzoDwUVWKxH1T-4
  SECRET_SUMMARY_CARD_THEME_MOSS = 10;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-16567&t=u97bzoDwUVWKxH1T-4
  SECRET_SUMMARY_CARD_THEME_CHERRY = 11;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-16863&t=u97bzoDwUVWKxH1T-4
  SECRET_SUMMARY_CARD_THEME_BERRY = 12;
}

enum SecretAnalyserState {
  SECRET_ANALYSER_STATE_UNSPECIFIED = 0;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=19267-19488&node-type=frame&t=ix0xvJKV5jSYJlt2-0
  SECRET_ANALYSER_STATE_BASE = 1;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20535-35288&node-type=frame&t=ix0xvJKV5jSYJlt2-0
  // state where insufficient data is available to generate a secret
  SECRET_ANALYSER_STATE_ZERO_VALUE = 2;
}
