//go:generate gen_sql -types=InvestmentInstrument,OtherDeclarationDetails
syntax = "proto3";

package insights.networth.model;

import "api/typesv2/investment_instrument.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/insights/networth/model";
option java_package = "com.github.epifi.gamma.api.insights.networth.model";

// InvestmentDeclaration defines a investment declared by the user
message InvestmentDeclaration {
  string id = 1;
  string actor_id = 2;
  // instrument in which money was invested
  api.typesv2.InvestmentInstrumentType instrument_type = 3;
  // amount invested in instrument
  google.type.Money invested_amount = 4;
  // time at which money was invested in the instrument
  google.protobuf.Timestamp invested_at = 5;
  // time at which invested amount will mature
  google.protobuf.Timestamp maturity_date = 6;
  // the rate at which invested amount is growing
  double interest_rate = 7;
  // metadata for instrument specific details
  OtherDeclarationDetails declaration_details = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  google.protobuf.Timestamp deleted_at = 11;
  // reference to consent recorded when user submitted this form
  string consent_id = 12;
  // external id to be shared with external system as a unique reference to this asset declaration
  string external_id = 13;
  Source source = 14;
}

// Source differentiates the existing ways of declaring investments, eg. Manual user addition, Magic import, etc.
enum Source {
  SOURCE_UNSPECIFIED = 0;
  SOURCE_MAGIC_IMPORT = 1;
}

message OtherDeclarationDetails {
  oneof details {
    FixedDepositDeclarationDetails fixed_deposit_declaration_details = 1;
    RecurringDepositDeclarationDetails recurring_deposit_declaration_details = 2;
    RealEstate real_estate = 3;
    AIF aif = 4;
    PrivateEquity private_equity = 5;
    DigitalGold digital_gold = 6;
    Cash cash = 7;
    DigitalSilver digital_silver = 8;
    Bond bond = 9;
    ArtAndArtefacts art_and_artefacts = 10;
    PortfolioManagementService portfolio_management_service = 11;
    PublicProvidentFund public_provident_fund = 12;
    EmployeeStockOption employee_stock_option = 13;
  }
}

message RealEstate {
  string investment_name = 1 [(validate.rules).string = {min_len: 1, max_len: 100}];
  google.type.Money invested_value = 2 [(validate.rules).message.required = true];
  google.type.Money current_value = 3 [(validate.rules).message.required = true];
  google.type.Date investment_date = 4;
}

message AIF {
  // This captures custom AIF names from when intermediary identifier was not supported.
  // It represents both the AMC name and the fund name.
  string aif_name = 1 [deprecated = true];

  google.type.Date investment_date = 2;
  google.type.Date evaluation_date = 3 [(validate.rules).message.required = true];

  // AMC is captured as part of the AIF name now.
  // Old records with AMC populated are not used anymore.
  // E.g., Abakkus Growth Fund captures the AMC name: Abakkus Asset Manager LLP and the fund name: Growth Fund
  string amc_name = 4 [deprecated = true];

  Category category = 5 [(validate.rules).enum = {not_in: [0]}];
  google.type.Money invested_value = 6 [(validate.rules).message.required = true];
  google.type.Money current_value = 7 [(validate.rules).message.required = true];
  string folio_id = 8 [(validate.rules).string.max_len = 100];
  string broker_code = 9 [(validate.rules).string.max_len = 100];
  string remarks = 10 [(validate.rules).string.max_len = 100];
  enum Category {
    CATEGORY_UNSPECIFIED = 0;
    CATEGORY_1 = 1;
    CATEGORY_2 = 2;
    CATEGORY_3 = 3;
  }

  oneof identifier {
    // A unique identifier of an AIF when it is chosen from a list of known AIFs.
    string aif_id = 11 [(validate.rules).string = {min_len: 1, max_len: 100}];

    // This name of an AIF submitted by user, instead of choosing from a list of known AIFs.
    // Represents both the AMC name and the fund name.
    string aif_name_v2 = 12 [(validate.rules).string = {min_len: 1, max_len: 100}];
  }
}

message PrivateEquity {
  string investment_name = 1 [(validate.rules).string = {min_len: 1, max_len: 100}];
  google.type.Money invested_value = 2 [(validate.rules).message.required = true];
  google.type.Money current_value = 3 [(validate.rules).message.required = true];
  google.type.Date investment_date = 4;
}

message DigitalGold {
  string investment_name = 1 [(validate.rules).string = {min_len: 1, max_len: 100}];
  google.type.Money invested_value = 2 [(validate.rules).message.required = true];
  double quantity_in_grams = 3 [(validate.rules).double.gt = 0];
  google.type.Date investment_date = 4;
  GoldCarat gold_carat_value = 5 [(validate.rules).enum = {not_in: [0]}];
}

enum GoldCarat {
  GOLD_CARAT_UNSPECIFIED = 0;
  GOLD_CARAT_24 = 1;
  GOLD_CARAT_22 = 2;
  GOLD_CARAT_18 = 3;
}

message ArtAndArtefacts {
  string investment_name = 1 [(validate.rules).string = {min_len: 1, max_len: 100}];
  google.type.Money invested_value = 2 [(validate.rules).message.required = true];
  google.type.Money current_value = 3 [(validate.rules).message.required = true];
  google.type.Date purchase_date = 4;
}

message Bond {
  string investment_name = 1 [(validate.rules).string = {min_len: 1, max_len: 100}];
  int64 number_of_units = 2 [(validate.rules).int64.gt = 0];
  google.type.Money invested_value_per_unit = 3 [(validate.rules).message.required = true];
  google.type.Money current_value_per_unit = 4 [(validate.rules).message.required = true];
  google.type.Date investment_date = 5 [(validate.rules).message.required = true];
  google.type.Date maturity_date = 6 [(validate.rules).message.required = true];
}

message DigitalSilver {
  string investment_name = 1 [(validate.rules).string = {min_len: 1, max_len: 100}];
  google.type.Money invested_value = 2 [(validate.rules).message.required = true];
  double quantity_in_grams = 3 [(validate.rules).double.gt = 0];
  google.type.Date investment_date = 4;
}

message Cash {
  google.type.Money current_amount = 1 [(validate.rules).message.required = true];
  google.type.Date last_viewed_on = 2;
  string name = 3 [(validate.rules).string = {min_len: 0, max_len: 100}];
}

message FixedDepositDeclarationDetails {
  CompoundingFrequency compounding_frequency = 1;
  string deposit_name = 2;
}

message RecurringDepositDeclarationDetails {
  CompoundingFrequency compounding_frequency = 1;
  string deposit_name = 2;
}

enum CompoundingFrequency {
  COMPOUNDING_FREQUENCY_UNSPECIFIED = 0;
  COMPOUNDING_FREQUENCY_DAILY = 1;
  COMPOUNDING_FREQUENCY_MONTHLY = 2;
  COMPOUNDING_FREQUENCY_YEARLY = 3;
  COMPOUNDING_FREQUENCY_QUARTERLY = 4;
  COMPOUNDING_FREQUENCY_HALF_YEARLY = 5;
  // interest is compounded on maturity of the investment
  COMPOUNDING_FREQUENCY_AT_MATURITY = 6;
}

enum InvestmentDeclarationFieldMask {
  INVESTMENT_DECLARATION_FIELD_MASKS_UNSPECIFIED = 0;
  INVESTMENT_DECLARATION_FIELD_MASKS_ID = 1;
  INVESTMENT_DECLARATION_FIELD_MASKS_ACTOR_ID = 2;
  INVESTMENT_DECLARATION_FIELD_MASKS_INSTRUMENT_TYPE = 3;
  INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AMOUNT = 4;
  INVESTMENT_DECLARATION_FIELD_MASKS_INVESTED_AT = 5;
  INVESTMENT_DECLARATION_FIELD_MASKS_MATURITY_TIME = 6;
  INVESTMENT_DECLARATION_FIELD_MASKS_INTEREST_RATE = 7;
  INVESTMENT_DECLARATION_FIELD_MASKS_DECLARATION_DETAILS = 8;
  INVESTMENT_DECLARATION_FIELD_MASKS_CREATED_AT = 9;
  INVESTMENT_DECLARATION_FIELD_MASKS_UPDATED_AT = 10;
  INVESTMENT_DECLARATION_FIELD_MASKS_DELETED_AT = 11;
  INVESTMENT_DECLARATION_FIELD_MASKS_EXTERNAL_ID = 12;
  INVESTMENT_DECLARATION_FIELD_MASKS_CONSENT_ID = 13;
}

message PortfolioManagementService {
  string pms_name = 1 [(validate.rules).string = {min_len: 1, max_len: 100}];
  google.type.Date investment_date = 2 [(validate.rules).message.required = true];
  google.type.Date evaluation_date = 3 [(validate.rules).message.required = true];
  string amc_name = 4 [deprecated = true];
  google.type.Money invested_value = 6 [(validate.rules).message.required = true];
  google.type.Money current_value = 7 [(validate.rules).message.required = true];
  string folio_id = 8 [(validate.rules).string.max_len = 100];
  string broker_code = 9 [(validate.rules).string.max_len = 100];
  string remarks = 10 [(validate.rules).string.max_len = 100];

  oneof identifier {
    // A unique identifier of a PMS provider when it is chosen from a list of known providers.
    string amc_id = 11 [(validate.rules).string = {min_len: 1, max_len: 100}];

    // This name of a PMS provider submitted by user, instead of choosing from a list of known providers.
    // Represents the AMC name.
    string amc_name_v2 = 12 [(validate.rules).string = {min_len: 1, max_len: 100}];
  }
}

message PublicProvidentFund {
  InvestmentFrequency investment_frequency = 1 [(validate.rules).enum = {not_in: [0]}];
  google.type.Money deposit_amount = 2 [(validate.rules).message.required = true];
  google.type.Date investment_date = 3 [(validate.rules).message.required = true];
}

enum InvestmentFrequency {
  INVESTMENT_FREQUENCY_UNSPECIFIED = 0;
  INVESTMENT_FREQUENCY_ONE_TIME = 1;
  INVESTMENT_FREQUENCY_MONTHLY = 2;
  INVESTMENT_FREQUENCY_QUARTERLY = 3;
  INVESTMENT_FREQUENCY_HALF_YEARLY = 4;
  INVESTMENT_FREQUENCY_ANNUAL = 5;
}

// InterestAccumulationFrequency is used to get interest accrual period (number of times interest is calculated per year)
enum InterestAccumulationFrequency {
  INTEREST_ACCUMULATION_FREQUENCY_UNSPECIFIED = 0;
  INTEREST_ACCUMULATION_FREQUENCY_DAILY = 1;
  INTEREST_ACCUMULATION_FREQUENCY_MONTHLY = 2;
  INTEREST_ACCUMULATION_FREQUENCY_YEARLY = 3;
}

message EmployeeStockOption {
  CurrencyType currency_type = 1;
  string company_name = 2;
  int64 esop_in_grant = 3;
  google.type.Date first_issue_date = 4;
  VestingSchedule vesting_schedule = 5;
  google.type.Money current_value_per_share = 6;
}

enum CurrencyType {
  CURRENCY_TYPE_UNSPECIFIED = 0;
  CURRENCY_TYPE_INR = 1;
  CURRENCY_TYPE_USD = 2;
}

message VestingSchedule {
  repeated double yearly_vested_stock = 1;
}
