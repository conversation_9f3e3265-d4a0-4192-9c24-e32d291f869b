// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendornotification/notifications/moengage/service.proto

package moengage

import (
	moengage "github.com/epifi/gamma/api/vendors/moengage"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Area is defined to create a logical separation between groups of different attributes of different services
// that GetUserAttributes RPC will serve. it will also help in reusing same field names to fetch different information
// from different services. for example `summary` field can be fetched for both rewards and investments if area are
// defined as REWARDS and INVESTMENTS respectively.
type Area int32

const (
	Area_AREA_UNSPECIFIED  Area = 0
	Area_REWARDS           Area = 1
	Area_QUEST             Area = 2
	Area_INSIGHTS          Area = 3
	Area_PRE_APPROVED_LOAN Area = 4
	Area_ACCOUNTS          Area = 5
	Area_AREA_CREDIT_CARD  Area = 6
	Area_AREA_DEBIT_CARD   Area = 7
	Area_FI_STORE          Area = 8
	Area_AREA_STOCKS       Area = 9
	// Deprecated: Marked as deprecated in api/vendornotification/notifications/moengage/service.proto.
	Area_AA_SALARY_PROGRAM Area = 10
	Area_SALARY_PROGRAM    Area = 11
	Area_UPI_ID            Area = 12
	Area_PORTFOLIO_TRACKER Area = 13
)

// Enum value maps for Area.
var (
	Area_name = map[int32]string{
		0:  "AREA_UNSPECIFIED",
		1:  "REWARDS",
		2:  "QUEST",
		3:  "INSIGHTS",
		4:  "PRE_APPROVED_LOAN",
		5:  "ACCOUNTS",
		6:  "AREA_CREDIT_CARD",
		7:  "AREA_DEBIT_CARD",
		8:  "FI_STORE",
		9:  "AREA_STOCKS",
		10: "AA_SALARY_PROGRAM",
		11: "SALARY_PROGRAM",
		12: "UPI_ID",
		13: "PORTFOLIO_TRACKER",
	}
	Area_value = map[string]int32{
		"AREA_UNSPECIFIED":  0,
		"REWARDS":           1,
		"QUEST":             2,
		"INSIGHTS":          3,
		"PRE_APPROVED_LOAN": 4,
		"ACCOUNTS":          5,
		"AREA_CREDIT_CARD":  6,
		"AREA_DEBIT_CARD":   7,
		"FI_STORE":          8,
		"AREA_STOCKS":       9,
		"AA_SALARY_PROGRAM": 10,
		"SALARY_PROGRAM":    11,
		"UPI_ID":            12,
		"PORTFOLIO_TRACKER": 13,
	}
)

func (x Area) Enum() *Area {
	p := new(Area)
	*p = x
	return p
}

func (x Area) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Area) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendornotification_notifications_moengage_service_proto_enumTypes[0].Descriptor()
}

func (Area) Type() protoreflect.EnumType {
	return &file_api_vendornotification_notifications_moengage_service_proto_enumTypes[0]
}

func (x Area) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Area.Descriptor instead.
func (Area) EnumDescriptor() ([]byte, []int) {
	return file_api_vendornotification_notifications_moengage_service_proto_rawDescGZIP(), []int{0}
}

// UseCase is defined to create a logical separation between different use cases within an area
// It will define the behaviour in which the data will be processed against a webhook callback
type UseCase int32

const (
	UseCase_USE_CASE_UNSPECIFIED UseCase = 0
	UseCase_DROP_OFF_OUTCALL     UseCase = 1
)

// Enum value maps for UseCase.
var (
	UseCase_name = map[int32]string{
		0: "USE_CASE_UNSPECIFIED",
		1: "DROP_OFF_OUTCALL",
	}
	UseCase_value = map[string]int32{
		"USE_CASE_UNSPECIFIED": 0,
		"DROP_OFF_OUTCALL":     1,
	}
)

func (x UseCase) Enum() *UseCase {
	p := new(UseCase)
	*p = x
	return p
}

func (x UseCase) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UseCase) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendornotification_notifications_moengage_service_proto_enumTypes[1].Descriptor()
}

func (UseCase) Type() protoreflect.EnumType {
	return &file_api_vendornotification_notifications_moengage_service_proto_enumTypes[1]
}

func (x UseCase) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UseCase.Descriptor instead.
func (UseCase) EnumDescriptor() ([]byte, []int) {
	return file_api_vendornotification_notifications_moengage_service_proto_rawDescGZIP(), []int{1}
}

var File_api_vendornotification_notifications_moengage_service_proto protoreflect.FileDescriptor

var file_api_vendornotification_notifications_moengage_service_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x29, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x6f, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2a, 0x83, 0x02, 0x0a, 0x04, 0x41, 0x72, 0x65, 0x61, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x52, 0x45,
	0x41, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x53, 0x49, 0x47,
	0x48, 0x54, 0x53, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50,
	0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x52,
	0x45, 0x41, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x06,
	0x12, 0x13, 0x0a, 0x0f, 0x41, 0x52, 0x45, 0x41, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x49, 0x5f, 0x53, 0x54, 0x4f, 0x52,
	0x45, 0x10, 0x08, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x52, 0x45, 0x41, 0x5f, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x53, 0x10, 0x09, 0x12, 0x19, 0x0a, 0x11, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52,
	0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x10, 0x0a, 0x1a, 0x02, 0x08, 0x01, 0x12,
	0x12, 0x0a, 0x0e, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41,
	0x4d, 0x10, 0x0b, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x50, 0x49, 0x5f, 0x49, 0x44, 0x10, 0x0c, 0x12,
	0x15, 0x0a, 0x11, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x54, 0x52, 0x41,
	0x43, 0x4b, 0x45, 0x52, 0x10, 0x0d, 0x2a, 0x39, 0x0a, 0x07, 0x55, 0x73, 0x65, 0x43, 0x61, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x53, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x44,
	0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x4f, 0x55, 0x54, 0x43, 0x41, 0x4c, 0x4c, 0x10,
	0x01, 0x32, 0xc6, 0x02, 0x0a, 0x08, 0x4d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x12, 0x9d,
	0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x12, 0x2a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d,
	0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2f, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x29, 0x12, 0x27, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x2d, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x99,
	0x01, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x30, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x22,
	0x29, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x2d, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x42, 0x8c, 0x01, 0x0a, 0x44, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x5a, 0x44, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_vendornotification_notifications_moengage_service_proto_rawDescOnce sync.Once
	file_api_vendornotification_notifications_moengage_service_proto_rawDescData = file_api_vendornotification_notifications_moengage_service_proto_rawDesc
)

func file_api_vendornotification_notifications_moengage_service_proto_rawDescGZIP() []byte {
	file_api_vendornotification_notifications_moengage_service_proto_rawDescOnce.Do(func() {
		file_api_vendornotification_notifications_moengage_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendornotification_notifications_moengage_service_proto_rawDescData)
	})
	return file_api_vendornotification_notifications_moengage_service_proto_rawDescData
}

var file_api_vendornotification_notifications_moengage_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_vendornotification_notifications_moengage_service_proto_goTypes = []interface{}{
	(Area)(0),    // 0: vendornotification.notifications.moengage.Area
	(UseCase)(0), // 1: vendornotification.notifications.moengage.UseCase
	(*moengage.GetUserAttributesRequest)(nil),       // 2: vendors.moengage.GetUserAttributesRequest
	(*moengage.ProcessConnectorWebhookRequest)(nil), // 3: vendors.moengage.ProcessConnectorWebhookRequest
	(*moengage.GetUserAttributesResponse)(nil),      // 4: vendors.moengage.GetUserAttributesResponse
	(*emptypb.Empty)(nil),                           // 5: google.protobuf.Empty
}
var file_api_vendornotification_notifications_moengage_service_proto_depIdxs = []int32{
	2, // 0: vendornotification.notifications.moengage.Moengage.GetUserAttributes:input_type -> vendors.moengage.GetUserAttributesRequest
	3, // 1: vendornotification.notifications.moengage.Moengage.ProcessConnectorWebhook:input_type -> vendors.moengage.ProcessConnectorWebhookRequest
	4, // 2: vendornotification.notifications.moengage.Moengage.GetUserAttributes:output_type -> vendors.moengage.GetUserAttributesResponse
	5, // 3: vendornotification.notifications.moengage.Moengage.ProcessConnectorWebhook:output_type -> google.protobuf.Empty
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendornotification_notifications_moengage_service_proto_init() }
func file_api_vendornotification_notifications_moengage_service_proto_init() {
	if File_api_vendornotification_notifications_moengage_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendornotification_notifications_moengage_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendornotification_notifications_moengage_service_proto_goTypes,
		DependencyIndexes: file_api_vendornotification_notifications_moengage_service_proto_depIdxs,
		EnumInfos:         file_api_vendornotification_notifications_moengage_service_proto_enumTypes,
	}.Build()
	File_api_vendornotification_notifications_moengage_service_proto = out.File
	file_api_vendornotification_notifications_moengage_service_proto_rawDesc = nil
	file_api_vendornotification_notifications_moengage_service_proto_goTypes = nil
	file_api_vendornotification_notifications_moengage_service_proto_depIdxs = nil
}
