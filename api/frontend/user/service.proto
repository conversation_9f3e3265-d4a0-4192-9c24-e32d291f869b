// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package frontend.user;

import "api/accounts/account_type.proto";
import "api/frontend/account/account.proto";
import "api/frontend/account/upi/upi.proto";
import "api/frontend/analytics/analytics_screen_name.proto";
import "api/frontend/analytics/ms_clarity_config.proto";
import "api/frontend/chat_head.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/auth.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/smsscanner/scienaptic_sms_scan.proto";
import "api/frontend/user/enums.proto";
import "api/frontend/user/nominee.proto";
import "api/frontend/user/onboarding/pin_code.proto";
import "api/frontend/user/profile.proto";
import "api/frontend/user/user_activity/user_activity.proto";
import "api/frontend/user/user_preference.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/app_instance_ids.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/deeplink_screen_option/onboarding/fi_lite_screen_options.proto";
import "api/typesv2/form/field_value.proto";
import "api/typesv2/nominee.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/text_with_hyperlinks.proto";
import "google/type/date.proto";
import "google/type/latlng.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/user";
option java_package = "com.github.epifi.gamma.api.frontend.user";

// Frontend RPC service to facilitate operations on an Epifi user.
//
// User service provides for:
// 1. User profile management: creating, viewing and updating profile data. It involves:
//    a. Managing addresses.
//    b. Managing other user profile data like email, uer specific configurations like preferred language etc.
//
// 2. Re-oobe scenarios w.r.t update of user identifiers like phone number and email.
//
// 3. Any other user specific use cases.
service User {

  // RPC facilitates adding a new address.
  // We identify the type of address using addressType attribute.
  //
  // This is a synchronous call i.e. if there is an RPC failure, the client is
  // expected to call the RPC again for the retry.
  rpc AddAddress (AddAddressRequest) returns (AddAddressResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC facilitates updating an address.
  // There can be multiple addresses for a user. We identify the address to be updated using addressType attribute.
  //
  // This is a synchronous call i.e. if there is an RPC failure, the client is
  // expected to call the RPC again for the retry.
  rpc UpdateAddress (UpdateAddressRequest) returns (UpdateAddressResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC fetches the existing address for the given user.
  // There can be multiple addresses for a user. We identify the address to be updated using addressType attribute.
  // If address type is ADDRESS_TYPE_UNSPECIFIED, all the available addresses are fetched.
  rpc GetAddress (GetAddressRequest) returns (GetAddressResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC fetches all the addresses of a user (irrespective of the type of address) which can be used to deliver
  // them their (debit/credit) card during the on-boarding sign-up process.
  //
  // The response captures a list of addresses with their types in an ordered form from most likely (lower index) to least likely (higher index).
  // The first element represents the default address.
  // De-dupe is taken care in this list of addresses.
  rpc GetCardDeliveryAddresses (GetCardDeliveryAddressesRequest) returns (GetCardDeliveryAddressesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  //TODO(rohan): Consider moving this to a different service.
  // RPC fetches Pincode details.
  // Deprecated: Use GetPinCodeArea instead
  rpc GetPinCodeDetails (GetPinCodeDetailsRequest) returns (GetPinCodeDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC fetches area info for the requested PIN code.
  rpc GetPinCodeAreas (onboarding.GetPinCodeAreasRequest) returns (onboarding.GetPinCodeAreasResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to get user details to be shown on profile home screen
  rpc GetUserProfileHome (frontend.user.GetUserProfileHomeRequest) returns (frontend.user.GetUserProfileHomeResponse) {
    option (rpc.auth_required) = true;
  }

  // RPC to get user details to be shown on header section on profile home screen
  // https://www.figma.com/file/C5US4W4er2caIJQmLkdzHI/Account-Tiering-%E2%80%A2-FFF?node-id=739%3A11267&t=lMIzOteXRKMiCgyx-0
  rpc GetProfileHeaderSection (frontend.user.GetProfileHeaderSectionRequest) returns (frontend.user.GetProfileHeaderSectionResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch user profile details. This is maintained as a list to be flexible about adding properties later on.
  rpc GetUserFullProfile (frontend.user.GetUserFullProfileRequest) returns (frontend.user.GetUserFullProfileResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch the legal agreements of the user with epifi, will be fetched from s3 link.
  rpc GetLegalAgreements (frontend.user.GetLegalAgreementsRequest) returns (frontend.user.GetLegalAgreementsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to email document to user on registered email address.
  rpc EmailDocumentToUser (frontend.user.EmailDocumentToUserRequest) returns (frontend.user.EmailDocumentToUserResponse) {
    option (rpc.auth_required) = true;
  }

  // RPC to edit user properties like communication address, display image etc
  rpc EditUserProperties (frontend.user.EditUserPropertiesRequest) returns (frontend.user.EditUserPropertiesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Rpc to delete user image.
  rpc DeleteUserImage (frontend.user.DeleteUserImageRequest) returns (frontend.user.DeleteUserImageResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to set a given user privacy setting to given value from the client
  rpc SetUserPrivacySettings (frontend.user.SetUserPrivacySettingsRequest) returns (frontend.user.SetUserPrivacySettingsResponse) {}

  // RPC to Get user privacy settings that are backend driven
  rpc GetUserPrivacySettings (frontend.user.GetUserPrivacySettingsRequest) returns (frontend.user.GetUserPrivacySettingsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Update Mother, and Father name of user. This is required details in onboarding process for bank account creation etc.
  rpc UpdateMotherFatherName (UpdateMotherFatherNameRequest) returns (UpdateMotherFatherNameResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to sync the data between client and server on session start of the client
  // will be called per session by the client
  rpc GetUserSessionDetails (GetUserSessionDetailsRequest) returns (GetUserSessionDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Creates nominee for an actor. An actor can add  multiple nominees.
  rpc CreateNominee (CreateNomineeRequest) returns (CreateNomineeResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Fetches all nominees associated with a given actor
  rpc GetNominees (GetNomineesRequest) returns (GetNomineesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Fetches nominee details for the requested nominee ID
  rpc GetNominee (GetNomineeRequest) returns (GetNomineeResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Creates and persist shipping preference info for a shipping item(eg. Debit card) which is used to ship these items to the customer
  rpc ConfirmCardShippingPreference (ConfirmCardShippingPreferenceRequest) returns (ConfirmCardShippingPreferenceResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RecordHashedContacts stores the hashed contacts of a user in contacts table. This will be a background call from the
  // client, the call will be made incrementally in batches and client will wait for the calls to succeed
  rpc RecordHashedContacts (RecordHashedContactsRequest) returns (RecordHashedContactsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Rpc to sync the user's contacts. This will a background call from the client
  // and the user will not be blocked for the response
  // rpc will return all the contacts of the current user those are on fi
  // contact contains hashed phone number along with other info like verified name, icon url
  // contact also contains a boolean denoting if the contact is to be shown as new on fi or not
  rpc SyncContactDetails (SyncContactDetailsRequest) returns (SyncContactDetailsResponse) {
    option (rpc.auth_required) = true;
  }

  // Fetches high level details for all accounts of all types
  rpc GetAllAccounts (GetAllAccountsRequest) returns (GetAllAccountsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // SearchByPhoneNumber searches user by phone number
  rpc GetByPhoneNumber (GetByPhoneNumberRequest) returns (GetByPhoneNumberResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetAccountDetails fetches account details when account number and ifsc code is specified.
  // The response includes basic account details, nominee , upi and card details
  rpc GetAccountDetails (frontend.user.GetAccountDetailsRequest) returns (frontend.user.GetAccountDetailsResponse) {
    option (rpc.auth_required) = true;
  };

  // GetLegalName returns user's legal name which was entered on the email screen during onboarding.
  rpc GetLegalName (GetLegalNameRequest) returns (GetLegalNameResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // ConfirmCardPreferences checks if the user entered debit card name matches with KYC and PAN name and if we have user's shipping preference
  // for card delivery address and returns the next action to UI accordingly.
  rpc ConfirmCardPreferences (ConfirmCardPreferencesRequest) returns (ConfirmCardPreferencesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetDebitCardName returns the name entered by user during onboarding for debit card creation
  // We will return the email name in case user is on an older build.
  rpc GetDebitCardName (GetDebitCardNameRequest) returns (GetDebitCardNameResponse) {
    option (rpc.auth_required) = true;
  }

  // An user activity is an action perform on UI by user. Client will post any such activity that need to be tracked from BE
  // can post activity here.
  // For ex: For ETB(existing to bank) user, if user have once seen the message related to UPI pin, we do not want to show this message again.
  // currently message is driven from BE. We will track this activity posted on this RPC and will disable message for that user.
  rpc PostUserActivity (PostUserActivityRequest) returns (PostUserActivityResponse) {
    option (rpc.auth_required) = true;
  }

  // BlockActor RPC blocks an actor for the current logged in user and optionally mark report the user,
  // based on the `is_spam` flag in the request.
  // Once blocked, current actor wont be able to send/request any payment to the blocked actor. Further,
  // all the payment requests from the blocked actor will not be received by the current actor.
  // The blocked actor is never notified of being blocked by the current actor.
  //
  // In addition once reported, the blocked actor is marked as a spam to the internal system. Upon crossing
  // a defined tolerance along with a bunch of other parameters defined by spam engine,
  // the actor might get blocked across the platform permanently.
  //
  // Note: An actor can blocked only once at given point of time i.e. once blocked, the logged in actor can't block
  // the other actor again without unblocking first. Hence, client must call ReportSpam
  // if an already blocked actor is to be reported.
  rpc BlockActor (BlockActorRequest) returns (BlockActorResponse) {
    option (rpc.auth_required) = true;
  }

  // UnblockActor RPC unblocks an actor for the current actor and optionally revokes the spam reported.
  // Unblocking revokes the temporary restriction that was imposed for a blocked.
  rpc UnblockActor (UnblockActorRequest) returns (UnblockActorResponse) {
    option (rpc.auth_required) = true;
  }

  // ReportSpam reports spam for a given blocked actor. Once reported, the blocked actor is marked as a spam to the
  // internal system. Upon crossing a defined tolerance along with a bunch of other parameters defined by spam engine,
  // the actor might get blocked across the platform permanently.
  //
  // Note: The blocked actor can be reported only once by a logged in actor at a given point of time i.e. once reported
  // the logged in actor can't report the other actor again without unblocking first.
  rpc ReportSpam (ReportSpamRequest) returns (ReportSpamResponse) {
    option (rpc.auth_required) = true;
  }

  // Adds app instance IDs for different third party vendors. Eg. client_appsFlyer_id for AppsFlyer, app_instance_id for Firebase, etc.
  rpc AddAppInstanceIdentifiers (AddAppInstanceIdentifiersRequest) returns (AddAppInstanceIdentifiersResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // This RPC will contain Personal Details, Contact Details, and Employment Details
  rpc UserFullDetails (UserFullDetailsRequest) returns (UserFullDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to syc vendor IDs against a specific user.
  rpc SyncVendorIDs (SyncVendorIDsRequest) returns (SyncVendorIDsResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }
  // ValidateExternalAccount RPC validates if user entered bank credentials are valid
  // and belongs to the user.
  // This rpc has device_registration_required is set as false since this rpc is called after
  // a users account is closed (and app access revoked); where a lower level auth token is used.
  rpc ValidateExternalAccount (ValidateExternalAccountRequest) returns (ValidateExternalAccountResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // GetExternalAccount RPC fetches user's verified external accounts to transfer
  // their money to after account is closed.
  // This rpc has device_registration_required is set as false since this rpc is called after
  // a users account is closed (and app access revoked); where a lower level auth token is used.
  rpc GetExternalAccount (GetExternalAccountRequest) returns (GetExternalAccountResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // SetUserPreferences - RPC to set user preferences for a user
  // Request: accepts an actor_id and a list of preferences as input
  // Response: returns status code
  // if a preference already exists for a given preference_type, it will be updated with the new preference_value
  rpc SetUserPreferences (SetUserPreferencesRequest) returns (SetUserPreferencesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // SetCallLanguagePreferences returns the success screen
  // figma : https://www.figma.com/file/GGiPlpxpvYa2eHdg68m0pg/Help-%E2%80%A2-Workfile?type=design&node-id=4037-25418&mode=design&t=yj3hKSAsmCRtQNta-0
  // based on the language preference or suggested language selection
  rpc SetCallLanguagePreferences (SetCallLanguagePreferencesRequest) returns (SetCallLanguagePreferencesResponse) {
    option (rpc.auth_required) = true;
    // we are setting device registration option as false because
    // call us option will be shown in earlier onboarding stages as well
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }

  // GetUserPreferences - RPC to get user preferences of a user
  // Request: accepts an actor_id(mandatory) and a list of preference_types as input
  // Response: returns Status code and a list of preferences for the given actor_id and preference_types
  // if the list of preference_types is empty, all preferences for the actor will be returned
  rpc GetUserPreferences (GetUserPreferencesRequest) returns (GetUserPreferencesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc UpdateDOBForDedupeRetry (UpdateDOBForDedupeRetryRequest) returns (UpdateDOBForDedupeRetryResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.auth_required) = true;
  }

  // Fetches all required account details for given derived account id
  rpc GetAccountDetailsByDerivedId (GetAccountDetailsByDerivedIdRequest) returns (GetAccountDetailsByDerivedIdResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Fetch the MoengageID from the vendorMapping service
  rpc GetMoEngageId (GetMoEngageIdRequest) returns (GetMoEngageIdResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetProfilePageSections takes page_type as request and returns the ordered list of sections to be rendered on the client
  rpc GetProfilePageSections (GetProfilePageSectionsRequest) returns (GetProfilePageSectionsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc GetProfileSettingPageSection (GetProfileSettingPageSectionRequest) returns (GetProfileSettingPageSectionResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // UpdateFormDetails RPC is used to update user details based on the field masks and values sent by the client.
  rpc UpdateFormDetails (UpdateFormDetailsRequest) returns (UpdateFormDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // FetchFormDetails RPC is used for fetching user's PII info to be presented on a form. A form is used
  // in the sense of the web term where the screen has text fields that needs to be prefilled.
  // The main use case of this API is to send PII data for auto fill since we can't send it in DL screen options.
  rpc FetchFormDetails (FetchFormDetailsRequest) returns (FetchFormDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetPopulatedAddressEntryScreen fetches address entry screen with some fields pre-populated based on the location passed in the request
  rpc GetAddressEntryScreenFromLocation (GetAddressEntryScreenFromLocationRequest) returns (GetAddressEntryScreenFromLocationResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.location_required) = LOCATION_CHECK_REQUIRED;
  };

  // GetFeatureBenefitsScreenOptions RPC is used screen options required for FEATURE_BENEFITS deeplink
  rpc GetFeatureBenefitsScreenOptions (GetFeatureBenefitsScreenOptionsRequest) returns (GetFeatureBenefitsScreenOptionsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // SendSmsData RPC stores sms data sent from client based on type of sms data
  rpc SendSmsData (SendSmsDataRequest) returns (SendSmsDataResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };
}

message GetFeatureBenefitsScreenOptionsRequest {
  frontend.header.RequestHeader req = 1;
  // entry point from where the deeplink got invoked
  // string of enum api.typesv2.deeplink_screen_option.onboarding.FeatureOnboardingEntryPoint
  string entry_point = 2;
}

message GetFeatureBenefitsScreenOptionsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions feature_benefits_screen_options = 2;
}

message GetProfileSettingPageSectionRequest {
  frontend.header.RequestHeader req = 1;
}

message SetCallLanguagePreferencesRequest {
  frontend.header.RequestHeader req = 1;
  // Screen Name from where this request is coming from eg: Home, Explore
  analytics.AnalyticsScreenName entry_point = 2;
  // list of preferences to be set for the requesting user(actor_id)
  user.PreferenceTypeValuePair preferences = 3;
}

message SetCallLanguagePreferencesResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Title of the section eg: "Call language preference has been set successfully"
  api.typesv2.common.Text title = 2;
  // Desc of the section eg: "You can edit your language preference from Profile > Settings"
  api.typesv2.common.Text desc = 3;
  // Info text for edit language preferences
  api.typesv2.common.Text edit_pref_info = 4;
  // Info Icon Component to display additional information
  api.typesv2.ui.IconTextComponent info_icon = 5;
}

message GetProfileSettingPageSectionResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // html text for closing Fi savings account with an deeplink action that takes user to account closure flow
  api.typesv2.common.Text account_closure_text = 2 [deprecated = true];
  // html text with deeplink to navigate to a screen [required for iOS as application is getting relaunch on click of Deeplink]
  api.typesv2.ui.TextWithHyperlinks account_closure_hyperlinks = 3;
}

message UpdateDOBForDedupeRetryRequest {
  frontend.header.RequestHeader req = 1;
  google.type.Date date_of_birth = 4;
}

message UpdateDOBForDedupeRetryResponse {
  frontend.header.ResponseHeader resp_header = 1;

  frontend.deeplink.Deeplink next_action = 2;

  // max attempts for dedupe dob mismatch
  int32 max_dob_dedupe_attempts = 3;

  // current count of attempts for dedupe dob mismatch
  int32 current_dob_dedupe_attempts = 4;
}



message GetDebitCardNameRequest {
  frontend.header.RequestHeader req = 1;
}

message GetDebitCardNameResponse {
  rpc.Status status = 1;
  api.typesv2.common.Name debit_card_name = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message AddAddressRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  api.typesv2.AddressType type = 2;
  api.typesv2.PostalAddress address = 4 [(validate.rules).message.required = true];
  // location token captured against collected address, if exists
  string location_token = 5;
}

message AddAddressResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  string id = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message UpdateAddressRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  api.typesv2.AddressType type = 2;
  string id = 3;
  api.typesv2.PostalAddress address = 4 [(validate.rules).message.required = true];
}

message UpdateAddressResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetAddressRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  api.typesv2.AddressType type = 2;
}

message GetAddressResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  repeated AddressInfo addresses = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetCardDeliveryAddressesRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // flow for which addresses needs to be fetched
  // if not sent we will return all the addresses present for a user
  AddressFlow address_flow = 2;
}

// Flow for which addresses needs to be fetched
enum AddressFlow {
  ADDRESS_FLOW_UNSPECIFIED = 0;
  ADDRESS_FLOW_ONBOARDING = 1;
  ADDRESS_FLOW_DEBIT_CARD = 2;
  ADDRESS_FLOW_CREDIT_CARD = 3;
}

message GetCardDeliveryAddressesResponse {
  rpc.Status status = 1;
  // deprecated: use addressesWithType instead
  repeated api.typesv2.PostalAddress addresses = 2;
  repeated AddressWithType addressesWithType = 3;
  bool enable_address_update = 4;
  frontend.header.ResponseHeader resp_header = 15;
}

message AddressInfo {
  string id = 1;
  api.typesv2.AddressType type = 2;
  api.typesv2.PostalAddress address = 3;
}

// Deprecated: Use GetPinCodeAreaRequest instead
message GetPinCodeDetailsRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  string pincode = 2;
}

// Deprecated: Use GetPinCodeAreaResponse instead
message GetPinCodeDetailsResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  string city = 2;
  string state = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message UpdateMotherFatherNameRequest {
  // Auth header
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // mother name
  string mother_name = 2;
  // father name
  string father_name = 3;
}

message UpdateMotherFatherNameResponse {
  rpc.Status status = 1;
  // next action
  frontend.deeplink.Deeplink next_action = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetUserSessionDetailsRequest {
  // Auth header
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

message GetUserSessionDetailsResponse {
  enum Status {
    OK = 0;
    // record not found
    RECORD_NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  message AccountDetails {
    // account id belonging to the user
    string account_id = 1;
    // boolean denoting if UPI pin is set for the account
    bool is_upi_pin_set = 2 [deprecated = true];
    // PIN_NOT_SET -> UPI pin is not set
    // PIN_SET -> UPI pin is not set
    // REOOBE_PIN_NOT_SET -> UPI pin is not set after reoobe
    frontend.account.upi.PinSetState pin_set_state = 3;
    // masked account number
    string masked_account_number = 4;
    // partner bank name
    string partner_bank = 5;
    // bank logo url of partner bank
    string bank_logo_url = 6;
    // account type (ex. Saving account, current account etc.)
    accounts.Type account_type = 7;
  }
  // list of account details for the user
  // Deprecated: Account object is standardized. Use accounts field instead of account_details.
  repeated AccountDetails account_details = 2 [deprecated = true];

  // list of card ids that exist for the user
  repeated string card_ids = 3;

  // id to be passed when fetching remote config from firebase
  string firebase_id = 4;

  // user properties to be passed while fetching remote config from firebase
  map<string, string> firebase_user_properties = 5;

  // Display Name entered by the user during Onboarding.
  // This name is also the one printed on the user's Debit card.
  // TODO(team): Currently Display Name is not a savings account level property but a user level property.
  //             In future, if multiple savings accounts exist per user, each account can have it's own
  //             display name, making display name a savings account level property.
  //             When this happens, amendments will be required here as well here as well
  api.typesv2.common.Name display_name = 6;

  frontend.header.ResponseHeader resp_header = 15;

  // list of account details for the user
  repeated frontend.account.Account accounts = 7;

  // map of feature as key to value defined via FeatureInfo
  // Feature enum is defined in api/types/feature.proto
  // For eg:
  // {
  //    "INVESTMENT_MF_UI" : {"enable": "true"}
  //    "PAY_VIA_PHONE_NUMBER" : {"enable": "false"},
  //    "MF_ADVANCE_FILTER" : {"enable": "true"},
  // }
  map<string, FeatureInfo> feature_map = 8;

  // true specifies user has completed savings account onboarding
  bool is_sa_onboarded = 9;

  // indicates if the it's is a fi lite user
  bool is_fi_lite_user = 10;

  // indicates whether user has access to home
  bool is_home_accessible = 11;

  // current feature user is onboarding on (CC/SA/PL)
  string current_feature = 12;

  analytics.MsClarityConfig ms_clarity_config = 13;

  // If this config is nil, then client will not change the existing config
  // If this config is not nil, then client will update the existing config with this config
  smsscanner.ScienapticSmsScannerConfig scienaptic_sms_scanner_config = 14;

  // Custom user id for Ms Clarity for a user, genereated by the backend in BE vendor
  // mapping. Clarity Ref: https://learn.microsoft.com/en-us/clarity/mobile-sdk/android-sdk?tabs=kotlin#setcustomuserid
  string ms_clarity_id = 16;

  // Config for cards tab UI.
  // Figma : https://www.figma.com/design/FWW6omNbhzvzwBqlMT03am/D'fixit---workfile?node-id=816-102651&t=43YzeFO8SVuQNz6g-4
  // https://www.figma.com/design/FWW6omNbhzvzwBqlMT03am/D'fixit---workfile?node-id=816-102847&t=43YzeFO8SVuQNz6g-4
  CardTabs card_tabs = 17;
  message CardTabs {
    // Background color for tab container
    api.typesv2.common.ui.widget.BackgroundColour bg_tabs = 1;
    // Corner radius of the tab container
    int32 corner_radius = 2;
    repeated Tab cards_tab = 3;

    // Tab contains the tab type and the selected/unselected tab UI
    message Tab {
      TabType tab_type = 1;
      api.typesv2.ui.IconTextComponent unselected_tab = 2;
      api.typesv2.ui.IconTextComponent selected_tab = 3;
    }
    enum TabType {
      TAB_TYPE_UNSPECIFIED = 0;
      DEBIT_CARD = 1;
      CREDIT_CARD = 2;
    }
  }
}

message CreateNomineeRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // Deprecated in favour of nominee_v2 [frontend.user.Nominee]
  api.typesv2.Nominee nominee = 2 [deprecated = true];
  // Entrypoint from where the nominee update is initiated
  // For entry_point 'wealth_onboarding' backend has checks if nominee and user's PAN are the same
  // maps to typesV2.NomineeEntryPoint
  string entry_point = 3;
  frontend.user.Nominee nominee_v2 = 4;
}

message CreateNomineeResponse {
  rpc.Status status = 1;
  string nominee_id = 2;

  enum Status {
    // Success
    OK = 0;
    // Internal error
    INTERNAL = 13;
    // Failed precondition: Nominee specified by the client cannot be the same is account holder
    FAILED_PRECONDITION_NOMINEE_SAME_AS_ACCOUNT_HOLDER = 461;
    // Failed precondition: Nominee's guardian specified by the client can not be the same is account holder
    FAILED_PRECONDITION_GUARDIAN_SAME_AS_ACCOUNT_HOLDER = 462;
  }
  frontend.header.ResponseHeader resp_header = 15;
  // Deeplink to redirect user incase nominee update success
  // optional
  frontend.deeplink.Deeplink deeplink = 3;
}

message GetNomineesRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // using flow_type to show nominees based on certain conditions
	// Usecase: For flow_type 'wealth_onboarding' only nominees who have documents
	// like pan, aadhaar or driving license, email and mobile number will be shown
  // maps to typesV2.NomineeEntryPoint
  string flow_type = 2;
}

message GetNomineesResponse {
  rpc.Status status = 1;
  // Deprecated in favour of nominees_v2 [frontend.user.Nominee]
  repeated api.typesv2.Nominee nominees = 2 [deprecated = true];
  frontend.header.ResponseHeader resp_header = 15;
  repeated frontend.user.Nominee nominees_v2 = 3;
}

message GetNomineeRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  string nominee_id = 2;
}

message GetNomineeResponse {
  rpc.Status status = 1;
  // Deprecated in favour of nominee_v2 [frontend.user.Nominee]
  api.typesv2.Nominee nominee = 2 [deprecated = true];
  frontend.header.ResponseHeader resp_header = 15;
  frontend.user.Nominee nominee_v2 = 3;
}

message ConfirmCardShippingPreferenceRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // Type of address chosen by the user.
  // This can be either PERMANENT or MAILING (for address fetched from KYC) or SHIPPING (for address manually entered by the user)
  api.typesv2.AddressType address_type = 2;
}

message ConfirmCardShippingPreferenceResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message RecordHashedContactsRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  message Contact {
    // MD5 hashed phone number
    string phone_number_hash = 1;
    // boolean denoting if the user has deleted this
    // contact from the contact list
    bool is_deleted = 2;
  }
  // list of MD5 hashed contacts. Maximum of 250 contacts can be sent per rpc call by the client
  repeated Contact contact = 2 [(validate.rules).repeated.max_items = 250];
}

message RecordHashedContactsResponse {
  enum Status {
    // rpc status ok
    OK = 0;
    // server error
    INTERNAL = 13;
    // rpc has reached the rate limit
    // client should retry again in the next retry interval
    RATE_LIMIT_EXCEEDED = 100;
  }
  rpc.Status status = 1;
  frontend.header.ResponseHeader resp_header = 15;
}

message AddressWithType {
  api.typesv2.AddressType type = 1;
  api.typesv2.PostalAddress address = 2;
  // We are reusing this rpc to fill the nominee address
  bool is_editable = 3;
  // Indicates whether the address is enabled or disabled for selection as delivery address for card
  bool is_active = 4;
  // Additional information associated with the address, such as the reason it cannot be selected as a delivery address.
  api.typesv2.ui.IconTextComponent info_text = 5;
}

message SyncContactDetailsRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  // cursor is a bytes field which encodes the information needed by the backend to perform optimisations on db calls to fetch contact information.
  // It stores info about the contacts already synced and ensures that only the diff in contacts is sent to the client.
  // This field should be populated from the SyncContactDetailsResponse. It's an optional field. It can be empty when called for the first time.
  // Subsequent calls should have this populated from the last response.
  bytes cursor = 2;
  frontend.header.RequestHeader req = 15;
}

message SyncContactDetailsResponse {
  enum Status {
    // rpc status ok
    OK = 0;
    // server error
    INTERNAL = 13;
    // rpc has reached the rate limit
    // client should retry again in the next retry interval
    RATE_LIMIT_EXCEEDED = 100;
  }
  rpc.Status status = 1;

  message ContactDetails {
    // phone number hash for the contact details
    string phone_number_hash = 1;
    // verified name of the contact as stored on the server
    // will be empty in case the contact is not on fi
    string verified_name = 2;
    // profile url of the contact user
    // will be empty in case the contact is not on fi
    string icon_url = 3;

    // bool denoting if the contact is to be shown as new on fi or not
    bool is_new_on_fi = 4;

    // optional: in case icon image is missing
    // then the client needs to generate the image
    // using name full_title and color_code as background
    // rgb hex will be returned as colour code
    string colour_code = 5;
  }
  // list of contact details
  repeated ContactDetails contact_details = 2;
  // Cursor sent by backend which the client stores at their end such that it is sent in next request for backend to
  // perform filtered queries
  bytes cursor = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetByPhoneNumberRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // phone number of the user to be searched
  api.typesv2.common.PhoneNumber phone_number = 2;
}

message GetByPhoneNumberResponse {
  enum Status {
    // success
    OK = 0;
    // no user found for the given input identifier
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }

  // Denotes the status of the request
  rpc.Status status = 1;

  // chat head of the fi user
  frontend.ChatHead chat_head = 2 [deprecated = true];
  // List fo chat heads for the phone number
  repeated frontend.ChatHead chat_heads = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetLegalNameRequest {
  frontend.header.RequestHeader req = 1;
}

message GetLegalNameResponse {
  rpc.Status status = 1;
  api.typesv2.common.Name legal_name = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message ConfirmCardPreferencesRequest {
  frontend.header.RequestHeader req = 1;
  // debit card name entered by user
  api.typesv2.common.Name debit_card_name = 2;
  // name check can be skipped if user uses the existing legal name as debit card name.
  // If user does not changes his legal name UI needs to be pass this flag as true.
  bool skip_name_check = 3;
  // Type of address chosen by the user.
  // This can be either PERMANENT or MAILING (for address fetched from KYC) or SHIPPING (for address manually entered by the user)
  api.typesv2.AddressType address_type = 4;
}

message ConfirmCardPreferencesResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

// request to post user activity on screen
message PostUserActivityRequest {
  frontend.header.RequestHeader req = 15;
  // use derived_account_id instead
  string account_id = 1 [deprecated = true];
  // activity made by user than need to be tracked
  frontend.user.user_activity.UserActivity user_activity = 2;
  string derived_account_id = 3 [(validate.rules).string = {min_len: 1}];
}

message PostUserActivityResponse {
  enum Status {
    // success
    OK = 0;
    // no user found for the given input identifier
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }

  // Denotes the status of the request
  rpc.Status status = 1;
  frontend.header.ResponseHeader resp_header = 15;
}

message BlockActorRequest {
  frontend.header.RequestHeader req = 1;

  // identifier of the actor who is supposed to be blocked
  string actor_id = 2 [(validate.rules).string.min_len = 1];

  // flag specifying whether to report actor as spam along with blocking
  bool is_spam = 3;
}

message BlockActorResponse {
  enum Status {
    // call successful
    OK = 0;
    // invalid request arguments
    INVALID_ARGUMENT = 3;
    // internal server error
    INTERNAL = 13;
    // actor is already blocked by logged in actor
    ALREADY_PROCESSED = 50;
  }
  frontend.header.ResponseHeader resp_header = 15;
}

message UnblockActorRequest {
  frontend.header.RequestHeader req = 1;

  // identifier of the actor who is supposed to be unblocked
  string actor_id = 2 [(validate.rules).string.min_len = 1];
}

message UnblockActorResponse {
  enum Status {
    // call successful
    OK = 0;
    // invalid request arguments
    INVALID_ARGUMENT = 3;
    // internal server error
    INTERNAL = 13;
    // actor already unblocked
    ALREADY_PROCESSED = 50;
  }

  frontend.header.ResponseHeader resp_header = 1;
}

message ReportSpamRequest {
  frontend.header.RequestHeader req = 1;

  // identifier of the actor who is supposed to be reported as spam.
  // the actor must be already blocked and not marked as spam by the logged in actor for the
  // RPC to succeed
  string actor_id = 2 [(validate.rules).string.min_len = 1];
}

message ReportSpamResponse {
  enum Status {
    // call successful
    OK = 0;
    // invalid request arguments
    INVALID_ARGUMENT = 3;
    // internal server error
    INTERNAL = 13;
    // actor is already marked as spam by logged in actor
    ALREADY_PROCESSED = 50;
    // actor not blocked by the logged in actor
    ACTOR_NOT_BLOCKED = 100;
  }

  frontend.header.ResponseHeader resp_header = 1;
}

message AddAppInstanceIdentifiersRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // List of various IDs form different vendors as well as internal app instance IDs (eg. Prospect ID)
  // This can be considered as a mapping of ID name -> ID value, where ID name is expected to be unique
  repeated api.typesv2.AppInstanceId identifiers = 2;
}

message AddAppInstanceIdentifiersResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message SyncVendorIDsRequest {
  frontend.header.RequestHeader req = 1;
  // List of various IDs form different vendors as well as internal app instance IDs (eg. Prospect ID)
  // Identifiers passed wil be be synced in DB.
  repeated api.typesv2.AppInstanceId identifiers = 2;
}

message SyncVendorIDsResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message ValidateExternalAccountRequest {
  frontend.header.RequestHeader req = 1;
  string account_number = 2 [(validate.rules).string = {min_len: 5, max_len: 32}];
  string ifsc_code = 3 [(validate.rules).string = {len: 11}];
  string user_given_name = 4 [(validate.rules).string = {min_len: 1, max_len: 100}];
}

message ValidateExternalAccountResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
  frontend.user.AccountValidationFailureReason failure_reason = 3;
}

message GetExternalAccountRequest {
  frontend.header.RequestHeader req = 1;
}

message GetExternalAccountResponse {
  frontend.header.ResponseHeader resp_header = 1;
  string account_number = 2 [deprecated = true];
  string ifsc_code = 3 [deprecated = true];
  string account_holder_name = 4 [deprecated = true];
  string bank_logo_url = 5;
  // represents name
  string title = 6;
  // can be any of the bank details like bank name, branch
  string line1 = 7;
  // can be any of the bank details like bank name, branch
  string line2 = 8;
}

message FeatureInfo {
  bool enable = 1;
}

message SetUserPreferencesRequest {
  frontend.header.RequestHeader req = 1;
  // list of preferences to be set for the requesting user(actor_id)
  repeated PreferenceTypeValuePair preferences = 2 [(validate.rules).repeated.min_items = 1];
}

message SetUserPreferencesResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message GetUserPreferencesRequest {
  frontend.header.RequestHeader req = 1;
  // List of preference_types to be fetched
  // optional field: if the list is empty all preference_types for the user will be fetched
  repeated PreferenceType preference_types = 2;
}

message GetUserPreferencesResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // list of preferences for the given user and preference_types
  repeated UserPreference user_preferences = 2;
}

message GetAccountDetailsByDerivedIdRequest {
  frontend.header.RequestHeader req = 1;
  // derived_account_id - account id derived from combination of :
  // internal account id, tpap account id, connected account id, deposit account id
  string derived_account_id = 2;
}

message GetAccountDetailsByDerivedIdResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // user account details fetched from derived account id
  frontend.user.UserAccountDetails user_account_details = 2;
}

message GetMoEngageIdRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}

message GetMoEngageIdResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Moengage ID from VendorMapping
  string moengage_id = 2;
}

message UpdateFormDetailsRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];

  // source flow for analytics purposes. For simplicity, any business logic on this is not advised.
  string source = 2;

  // data collected from the user in the form. The key for the map is the field_id corresponding to the respective InputField
  map<string, api.typesv2.form.FieldValue> values = 3;

  // list of consents collected from the user
  repeated string consent_ids = 4;
}

message UpdateFormDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // field_id <> error message mapping. The error message has to be shown inline
  map<string, api.typesv2.common.Text> field_inline_error = 2;
}

message FetchFormDetailsRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];

  // source flow for analytics purposes. For simplicity, any business logic on this is not advised.
  string source = 2;

  // Client needs to pass the field_id sent with EditableFields in the UPDATE_USER_DETAILS screen options
  // On backend, it maps to ENUM api.typesv2.Form.FieldIdentifier
  repeated string field_ids = 3;
}

message FetchFormDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // PII data that needs to be pre-populated on the form. Key of the map is the field_id corresponding to the respective InputField or Card
  map<string, api.typesv2.form.FieldValue> values = 2;
}

message GetAddressEntryScreenFromLocationRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];

  // location reference for the address entry
  google.type.LatLng lat_lng = 2;

  api.typesv2.AddressType type = 3;
}

message GetAddressEntryScreenFromLocationResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // address entry screen
  frontend.deeplink.Deeplink next_action = 2;
}

message SendSmsDataRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];

  repeated SmsData sms_data = 2;
}

enum SmsDataError {
  SMS_DATA_ERRORS_UNSPECIFIED = 0;
  SMS_DATA_ERRORS_PERMISSION_DENIED = 1;
  SMS_DATA_ERRORS_DATA_NOT_FOUND = 2;
}

message SmsData {
  SmsDataError sms_data_error = 1;

  // Type of data to be stored
  oneof sms_data_type {
    string pan = 2;
  }
}

message SendSmsDataResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

