// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package frontend.rewards;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/auth.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/rewards/card_offer.proto";
import "api/frontend/rewards/catalog_offer.proto";
import "api/frontend/rewards/claimed_reward.proto";
import "api/frontend/rewards/display_components.proto";
import "api/frontend/rewards/exchanger_offer.proto";
import "api/frontend/rewards/exchanger_order.proto";
import "api/frontend/rewards/filters.proto";
import "api/frontend/rewards/offer.proto";
import "api/frontend/rewards/order.proto";
import "api/frontend/rewards/page.proto";
import "api/frontend/rewards/pkg/display_components.proto";
import "api/frontend/rewards/redeemed_offer.proto";
import "api/frontend/rewards/reward.proto";
import "api/frontend/rewards/reward_offer.proto";
import "api/frontend/rewards/reward_wrapper.proto";
import "api/frontend/search/widget/widgets.proto";
import "api/rpc/method_options.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/input.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/filter_views.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/typesv2/ui/text_with_hyperlinks.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "api/typesv2/ui/widget_themes.proto";
import "api/typesv2/ui/sdui/components/spacer.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/rewards";
option java_package = "com.github.epifi.gamma.api.frontend.rewards";

// deprecated
message GetActiveRewardOffersRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

// deprecated
message GetActiveRewardOffersResponse {
  // rpc status
  rpc.Status status = 1;
  // list of active reward offerings now
  repeated frontend.rewards.RewardOffer reward_offers = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

// deprecated
message GetRewardOfferDetailsRequest {
  frontend.header.RequestHeader req = 1;
  // id of offer whose details need to be fetched.
  string reward_offer_id = 2;
}

// deprecated
message GetRewardOfferDetailsResponse {
  // rpc status
  rpc.Status status = 1;
  // reward offer
  frontend.rewards.RewardOffer reward_offer = 2;

  frontend.header.ResponseHeader resp_header = 15;
}

message GetRewardDigestRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

message GetRewardDigestResponse {
  // rpc status
  rpc.Status status = 1;
  // unopened rewards counts
  uint32 unopended_rewards_count = 2;
  // current fi-coin balance
  uint32 fi_coins_balance = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetRewardSummaryRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

message GetRewardSummaryResponse {
  // rpc status
  rpc.Status status = 1;
  // current fi-coin balance
  uint32 fi_coins_balance = 2;
  // Deprecated
  // represents total sd reward amount earned.
  // Naming of this field is incorrect, so deprecating it in favour of total_sd_reward_amount_earned
  api.typesv2.Money worth_saved = 3 [deprecated = true];
  // total amount of fi coins that have been
  // claimed but are not processed yet.
  uint32 in_process_fi_coins_amount = 4 [deprecated = true];
  // Deprecated
  // represents total sd reward amount claimed but not processed yet.
  // Naming of this field is incorrect, so deprecating it in favour of total_in_process_sd_reward_amount
  api.typesv2.Money in_process_cash_amount = 5 [deprecated = true];
  // total cash reward amount earned
  // deprecated in favour of reward_units_summaries
  api.typesv2.Money total_cash_reward_amount_earned = 6 [deprecated = true];
  // total cash reward amount which is claimed but not yet processed.
  // deprecated in favour of reward_units_summaries
  api.typesv2.Money total_in_process_cash_reward_amount = 7 [deprecated = true];
  // total sd reward amount earned
  // deprecated in favour of reward_units_summaries
  api.typesv2.Money total_sd_reward_amount_earned = 8 [deprecated = true];
  // total sd reward amount which is claimed but not yet processed.
  // deprecated in favour of reward_units_summaries
  api.typesv2.Money total_in_process_sd_reward_amount = 9 [deprecated = true];

  frontend.header.ResponseHeader resp_header = 15;

  repeated RewardUnitsSummary reward_units_summaries = 16;

  // RewardUnitsSummary will contain details of the summary (total processed, pending, etc.) of a reward type
  message RewardUnitsSummary {
    // denotes the name of the reward unit, eg. Fi-coins, cashback, etc.
    api.typesv2.ui.IconTextComponent reward_unit_name = 1;
    // denotes the amount of reward units that have been credited to the user's account
    api.typesv2.ui.IconTextComponent processed_reward_units_value = 2;
    // denotes the amount of reward units that are not credited yet and are in an intermediate state like LOCKED, PROCESSING, etc.
    api.typesv2.ui.IconTextComponent pending_reward_units_value = 3;
    // denotes the custom action that shows detailed breakdown of the reward units in pending state
    // can be nil if no details are to be shown
    PendingRewardsBreakdown pending_rewards_breakdown = 4;
  }

  // PendingRewardsDetails will be used to show details of rewards that haven't been credited to user's account yet
  // figma - https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=18229-33074&mode=design&t=kOYbPDnBo5hqrIQA-0
  message PendingRewardsBreakdown {
    // title of the bottom sheet
    api.typesv2.common.Text title = 1;

    // rows containing details of the pending reward
    repeated Row rows = 2;

    message Row {
      // icon to be shown for the row, representing the reward type
      api.typesv2.common.VisualElement icon = 1;
      // heading of the row, status of the rewards for which this row is showing details
      api.typesv2.common.Text status = 2;
      // status related info
      api.typesv2.common.Text desc = 3;
      // reward unit aggregates falling in this status
      api.typesv2.ui.IconTextComponent reward_units = 4;
    }
  }
}

message GetEarnedRewardsRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // page context to help server fetch the page
  PageContextRequest page_context = 2;
}

message GetEarnedRewardsResponse {
  // rpc status
  rpc.Status status = 1;
  // page context to help client fetch next page
  PageContextResponse page_context = 2;
  // list of claimed and unclaimed rewards
  repeated frontend.rewards.Reward rewards = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetRewardsForActorV1Request {
  frontend.header.RequestHeader req = 1;

  Filters filters = 2;

  // intentional gap in field number to accommodate new fields
  // page context to help server fetch the page
  // Note : pass only after-token for pagination, two way pagination is not supported currently for this rpc.
  PageContextRequest page_context = 5;

  message Filters {
    RewardTileStatus reward_tile_status = 1;
    // id of the reward to be fetched
    string reward_id = 2;
  }

  enum RewardTileStatus {
    // will fetch all reward tiles
    REWARD_TILE_STATUS_UNSPECIFIED = 0;
    // we'll only fetch those reward tiles that have the reward amount visible
    REWARD_TILE_STATUS_OPENED = 1;
    // we'll only fetch those reward tiles that show the money plants and not the reward value
    REWARD_TILE_STATUS_UNOPENED = 2;
  }
}

message GetRewardsForActorV1Response {
  // rpc status
  rpc.Status status = 1;
  // list of rewards
  repeated frontend.rewards.RewardWrapperV1 rewards = 2;
  // page context to help client fetch next page
  // Note : use only after-token for pagination, two way pagination is not supported currently for this rpc.
  PageContextResponse page_context = 3;

  // CTA to be used for claiming multiple rewards at once. it can be nil if we don't want to show the CTA
  CTA bulk_claim_rewards_cta = 4 [deprecated = true];

  // information to be shown on the rewards screen, like "Some rewards are under processing"
  api.typesv2.common.Text rewards_status_text = 5 [deprecated = true];

  // Cta for Common actions across unopened rewards, like claiming all rewards at one go.
  // Currently, this can be sent with any page, clients are expected to keep showing the CTA, once they receive the CTA
  // from n-th page onwards: https://epifi.slack.com/archives/C02VCMA4EU9/p1699271931805919?thread_ts=1699267899.424869&cid=C02VCMA4EU9
  // Ref: https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=19125-48780&mode=design&t=Xc6fEIYrx5zYrr5I-4
  IconTextCta bulk_claim_rewards_cta_v2 = 6;

  // reward summary widget to be shown on the top of rewards screen
  // client can show this when it is not nil
  search.widget.RewardSummaryWidget summary_widget = 7;

  frontend.header.ResponseHeader resp_header = 15;
}

message GetMyRewardsScreenDetailsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetMyRewardsScreenDetailsResponse {
  // rpc status
  rpc.Status status = 1;
  // different sections present on the screen
  repeated Section sections = 2;
  // (optional) cta on top nav bar ex- ? icon (help section)
  CtaV1 nav_bar_cta = 3;

  frontend.header.ResponseHeader resp_header = 15;

  message Section {
    // title of the section to be shown on the header(if any). e.g. Unopened Money-plants
    api.typesv2.common.Text header_title = 1;
    // CTA to be shown on the header, can be nil. e.g. CTA for viewing all unopened/opened money plants
    CtaV1 header_cta = 2;
    oneof SectionDetails {
      // contains summary of all rewards earned by the user
      RewardsSummary rewards_summary = 3;
      // contains widgets that take the user to different rewards related pages like offer catalog, ways to earn page
      Widgets widgets = 4;
      // contains unopened rewards for actor
      UnopenedRewardTiles unopened_reward_tiles = 5;
      // contains opened rewards for actor
      OpenRewardTiles open_reward_tiles = 6;
      // contains catalog offers card widget
      CatalogOffersCardWidget catalog_offers_card_widget = 7;
      // contains open account zero state card widget
      // Ref -> https://docs.google.com/document/d/10suGJqc4WGBm7zeblcbsMvrOr3PNJB_PVEqqJ00LqUk/edit#heading=h.c7ch7ebjupsg
      ZeroStateWidget zero_state_widget = 8;
      // contains promotion banner widget
      PromotionBannerWidget promotion_widget = 9;
    }
  }

  message PromotionBannerWidget {
    // no information needs to be sent from BE for this section as of now,
    // client makes a separate RPC call to fetch this information and shows
    // list of dynamic elements to be render for the user on this screen.
    // https://www.figma.com/design/Bc8Y41Lgv4YMmX00n8V9ZU/%F0%9F%8E%81-Rewards-%E2%80%A2-Workfile-2?node-id=21875-16662&node-type=frame&t=YQgiR7Jppgc7bM8K-0
  }

  message RewardsSummary {
  }

  message Widgets {
    repeated CtaV1 ctas = 1;
  }

  message UnopenedRewardTiles {
    repeated frontend.rewards.RewardWrapperV1 rewards = 2;
    // Cta for Common actions across unopened rewards, like claiming all rewards at one go.
    // Ref. https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=19125-49215&mode=design&t=Xc6fEIYrx5zYrr5I-4
    IconTextCta bulk_claim_rewards_cta_v2 = 3;
  }

  message OpenRewardTiles {
    // no information needs to be sent from BE for this section as of now,
    // client makes a separate RPC call to fetch this information
    // (optional) opened rewards to be sent from BE
    repeated frontend.rewards.RewardWrapperV1 rewards = 1;
  }

  message CatalogOffersCardWidget {
    // no information needs to be sent from BE for this section as of now,
    // client makes a separate RPC call to fetch this information
    // https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=6478-18282&t=3KQ1F13a1uGPqXKW-0
  }

  message ZeroStateWidget {
    // Ref -> https://docs.google.com/document/d/10suGJqc4WGBm7zeblcbsMvrOr3PNJB_PVEqqJ00LqUk/edit#heading=h.c7ch7ebjupsg
    // image on the widget to show empty state
    api.typesv2.common.VisualElement image = 1;
    // description on the widget card
    // example : No money Plants yet, open a savings account to start earning them
    api.typesv2.common.Text desc = 2;
    // cta at bottom of the widget to connect the accounts Eg: Open account
    api.typesv2.ui.IconTextComponent cta = 3;
  }
}


message GetExchangerOrdersForActorRequest {
  // page context for pagination
  rpc.PageContextRequest page_context = 1;

  frontend.header.RequestHeader req = 15;
}

message GetExchangerOrdersForActorResponse {
  // rpc status
  rpc.Status status = 1;
  // page context for pagination
  rpc.PageContextResponse page_context = 2;
  // list of exchanger orders, i.e. rewards earned sorted in descending order.
  // this list will include the "in-progress" and "fulfilled" orders only.
  repeated ExchangerOrder orders = 3;

  frontend.header.ResponseHeader resp_header = 15;
}

message ClaimRewardInputScreenRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // id of the reward
  string reward_id = 2;
  // id of the reward option that is to be claimed
  string reward_option_id = 3;
}

message ClaimRewardInputScreenResponse {
  // rpc status
  rpc.Status status = 1;
  // deeplink to the screen where user input should be taken
  // if deeplink is nil, then no input is required
  deeplink.Deeplink input_screen = 3;
  // Error view to display custom messages for error scenarios.
  // It will be nil for success scenarios.
  ErrorView error_view = 10;

  frontend.header.ResponseHeader resp_header = 15;
}


message ClaimRewardRequest {
  // contains data required on claiming reward
  message ClaimMetadata {
    // useful for physical rewards like gift hampers.
    api.typesv2.PostalAddress shipping_address = 1;
    // encapsulates all data required for claiming an SD reward
    message SDMetadata {
      // nominee info for creating a new SD
      message DepositNomineeInfo {
        string nominee_id = 1;
        string percentage_share = 2;
      }
      repeated DepositNomineeInfo nominee_info_list = 1;

      string sd_name = 2;
    }
    // intentional gap in field number.
    SDMetadata sd_metadata = 6;
  }
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // id of the reward
  string reward_id = 2;
  // id of the reward option that is to be claimed
  string reward_option_id = 3;
  // data required on claiming reward
  ClaimMetadata claim_metadata = 4;
}

// Note : ClaimRewardResponseV2 inside ClaimRewardResponse is used as a v2 response for ClaimReward rpc.
// This will be passed inside ClaimRewardResponse and if not nil, client will only use any details present inside this
// If nil, we will use the existing response and flows.
message ClaimRewardResponse {
  // rpc status
  rpc.Status status = 1;
  // title to be shown on screen shown after reward is claimed like "Congratulations on your winnings"
  string title = 2 [deprecated = true];
  // redirection related details, this can be `nil` if next_screen_cta is provided
  RedirectionDetails redirection_details = 3;
  // CTA to be shown on screen in case we want user to click on button to go to next screen,
  // this can be `nil` if redirection_details is provided
  CTA next_screen_cta = 4;

  // title to render on the Claim success state, e.g. 'Congratulations on your winnings'. Use this field in place of
  // title plain string, for new client versions
  api.typesv2.common.Text title_v2 = 5;
  // The claimed reward icon/visual to be rendered on the screen
  api.typesv2.common.VisualElement reward_icon = 6;
  // Title of the claimed reward, e.g. 'Apple':
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=26221-46483&mode=design&t=gM25gy5eyFFyiQNh-4
  api.typesv2.common.Text claimed_reward_title = 7;
  // The sub-text/description for the claimed reward, e.g. 'Stock worth 100'
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=26221-46488&mode=design&t=gM25gy5eyFFyiQNh-4
  api.typesv2.common.Text claimed_reward_description = 8;
  // (Optional) Banner to display at the bottom of the screen for claimed details:
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=26221-46483&mode=design&t=gM25gy5eyFFyiQNh-4
  Banner bottom_banner = 9;
  // Flag to indicate whether to trigger the catalog offers RPC call on this Reward claim success screen to show offers widget
  // if true, this will take precedence over bottom banner
  bool should_fetch_catalog_offers = 10;

  // [optional] Addition information shown in a carousel, e.g. Boosters information
  // Figma: https://www.figma.com/design/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=12242-76632&t=XN9I5l5yuGWuOiO2-1
  AdditionalInfoCarousel additional_info_carousel = 11;

  // [optional] section to show additional info, ex- egv code, PIN
  // https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=2269-7387&t=oMdTEKdsqXrClvmU-1
  AdditionalInfoSection additional_info_section = 12;

  frontend.header.ResponseHeader resp_header = 15;
  // ClaimRewardResponseV2 is used as a v2 response for ClaimReward rpc.
  // This will be passed inside ClaimRewardResponse and if not nil, client will only use any details present inside this
  // If nil, we will use the existing response and flows.
  ClaimRewardResponseV2 response_v2 = 16;

  // ClaimRewardResponseV2 is used as a v2 response for ClaimReward rpc.
  // This will be passed inside ClaimRewardResponse and if not nil, client will only use any details present inside this
  // If nil, we will use the existing response and flows.
  message ClaimRewardResponseV2 {
    // Deeplink to navigate after ClaimReward RPC call happens
    // If nil, we will use the existing response and flows.
    // Client will pop current screen and navigate to given deeplink.
    deeplink.Deeplink redirection_deeplink = 1;
  }

  // this contains all details related to redirection of user to some screen
  // in future, we can also add a timer to this if such a use-case comes up
  message RedirectionDetails {
    deeplink.Deeplink redirection_screen = 1;
  }
  // An optional banner shown on the claimed screen:
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=26221-46483&mode=design&t=gM25gy5eyFFyiQNh-4
  message Banner {
    // (optional) The left icon/visual shown on the claimed screen, e.g. the green tick icon
    api.typesv2.common.VisualElement banner_icon = 1;
    // The Text/paragraph of the Banner
    api.typesv2.common.Text banner_text = 2;
    // The background of the banner (optional, if not specified, clients will use the Figma default)
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 3;
    // (Optional) a Deeplink to navigate from the banner. If provided, clients will handle the click
    deeplink.Deeplink banner_deeplink = 4;
    // (optional) The right icon/visual shown on the claimed screen, e.g. chevron >
    api.typesv2.common.VisualElement right_icon = 5;
  }
}

//  section to show additional info, ex- egv code, PIN
// https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=2269-7387&t=oMdTEKdsqXrClvmU-1
message AdditionalInfoSection {
  // KeyValueInfo will contain key-value pairs related to the card Header, isCopyable defines if the value can be copied or not
  repeated KeyValueInfo key_value_infos = 1;
  // section bg color
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 2;
}

// Addition information shown in a carousel, e.g. Boosters information
// Figma: https://www.figma.com/design/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=12242-76632&t=XN9I5l5yuGWuOiO2-1
message AdditionalInfoCarousel {
  // Title of the section, e.g. 'Reward boosters added'
  api.typesv2.common.Text title = 1;
  // List of info cards to show
  repeated Banner banners = 2;
  // Describes a single info card in the carousel
  message Banner {
    // Value shown in the card, e.g. '+100', with the icon
    api.typesv2.ui.IconTextComponent value = 1;
    // The text description, e.g. '2x boost applied for Fi Plus accounts'
    api.typesv2.common.Text info_text = 2;
    // [optional] background color of the card
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 3;
    // [optional] the color of the divider line between the value and info text
    api.typesv2.common.ui.widget.BackgroundColour divider_color = 4;
  }
}

message BulkClaimRewardsRequest {
  frontend.header.RequestHeader req = 1;
}

message BulkClaimRewardsResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message GetRedeemedOffersV1Request {
  // page context to help server fetch the page
  rpc.PageContextRequest page_context = 1;
  frontend.header.RequestHeader req = 15;
  // filters to be applied for fetching active offers. can be nil if no filters are to be applied.
  Filters filters = 2;
  message Filters {
    // offer tags filter,
    // Redeemed offers will be returned when the corresponding offer contains ANY of these tags
    repeated string or_tags = 1;
    // redeemed offer status, for getting offers of any particular status, ex- Successful, Cancelled or Expired
    string status = 2;
  }
}

message GetRedeemedOffersV1Response {
  // rpc status
  rpc.Status status = 1;
  // a list containing active redeemed offers and physical merchandise and EGV type exchanger offer orders
  repeated frontend.rewards.OrderV1 orders = 2;
  // page context to help server fetch the page
  rpc.PageContextResponse page_context = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

// request to get the collected offers page layout
message GetRedeemedOffersCatalogLayoutRequest {
  frontend.header.RequestHeader req = 1;
}

// response for the collected offers page layout
// figma -> https://www.figma.com/design/Bc8Y41Lgv4YMmX00n8V9ZU/%F0%9F%8E%81-Rewards-%E2%80%A2-Workfile-2?node-id=20606-18938&t=kK4kZztOBk6ehR82-4
message GetRedeemedOffersCatalogLayoutResponse {
  // title shown on the page eg : "Collected Offers"
  api.typesv2.common.Text title = 2;
  // contains all the information needed to render the top bar including filters, sort options, and clear-all filters CTA
  FiltersWidget filters_widget = 3;
  // [Optional] zero state widget, to be shown when no collected offers returned from BE.
  // will not visible if passed NIL
  // figma -> https://www.figma.com/design/Bc8Y41Lgv4YMmX00n8V9ZU/%F0%9F%8E%81-Rewards-%E2%80%A2-Workfile-2?node-id=21338-21684&t=iKpC89j2sLLIvzeQ-4
  ZeroStateWidget zero_state_widget = 4;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetActiveOffersRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // page context to help server fetch the page
  PageContextRequest page_context = 2;
  // filter on mode though which offer is redeemable FI_COINS/FI_CARD etc
  // if UNSPECIFIED then by default FI_COINS redemption_mode filter is applied implicitly.
  OfferRedemptionMode offer_redemption_mode = 3;
}

message GetActiveOffersResponse {
  // rpc status
  rpc.Status status = 1;
  // page context to help client fetch next page
  PageContextResponse page_context = 2;
  // list of offer available to redeem
  repeated frontend.rewards.Offer offers = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetOfferDetailsByIdRequest {
  // offer id to fetch details
  string offer_id = 1;
  // standard request header
  frontend.header.RequestHeader req = 15;
}

message GetOfferDetailsByIdResponse {
  // rpc status
  rpc.Status status = 1;
  // offer details
  frontend.rewards.Offer offer = 3;
  // standard response header
  frontend.header.ResponseHeader resp_header = 15;
}

message GetOfferCatalogV1Request {
  // optional param to denote which offers are to be shown first in the catalog.
  // for all the offers in display first list ordering would be same as ordering in this list.
  // it will be ignored if sort_type is not UNSPECIFIED.
  repeated string display_first_offer_ids = 1;

  // filters to be applied for fetching active offers. can be nil if no filters are to be applied.
  CatalogFilters filters = 2;

  // option to sort the list of offers on. can be set to UNSPECIFIED if we want default ordering
  string sort_by = 3;

  frontend.header.RequestHeader req = 15;
}

message GetOfferCatalogV1Response {
  rpc.Status status = 1;
  // list of "generic" offers with varied offer_types in it
  repeated CatalogOfferV1 offers = 2;

  frontend.header.ResponseHeader resp_header = 15;

}

message GetRewardsAndOffersForHomeRequest {
  // standard request header
  frontend.header.RequestHeader req = 15;
}

message GetRewardsAndOffersForHomeResponse {
  api.typesv2.common.Text title = 1;

  // details about fi coins balance and cta to my rewards page
  YourRewardsTile your_rewards_tile = 2;

  message YourRewardsTile {
    api.typesv2.common.Text title = 1;
    api.typesv2.common.Image image = 2;
    api.typesv2.ui.BackgroundColour bg_color = 3;
    api.typesv2.ui.Shadow shadow = 4;
    api.typesv2.ui.IconTextComponent cta = 5;
    api.typesv2.ui.IconTextComponent fi_coins_balance = 6;
  }

  // tile to redirect to ways to earn page if fi coins balance less than certain threshold
  WaysToEarnTile ways_to_earn_tile = 3;

  message WaysToEarnTile {
    api.typesv2.common.Text title = 1;
    api.typesv2.common.Image image = 2;
    api.typesv2.ui.BackgroundColour bg_color = 3;
    api.typesv2.ui.Shadow shadow = 4;
    api.typesv2.ui.IconTextComponent cta = 5;
  }

  // list of offers to be displayed on home
  repeated CatalogOfferHome offers = 4;

  // standard response header
  frontend.header.ResponseHeader resp_header = 15;
}

message GetRewardsAndOffersWidgetRequest {
  // screen name for which offers are fetched
  // if this is UNSPECIFIED, we default to HOME
  frontend.deeplink.Screen screen = 1;
  // time of last visit of MyRewards. used to show count of new rewards generated since last visited
  google.protobuf.Timestamp last_my_rewards_visit_timestamp = 2;
  // Specify which type of widget data to serve. This can be null from legacy clients
  WidgetType widget_type = 3;
  // standard request header
  frontend.header.RequestHeader req = 15;
  // Enum to specify which type of widget response to serve. We can have the existing/legacy Rewards and Offers
  // response together. Or, we can have only Rewards or only Card Offers widget data
  enum WidgetType {
    // When unspecified, the Request will serve the existing Rewards and Offers response
    WIDGET_TYPE_UNSPECIFIED = 0;
    // Type to signify this request will serve only the User's Reward/Catalog offer response for Rewards only widget in Ui
    WIDGET_TYPE_CATALOG_OFFERS = 1;
    // Type to signify this request will serve only Card offers widget
    WIDGET_TYPE_CARD_OFFERS = 2;
  }
}

message GetRewardsAndOffersWidgetResponse {
  api.typesv2.common.Text title = 1;

  // ui component for displaying list of tabs at top of the widget
  // tabs will be used to filter between data displayed on the widget
  api.typesv2.ui.Filter filter = 10;

  // map of tab id vs data to be displayed on clicking the tab
  // key of the map will correspond to the tab id inside the filter
  map<string, TabData> tab_data_map = 3;

  // cta at bottom of the widget, can be used for redirecting to ways-to-earn, collected-offers etc.
  api.typesv2.ui.IconTextComponent bottom_cta = 6;

  // boolean to decide whether to show the tabs or not
  // if tabs are hidden, data corresponding to the first tab will be visible
  bool are_tabs_visible = 5;

  // TabData consists of a list of cards to be displayed when corresponding tab is selected
  message TabData {
    // list of cards in the section
    repeated Card cards = 1;

    // cta to be displayed on the top right of cards section eg - see all
    api.typesv2.ui.IconTextComponent top_cta = 5;

    // one of the various card designs to be displayed in the carousel
    message Card {
      oneof card {
        YourRewardsCard your_rewards_card = 1;
        WaysToEarnCard ways_to_earn_card = 2;
        CatalogOfferHome catalog_offer_home = 3;
        ViewAllOffersCard view_all_offers_card = 4;
        YourRewardsCardV2 your_rewards_card_v2 = 5;
        GenericOfferCard generic_offer_card = 6;
        YourRewardsCardV3 your_rewards_card_v3 = 7;
      }

      // generic card design to display card offers
      message GenericOfferCard {
        api.typesv2.ui.sdui.sections.Section section = 1;
      }

      // details about fi coins balance and cta to my rewards page
      message YourRewardsCard {
        api.typesv2.common.Text title = 1;
        api.typesv2.common.Image image = 2;
        api.typesv2.ui.BackgroundColour bg_color = 3;
        api.typesv2.ui.Shadow shadow = 4;
        api.typesv2.ui.IconTextComponent cta = 5;
        api.typesv2.ui.IconTextComponent fi_coins_balance = 6;
      }

      // v2 version of your rewards card with two sections
      // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%9B%A0%EF%B8%8F-Home-Workfile-2?type=design&node-id=841%3A20949&t=lBmEm5y1gou3UXep-1
      message YourRewardsCardV2 {
        TopSection top_section = 1;
        BottomSection bottom_section = 2;

        message TopSection {
          // for eg - Your coins
          api.typesv2.common.Text header = 1;
          // for eg - f 30,484 > in case of coins tab
          // shifts to the center of section if header is nil
          api.typesv2.ui.IconTextComponent body = 2;
          // badge to display count of unclaimed rewards, etc
          api.typesv2.ui.IconTextComponent badge = 3;
          // screen to redirected to on clicking the card
          frontend.deeplink.Deeplink deeplink = 4;
          // bg color kept backend configurable to drive A/B exp
          api.typesv2.ui.BackgroundColour bg_color = 5;
          // shadow kept backend configurable to drive A/B exp
          api.typesv2.ui.Shadow shadow = 6;
        }
        message BottomSection {
          // image to be displayed on the top of the section, can be a lottie
          api.typesv2.common.Image image = 1;
          // for eg - Get more
          api.typesv2.common.Text header = 2;
          // for eg - rewards >, fi coins >
          // components in this list will be displayed one by one in a circular fashion
          repeated api.typesv2.ui.IconTextComponent body = 3;
          // screen to redirected to on clicking the card
          frontend.deeplink.Deeplink deeplink = 4;
          // bg color kept backend configurable to drive A/B exp
          api.typesv2.ui.BackgroundColour bg_color = 5;
          // shadow kept backend configurable to drive A/B exp
          api.typesv2.ui.Shadow shadow = 6;
        }
      }

      // V3 version of your rewards card with two sections:
      // https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=4264-13332&mode=design&t=nTQvmcw6cSRbF4Bc-4
      message YourRewardsCardV3 {
        TopSection top_section = 1;
        BottomSection bottom_section = 2;

        message TopSection {
          // for eg - Fi coins
          api.typesv2.common.Text title = 1;
          // for eg - 3.5k
          api.typesv2.common.Text subtitle = 2;
          // The Icon/Visual displayed on the left of the header/value content
          api.typesv2.common.VisualElement icon = 3;
          // badge to display count of unclaimed rewards, etc
          api.typesv2.ui.IconTextComponent badge = 4;
          // screen to redirected to on clicking the card
          frontend.deeplink.Deeplink deeplink = 5;
          // bg color kept backend configurable to drive A/B exp
          api.typesv2.common.ui.widget.BackgroundColour bg_color = 6;
          // shadow kept backend configurable to drive A/B exp
          api.typesv2.common.ui.widget.Shadow shadow = 7;
        }
        message BottomSection {
          // for eg - rewards >, fi coins >
          // components in this list will be displayed one by one in a circular fashion
          repeated api.typesv2.ui.IconTextComponent ticker_texts = 1;
          // The chevron icon displayed on the right side of the card
          api.typesv2.common.VisualElement icon = 2;
          // screen to redirected to on clicking the card
          frontend.deeplink.Deeplink deeplink = 3;
          // bg color kept backend configurable to drive A/B exp
          api.typesv2.common.ui.widget.BackgroundColour bg_color = 4;
          // shadow kept backend configurable to drive A/B exp
          api.typesv2.common.ui.widget.Shadow shadow = 5;
        }
      }

      // card to redirect to ways to earn page
      message WaysToEarnCard {
        api.typesv2.common.Text title = 1;
        api.typesv2.common.Image image = 2;
        api.typesv2.ui.BackgroundColour bg_color = 3;
        api.typesv2.ui.Shadow shadow = 4;
        api.typesv2.ui.IconTextComponent cta = 5;
      }

      // will be used to redirect user to all offers screen
      message ViewAllOffersCard {
        api.typesv2.common.Text title = 1;
        api.typesv2.common.Image image = 2;
        api.typesv2.ui.BackgroundColour bg_color = 3;
        api.typesv2.ui.Shadow shadow = 4;
        api.typesv2.ui.IconTextComponent cta = 5;
      }
    }
  }

  // list of tabs at top of the widget
  // for eg- spend your fi coins, card offers
  // first tab is in active state by default
  repeated Tab tabs = 2 [deprecated = true];

  // cta to ways to earn page at bottom of the widget
  api.typesv2.ui.IconTextComponent ways_to_earn = 4 [deprecated = true];

  // tappable tab to allow user to choose a section for display
  message Tab {
    string id = 1;

    // tab display in inactive state
    api.typesv2.ui.IconTextComponent inactive_cta = 2;

    // tab display in active state
    api.typesv2.ui.IconTextComponent active_cta = 3;
  }

  // standard response header
  frontend.header.ResponseHeader resp_header = 15;
}

message RedeemOfferInputScreenRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // identifier of the offer to be redeemed
  string offer_id = 2;
}

message RedeemOfferInputScreenResponse {
  // rpc status
  rpc.Status status = 1;
  // deeplink to the screen where user input should be taken
  // if deeplink is nil, then no input is required
  deeplink.Deeplink input_screen = 3;
  frontend.header.ResponseHeader resp_header = 15;
}


message InitiateRedemptionRequest {
  message RedemptionRequestMetadata {
    // shipping address useful for physical merchandise offers etc.
    api.typesv2.PostalAddress shipping_address = 1;

    message VistaraAirMilesRedemptionMetadata {
      // club vistara account email id inputted by user
      string email_id = 1;
      // club vistara account id inputted by user
      string cv_id = 2;
      // number of fi coins used to initiate the offer redemption request
      int32 fi_coins = 3;
    }

    // oneof field used to collect redemption data for various offer types
    oneof offer_type_specific_metadata {
      VistaraAirMilesRedemptionMetadata vistara_air_miles_redemption_metadata = 2 [deprecated = true];
    }

    // optional: number of fi coins used to initiate the offer redemption request
    // this will only be used for offers where user can select no of fi coins to redeem
    // for eg - ITC Club points, Club Vistara Points, etc
    int32 fi_coins = 3;

    // optional: additional inputs provided by user to initiate the offer redemption request
    // this will only be used for offers where additional user input is required to redeem
    // for eg - ITC Club points, Club Vistara Points, etc
    repeated AdditionalInput additional_inputs = 4;
    message AdditionalInput {
      // input identifier, eg- EMAIL_ID
      string id = 1;
      // input value, eg- <EMAIL>
      string value = 2;
    }
  }
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // identifier of the offer to be redeemed
  string offer_id = 2 [(validate.rules).string.min_len = 1];

  // contains metadata like shipping address of user.
  RedemptionRequestMetadata request_metadata = 5;
}

message InitiateRedemptionResponse {
  // rpc status
  rpc.Status status = 1;
  // id used to confirm redemption
  string redemption_request_id = 2;
  // any error that may arise while initiating redemption. RPC's status will be sent as FAILED_PRECONDITION or INVALID_ARGUMENT in this case. will be nil if no error arises
  api.typesv2.common.Text error_message = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message ConfirmRedemptionRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // id used to confirm redemption
  string redemption_request_id = 2 [(validate.rules).string.min_len = 1];
}

message ConfirmRedemptionResponse {
  // rpc status
  rpc.Status status = 1;
  // redeemed offer
  frontend.rewards.RedeemedOffer redeemedOffer = 2;
  // next screen cta
  api.typesv2.ui.IconTextComponent next_screen_cta = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetActiveRedeemedOffersRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // page context to help server fetch the page
  PageContextRequest page_context = 2;
}

message GetActiveRedeemedOffersResponse {
  // rpc status
  rpc.Status status = 1;
  // redeemed offers
  repeated frontend.rewards.RedeemedOffer redeemedOffers = 2;
  // page context to help server fetch the page
  PageContextResponse page_context = 4;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetExpiredRedeemedOffersRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // page context to help server fetch the page
  PageContextRequest page_context = 2;
}

message GetExpiredRedeemedOffersResponse {
  // rpc status
  rpc.Status status = 1;
  // redeemed offers
  repeated frontend.rewards.RedeemedOffer redeemedOffers = 2;
  // page context to help server fetch the page
  PageContextResponse page_context = 4;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetRedeemedOfferDetailsRedirectionInfoRequest {
  // denotes the redeemed offer for which redirection info need to be fetched.
  string redeemed_offer_id = 1;

  frontend.header.RequestHeader req = 15;
}

message GetRedeemedOfferDetailsRedirectionInfoResponse {
  // redirection url
  string redirection_url = 1;

  frontend.header.ResponseHeader resp_header = 15;
}

// Error view to display custom messages for error scenarios.
// It will be nil for success scenarios.
message ErrorView {
  string title = 1;
  string description = 2;
}

message GetExchangerOfferByIdRequest {
  string offer_id = 1;

  frontend.header.RequestHeader req = 15;
}

message GetExchangerOfferByIdResponse {
  rpc.Status status = 1;

  ExchangerOfferWidget exchanger_offer = 2;

  // current fi-coins balance
  uint32 fi_coins_balance = 3;

  frontend.header.ResponseHeader resp_header = 15;
}

message RedeemExchangerOfferRequest {
  // offer_id on which the trade is performed
  string exchanger_offer_id = 1;

  // client-request-id for idempotency at the backend
  string request_id = 2;

  frontend.header.RequestHeader req = 15;
}

message RedeemExchangerOfferResponse {
  rpc.Status status = 1;

  // each trade/redemption results in the creation of an order with options to choose from
  ExchangerOrder exchanger_offer_order = 2;

  // attempts left post the trade/redemption
  uint32 attempts_left = 3;

  // fi-coin balance after redemption
  uint32 fi_coins_balance = 4;

  // text to be shown under the title, like - "You can find it under 'My Orders'".
  string subtitle = 5;

  frontend.header.ResponseHeader resp_header = 15;
}

message ChooseExchangerOrderOptionRequest {
  // order_id of the order which was generated for the offer
  string exchanger_order_id = 1;

  // option_id of the option to choose from the exchanger_order of the offer
  string option_id = 2;

  frontend.header.RequestHeader req = 15;
}

message ChooseExchangerOrderOptionResponse {
  rpc.Status status = 1;

  // updated exchanger_order with the chosen option
  ExchangerOrder exchanger_order = 2;

  // deeplink to the next screen (could take to the same offer or the catalog offers screen)
  deeplink.Deeplink next_screen = 3;

  message SuccessScreenDisplayDetails {
    // main heading, e.g. - "Drum Roll! This has been added to your orders"
    string title = 1;

    // information related to the claimed reward or next screen to follow like "Taking you to address confirmation"
    string info = 2;
  }

  SuccessScreenDisplayDetails success_screen_display_details = 4;

  frontend.header.ResponseHeader resp_header = 15;
}

message GetExchangerOrderInputScreenRequest {
  // exchanger order id for which input is required
  string exchanger_order_id = 1;

  frontend.header.RequestHeader req = 15;
}

message GetExchangerOrderInputScreenResponse {
  // deeplink to the screen where user input should be taken
  // if deeplink is nil, then no input is required
  deeplink.Deeplink input_screen = 1;

  // contains the rpc response status along with error_view
  frontend.header.ResponseHeader resp_header = 15;
}

message SubmitExchangerOrderUserInputRequest {
  // contains the input obtained from user
  message UserInput {
    // for physical merchandise
    api.typesv2.PostalAddress shipping_address = 1;
  }
  // id of the reward
  string exchanger_order_id = 1;
  // user input
  UserInput user_input = 2;

  frontend.header.RequestHeader req = 15;
}

message SubmitExchangerOrderUserInputResponse {
  // exchanger order
  frontend.rewards.ExchangerOrder exchanger_order = 1;

  message CTA {
    // text holds the text to display inside the button, e.g. "View in 'My orders'"
    string text = 1;
    // deeplink to the next screen
    deeplink.Deeplink next_screen = 2;
  }

  CTA cta = 2;

  // contains rpc status and error_view
  frontend.header.ResponseHeader resp_header = 15;
}

message GetCatalogOffersAndFiltersRequest {
  // optional param to denote which offers are to be shown first in the catalog.
  // for all the offers in display first list ordering would be same as ordering in this list.
  repeated string display_first_offer_ids = 1;

  // Optional: filters to add to the request. Usually this is done when the screen is loaded from some deeplink which
  // specifies some filters
  CatalogFilters filters = 2;

  // Optional: option to sort the list of offers on. can be set to UNSPECIFIED if we want default ordering
  string sort_by = 3;

  frontend.header.RequestHeader req = 15;
}

message GetCatalogOffersAndFiltersResponse {
  // contains all the information needed to render the top bar including filters, sort options, and clear-all filters CTA
  TopBar top_bar = 1;

  // list of "generic" offers with varied offer_types in it
  repeated CatalogOfferV1 offers = 2;

  // max number of active/promoted filters to show inside top filters bar.
  // a promoted filter is one that is to shown on the top bar as well as in the "all-filters" section.
  uint32 max_promoted_filters_for_top_bar = 3;

  // contains rpc status and error_view
  frontend.header.ResponseHeader resp_header = 15;

  message TopBar {
    // this CTA will be used to remove all applied filters. its working is client controlled, we will just send the display details.
    // it will not clear selected sort-options.
    api.typesv2.ui.IconTextComponent clear_filters_cta = 1;

    // top bar to be shown on catalog offers page that will include ordered list of filters and sort options
    repeated TopBarWidget widgets = 2;
  }

  // single widget to be displayed on the top bar
  message TopBarWidget {
    oneof widget {
      CatalogTagFilter tag_filter_widget = 1;
      SortByWidget sort_by_widget = 2;
      AllFiltersWidget all_filters_widget = 3;
    }
  }

  // widget for viewing all supported filters.
  message AllFiltersWidget {
    api.typesv2.ui.IconTextComponent cta = 1;
    repeated CatalogTagFilter tag_filters = 2;
  }

  // widget for showing the different available sort options
  message SortByWidget {
    api.typesv2.ui.IconTextComponent cta = 1;
    repeated CatalogSortOption sort_options = 2;
  }
}

message GetOffersCatalogPageRequest {
  frontend.header.RequestHeader req = 1;
  Filters filters = 2;

  message Filters {
    repeated SectionType section_types = 1;
    // OffersSectionSpecificFilters : filters to be applied while fetching offers section. can be nil if no filters are to be applied.
    // only valid with unspecific or offers section type
    OffersSectionSpecificFilters offers_section_specific_filters = 2;
  }

  enum SectionType {
    SECTION_TYPE_UNSPECIFIED = 0;
    SECTION_TYPE_MY_EARNINGS = 1;
    SECTION_TYPE_BANNERS = 2;
    SECTION_TYPE_OFFER_FILTERS = 3;
    SECTION_TYPE_OFFERS = 4;
    SECTION_TYPE_UNOPENED_REWARDS = 5;
    SECTION_TYPE_GENERIC_SERVER_DRIVEN = 6;
  }

  message OffersSectionSpecificFilters {
    repeated string display_first_offer_ids = 1;
    repeated string tag_names = 2;
  }
}

message GetOffersCatalogPageResponse {
  // contains rpc status and error_view
  frontend.header.ResponseHeader resp_header = 1;
  // PageDetails : contains all the information needed to render the catalog offers page
  PageDetails page_details = 2;
  // PageDetails contains :
  // 1. TopBarDetails - details related to top bar of the page
  // 2. Sections - list of sections to be displayed on the page
  // 3. PageUiDetails - details related to the whole page ui
  // 4. AdditionalDetails - additional details like display first offer view offer details deeplink
  // figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11166-8184&node-type=frame&t=9r3xrmHUKNR6nOz8-0
  message PageDetails {
    TopBarDetails top_bar_details = 1;
    repeated Section page_sections = 2;
    PageUiDetails page_ui_details = 3;
    AdditionalDetails additional_details = 4;
  }
  // TopBarDetails contains :
  // 1. title - title of the page Eg : Spend your Fi Coins
  // 2. cta - list of custom ctas to be shown on the left top bar Eg : Help, Contact Us etc
  message TopBarDetails {
    // title of the page
    api.typesv2.common.Text title = 1;
    // custom cta to be shown on the right of top bar
    repeated CtaV1 ctas = 2;
    // background color of the top bar
    api.typesv2.common.ui.widget.BackgroundColour background_color = 3;
  }
  // Section is a one-of message containing different sections to be displayed on the catalog offers page :
  // 1. MyEarningsSection - section to display user's reward related details
  // 2. BannerSection - section to display promo banner
  // 3. OfferFiltersSection - section to display filters
  // 4. OffersSection - section to display catalog offers
  // 5. UnopenedRewardsSection - section to display unopened rewards
  // 6. GenericServerDrivenSection - section to display generic server driven section
  //    This section would be completely backend dependent and would be loaded by client as and when sent(non nil) by backend.
  // NOTE: Each section should only be used once in a page.
  // figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11166-8184&node-type=frame&t=9r3xrmHUKNR6nOz8-0

  message Section {
    // background color of the section
    // deprecated in favor of section_ui_details.background_color
    api.typesv2.common.ui.widget.BackgroundColour background_color = 1[deprecated = true];
    oneof section {
      MyEarningsSection my_earnings_section = 2;
      BannersSection banners_section = 3;
      OfferFiltersSection offer_filters_section = 4;
      OffersSection offers_section = 5;
      UnopenedRewardsSection unopened_rewards_section = 6;
      pkg.GenericServerDrivenSection generic_server_driven_section = 8;
    }
    SectionUiDetails section_ui_details = 7;
  }

  message PageUiDetails {
    // background colour of the page
    api.typesv2.common.ui.widget.BackgroundColour background_color = 2;
  }

  message AdditionalDetails {
    deeplink.Deeplink display_first_offer_view_offer_details_deeplink = 1;
  }

  // SectionUiDetails contains ui details for Section
  message SectionUiDetails {
    // [Optional] background color of the section
    // This is used to set the background color of individual section which takes precedence over the page background color
    api.typesv2.common.ui.widget.BackgroundColour background_color = 1;
    // [Optional] spacing to be rendered on top of the section
    api.typesv2.ui.sdui.components.Spacer top_spacer = 7;
    // [Optional] spacing to be rendered on bottom of the section
    api.typesv2.ui.sdui.components.Spacer bottom_spacer = 8;
  }


  // MyEarningsSection - section to display user's reward related details
  // Figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11166-8184&node-type=frame&t=9r3xrmHUKNR6nOz8-0
  message MyEarningsSection {
    // details related to my rewards section
    api.typesv2.ui.sdui.sections.Section section = 1;
  }

  // BannerSection - section to display promo banner
  // Figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11136-6920&node-type=frame&t=X1aWzkKvYkqnDdsX-0
  message BannersSection {
    // list of banners to be displayed
    repeated pkg.Banner banners = 1;
    // scroll behaviour of the banners
    // NOTE : only SCROLL_ORIENTATION_HORIZONTAL is supported for now
    pkg.ScrollBehaviour scroll_behaviour = 2;
  }


  // OfferFiltersSection - section to display offer filters
  // Figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11166-9078&node-type=frame&t=X1aWzkKvYkqnDdsX-0
  message OfferFiltersSection {
    // list of filters to be displayed
    repeated OfferFilterWidget filters = 1;
  }

  message OfferFilterWidget {
    oneof widget {
      AllOffersCatalogFilter all_offers_filter = 1;
      VerticalCatalogTagFilter tag_filter = 2;
    }
  }

  // AllOffersCatalogFilter - widget specifically to denote the all offers filter, all offers are shown on applying this filter
  message AllOffersCatalogFilter {
    // cta when tag is inactive
    api.typesv2.ui.VerticalKeyValuePair inactive_filter_cta = 1;

    // cta when tag is active
    api.typesv2.ui.VerticalKeyValuePair active_filter_cta = 2;
  }

  // OffersSection - section to display catalog offers
  // Figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11136-6789&node-type=frame&t=X1aWzkKvYkqnDdsX-0
  message OffersSection {
    // section to display catalog offers
    repeated api.typesv2.ui.sdui.sections.Section catalog_offers_sections = 1;
  }

  // Rewards section
  // Figma : https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=30402-16444&t=ecrop9NV4M8hLJbF-4
  message UnopenedRewardsSection {
    api.typesv2.common.Text header = 1;
    api.typesv2.ui.IconTextComponent view_all_cta = 2;
    repeated frontend.rewards.RewardWrapperV1 rewards = 3;
    UnopenedRewardsSectionBanner banner = 4;

    message UnopenedRewardsSectionBanner {
      api.typesv2.common.VisualElement leading_image = 1;
      api.typesv2.common.Text title = 2;
      api.typesv2.common.VisualElement trailing_image = 3;
      api.typesv2.common.ui.widget.BackgroundColour bg_color = 4;
      api.typesv2.common.ui.widget.BackgroundColour border_color = 5;
      frontend.deeplink.Deeplink deeplink = 6;
    }
  }
}

message GetOffersCatalogPageVersionToRenderRequest {
  frontend.header.RequestHeader req = 1;
}

message GetOffersCatalogPageVersionToRenderResponse {
  frontend.header.ResponseHeader resp_header = 1;
  CatalogOffersPageVersion version = 2;
  enum CatalogOffersPageVersion {
    CATALOG_OFFERS_PAGE_VERSION_UNSPECIFIED = 0;
    CATALOG_OFFERS_PAGE_VERSION_1 = 1;
    CATALOG_OFFERS_PAGE_VERSION_2 = 2;
  }
}

message GetCardOffersTabsRequest {
  frontend.header.RequestHeader req = 1;
}

// GetCardOffersTabsResponse returns a list of tabs to be displayed on top of card offers page
// https://www.figma.com/file/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?type=design&node-id=10230%3A12810&mode=design&t=e3mtrgFYWCSAMclM-1
message GetCardOffersTabsResponse {
  // list of tabs to be displayed on top of card offers page
  repeated Tab tabs = 1;

  // tappable tab to allow user to choose a section for display
  message Tab {
    // id will be used to uniquely identify a tab
    // this will subsequently be passed to GetCardOffersCatalogLayout and GetCardOffers
    string id = 1;

    // tab display in inactive state
    api.typesv2.ui.IconTextComponent inactive_cta = 2;

    // tab display in active state
    api.typesv2.ui.IconTextComponent active_cta = 3;
  }

  // id of the card type to be selected by default
  string default_card_type_id = 2;

  frontend.header.ResponseHeader resp_header = 3;
}

message GetCardOffersCatalogLayoutRequest {
  frontend.header.RequestHeader req = 1;
  // card type for which catalog layout to be fetched: DEBIT_CARD/CREDIT_CARD.
  // It is a mandatory param.
  // deprecated in favor of card_type_id
  CardType card_type = 2 [deprecated = true];

  // card type id for which catalog layout should be fetched: For eg: AMPLIFI_CREDIT_CARD_ID for amplifi CC
  // it will resolve to a combination of CardType and Tag: For eg: AMPLIFI_CREDIT_CARD_ID will resolve to CREDIT_CARD_TYPE and AMPLIFI_CREDIT_CARD_EXCLUSIVE tag
  // using string type instead of enum for ease of adding new card types with client backward compatibility
  // It is a mandatory param.
  string card_type_id = 3;
}

message GetCardOffersCatalogLayoutResponse {
  // promoted offers section
  PromotedOffersSection promoted_offers_section = 1;
  // card offers catalog section
  OffersCatalogSection offer_catalog_section = 2;
  // (optional) cta shown on card catalog, can be used for different use cases, ex- GET FI CARD cta if user doesn't have a fi-card.
  CTA cta = 3;

  frontend.header.ResponseHeader resp_header = 15;

  // section for showing promoted offers
  message PromotedOffersSection {
    // section title
    api.typesv2.common.Text title = 1;
    // promoted card offers
    repeated CardOffer offers = 2;
  }

  // offers catalog section
  message OffersCatalogSection {
    api.typesv2.common.Text title = 1;
    // catalog top bar, contains filter tags, all filters widget, clear filters cta etc.
    CatalogTopBar catalog_top_bar = 2;
    // card offers
    repeated CardOffer offers = 3;
  }

  message CatalogTopBar {
    // this CTA will be used to remove all applied filters. its working is client controlled, we will just send the display details.
    api.typesv2.ui.IconTextComponent clear_filters_cta = 1;
    // these are the widgets shown on catalog top bar, ex- offer filter tags, all-filters widget.
    repeated TopBarWidget widgets = 2;
  }

  message TopBarWidget {
    oneof widget {
      // filter tags widget
      CatalogTagFilter filter_tag_widget = 1;
      // all filters widget
      AllFiltersWidget all_filters_widget = 2;
    }
  }

  // widget for viewing all supported filters.
  message AllFiltersWidget {
    // all filters cta
    api.typesv2.ui.IconTextComponent cta = 1;
    // all supported filter tags
    repeated CatalogTagFilter filter_tags = 2;
  }
}

message GetCardOffersRequest {
  frontend.header.RequestHeader req = 1;
  // card type for which offers to be fetched: DEBIT_CARD/CREDIT_CARD.
  // deprecated in favor of card_type_id
  CardType card_type = 2 [deprecated = true];
  // card offer filters
  Filters filters = 3;

  message Filters {
    // offer filter tags
    repeated string tags = 1;
    // offer category, for getting offers of any particular category, ex- ALL offers, PROMOTED offers etc.
    OfferCategory offer_category = 2;
  }

  // card type id for which card offers should be fetched: For eg: AMPLIFI_CREDIT_CARD_ID for amplifi CC
  // it will resolve to a combination of CardType and Tag: For eg: AMPLIFI_CREDIT_CARD_ID will resolve to CREDIT_CARD_TYPE and AMPLIFI_CREDIT_CARD_EXCLUSIVE tag
  // using string type instead of enum for ease of adding new card types with client backward compatibility
  // It is a mandatory param.
  string card_type_id = 4;
}

message GetCardOffersResponse {
  // card offers
  repeated CardOffer offers = 1;

  frontend.header.ResponseHeader resp_header = 15;
}

message GetCardOfferDetailsRequest {
  frontend.header.RequestHeader req = 1;

  // offer id to fetch details
  string offer_id = 2;
}

message GetCardOfferDetailsResponse {
  // card offer details
  CardOffer card_offer = 1;

  frontend.header.ResponseHeader resp_header = 15;
}

message GetConvertFiCoinsOfferRedemptionScreenRequest {
  frontend.header.RequestHeader req = 1;

  // offer id corresponding to which we need to fetch fi coins conversion screen
  string offer_id = 2;
}

message GetConvertFiCoinsOfferRedemptionScreenResponse {
  // rpc status
  frontend.header.ResponseHeader resp_header = 1;

  // title of the bottom sheet screen, eg - How many Fi-Coins do you want to convert?
  api.typesv2.common.Text title = 2;

  // available fi coins balance of the user
  int32 available_fi_coin_balance = 3;
  // multiplier to be used for fi coins to points conversion
  // eg - 0.025 means 1 point for every 40 fi coins spent
  float fi_coins_to_points_conversion_multiplier = 4;
  // multiplier to be used to calculate the step size for fi coins slider
  // step size is calculated with the following formula - (1/fi_coins_to_points_conversion_multiplier)*fi_coins_step_size_multiplier
  // eg - for a multiplier of 5, step size will be = (1/0.025)*5 = 200
  uint32 fi_coins_step_size_multiplier = 5;
  // minimum number of fi coins that the user can convert to points
  // this should be a multiple of (1/fi_coins_to_points_conversion_multiplier)*fi_coins_step_size_multiplier
  // eg - 200, 400, 600, 800.. and so on
  int32 min_convertable_fi_coins = 6;
  // maximum number of fi coins that the user can convert to points
  // this should be a multiple of (1/fi_coins_to_points_conversion_multiplier)*fi_coins_step_size_multiplier
  int32 max_convertable_fi_coins = 7;

  // to show the number of points the user will get post converting fi coins
  Banner points_banner = 8;
  // banner to be displayed on the points redemption bottom sheet
  message Banner {
    string description = 1;
    string left_img_url = 2;
    string right_img_url = 3;
    string bg_color = 4;
  }

  // disclaimer for the redemption, to be displayed in bottom left of sheet
  // eg- 'You’ll need a Club ITC membership to claim this offer. Join here'
  api.typesv2.ui.TextWithHyperlinks disclaimer = 9;

  // cta at the bottom right of sheet
  CtaV1 cta = 10;
}

message GetUserDetailsInputOfferRedemptionScreenRequest {
  frontend.header.RequestHeader req = 1;

  // offer id corresponding to which we need to user details input screen
  string offer_id = 2;
}

message GetUserDetailsInputOfferRedemptionScreenResponse {
  // rpc status
  frontend.header.ResponseHeader resp_header = 1;

  // title of the bottom sheet screen, eg - To claim this offer, enter your Club ITC membership details
  api.typesv2.common.Text title = 2;

  // list of fields required to take user input for offer redemption
  repeated api.typesv2.TextInputField text_input_fields = 3;

  // disclaimer for the redemption, to be displayed in bottom left of sheet
  // eg- 'Not a Club ITC member? Create an account'
  api.typesv2.ui.TextWithHyperlinks disclaimer = 4;

  // cta at the bottom right of sheet
  CtaV1 cta = 5;
}

message GetDynamicUrlForWebPageScreenRequest {
  frontend.header.RequestHeader req = 1;
  // A comma-separated list of information required by the RPC to fetch specific fields.
  // This information is expected to be in the form of key-value pairs (e.g., vendor:POSHVINE,target_url:xyz).
  string request_metadata = 2;
}

message GetDynamicUrlForWebPageScreenResponse {
  // rpc status
  frontend.header.ResponseHeader resp_header = 1;
  // Url for the page content
  string webpage_url = 2;
}

message GetWaysToEarnRewardsRequest {
  frontend.header.RequestHeader req = 1;
}

// This is to display data on the Ways to earn rewards screen
// https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=3899-9767&mode=design&t=YvCDphsrSJqIA6sJ-0
message GetWaysToEarnRewardsResponse {
  // rpc status
  frontend.header.ResponseHeader resp_header = 1;
  // Title to be displayed in the top navbar ex: Earn more rewards
  api.typesv2.common.Text navbar_title = 2;
  // Cards showing way to earn reward
  repeated WayToEarnRewardCard way_to_earn_row = 3;

  // Individual Card showing way to earn reward
  message WayToEarnRewardCard {
    // Icon to show on left of title
    api.typesv2.common.VisualElement icon = 1;
    // Title describing how to earn reward ex: Invite friends to Fi
    api.typesv2.common.Text how_to_earn_title = 2;
    // Value describing how much can a user earn through this, ex: Rs 500/ some Fi-coins, etc
    api.typesv2.ui.IconTextComponent earning_potential_value = 3;
    // Describes if reward is earned per transaction/ per referral, etc
    api.typesv2.common.Text earning_per_unit_desc = 4;
    // Description shown right below the offer row, ex: USE 249 FI-COINS TO AVAIL OFFER
    api.typesv2.ui.IconTextComponent bottom_info = 5;
    // Background color of the above desc
    api.typesv2.ui.BackgroundColour bg_color = 6;
    // Reward tag, ex: TOP REWARD, etc to be displayed at the top left corner of the record
    api.typesv2.ui.IconTextComponent reward_tag = 7;
    // Shadow displayed below the record
    api.typesv2.ui.Shadow shadow = 8;
    // Reward offer id
    string reward_offer_id = 9;
  }
}

message GetWaysToEarnRewardDetailRequest {
  frontend.header.RequestHeader req = 1;
  // id of offer whose details need to be fetched.
  string reward_offer_id = 2;
}

// This is for the screen displayed when user clicks on any card in ways to earn rewards screen
// https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=3510-7906&mode=design&t=YvCDphsrSJqIA6sJ-0
message GetWaysToEarnRewardDetailResponse {
  // rpc status
  frontend.header.ResponseHeader resp_header = 1;
  // title to be displayed at the top of the details ex: Invite your friends to Fi & earn rewards
  api.typesv2.common.Text title = 2;
  // header of the ways to earn detail screen
  Header header = 3;
  // first section showing description of the reward
  DescriptionSection reward_description = 4;
  // used to show sections like how to redeem, tnc, etc
  repeated WaysToEarnInfoSection reward_offer_details = 5;
  // Optional Reward tag, ex: TOP REWARD, POWER UP, etc. to be displayed below the header
  // https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=3510-7939&mode=design&t=K5aCyBQS1WxN1nQO-0
  api.typesv2.ui.IconTextComponent reward_tag = 6;
  // cta at the bottom of the screen
  CtaV1 cta = 7;

  message Header {
    // top banner icon
    api.typesv2.common.VisualElement top_banner = 1;
    // background color of the top banner
    api.typesv2.ui.BackgroundColour bg_color = 2;
  }

  message DescriptionSection {
    // icon to be displayed on the left
    api.typesv2.common.VisualElement icon = 1;
    // description about the reward ex- Earn flat ₹300 per referral
    api.typesv2.common.Text description = 2;
    // Background color for this section
    api.typesv2.ui.BackgroundColour bg_color = 3;
  }
}

message GetWaysToEarnRewardsScreenVersionToRenderRequest {
  frontend.header.RequestHeader req = 1;
}

message GetWaysToEarnRewardsScreenVersionToRenderResponse {
  // response header
  frontend.header.ResponseHeader resp_header = 1;
  WaysToEarnRewardsScreenVersion ways_to_earn_rewards_screen_version = 2;

  enum WaysToEarnRewardsScreenVersion {
    WAYS_TO_EARN_REWARDS_SCREEN_VERSION_UNSPECIFIED = 0;
    WAYS_TO_EARN_REWARDS_SCREEN_VERSION_1 = 1;
    WAYS_TO_EARN_REWARDS_SCREEN_VERSION_2 = 2;
  }
}

message GetClaimedRewardDetailsRequest {
  frontend.header.RequestHeader req = 1;
  // Id to identify the claimed reward/exchanger reward
  string reward_id = 2;
}

message GetClaimedRewardDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Contains details about the Claimed Reward/Exchanger reward
  ClaimedRewardDetails reward_details = 2;
}

message ForceRetryRewardProcessingRequest {
  frontend.header.RequestHeader req = 1;
  oneof reward_identifier {
    // Id to identify the claimed reward
    string reward_id = 2;
    // Id to identify the claimed exchanger reward
    string exchanger_order_id = 3;
  }
}

message ForceRetryRewardProcessingResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

service Rewards {

  // will be called for home screen tile
  // takes in only auth params and return quick summary need to be displayed on home screen tile
  rpc GetRewardDigest (GetRewardDigestRequest) returns (GetRewardDigestResponse) {
    option (rpc.auth_required) = true;
  }

  // will be called for my rewards screen
  // takes in only auth params and return summary need to be displayed on my rewards screen
  rpc GetRewardSummary (GetRewardSummaryRequest) returns (GetRewardSummaryResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // will be called for How to earn finvelops screen
  // takes in only auth params and return active reward offerings now
  // Deprecated in favour of GetWaysToEarnRewardsScreen
  rpc GetActiveRewardOffers (GetActiveRewardOffersRequest) returns (GetActiveRewardOffersResponse) {
    // this rpc is deprecated
    option deprecated = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetRewardOfferDetails rpc is useful to fetch rewardOffer details by offer-id.
  // Deprecated in favour of GetWaysToEarnRewardDetailScreen
  rpc GetRewardOfferDetails (GetRewardOfferDetailsRequest) returns (GetRewardOfferDetailsResponse) {
    // this rpc is deprecated
    option deprecated = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // will be called for How to earn finvelops screen
  // takes in only auth params and return active reward offerings now
  rpc GetEarnedRewards (GetEarnedRewardsRequest) returns (GetEarnedRewardsResponse) {
    option (rpc.auth_required) = true;
  }

  // fetches all the rewards for the actor for displaying on "My Rewards" Screen.
  // On "My Rewards" screen we'll show a combined list of rewards earned by doing a qualifying action as well as the
  // rewards earned by the user by redeeming an exchanger offer.
  rpc GetRewardsForActorV1 (GetRewardsForActorV1Request) returns (GetRewardsForActorV1Response) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch the exchanger orders for the actor.
  // This RPC returns only the claimed orders, i.e. the ones in in-progress/fulfilled state.
  // Note: verify the logic here if we plan to include "user-intervention-reqd" orders as well in the future.
  rpc GetExchangerOrdersForActor (GetExchangerOrdersForActorRequest) returns (GetExchangerOrdersForActorResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // will be called just before claimReward rpc to check if input is required for claiming reward.
  // takes in reward id and reward option id and returns deeplink to the screen where user input
  // should be taken.
  rpc GetClaimRewardInputScreen (ClaimRewardInputScreenRequest) returns (ClaimRewardInputScreenResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // will be called to claim a reward
  rpc ClaimReward (ClaimRewardRequest) returns (ClaimRewardResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // will be called to claim rewards in bulk
  rpc BulkClaimRewards (BulkClaimRewardsRequest) returns (BulkClaimRewardsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // will be called from offers screen
  // takes in only auth params and return active offers now
  rpc GetActiveOffers (GetActiveOffersRequest) returns (GetActiveOffersResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // takes in offer_id and returns details of the offer only if offer is active
  // will be called from deeplink DEBIT_CARD_OFFERS_HOME_SCREEN if DebitCardOffersHomeScreenOptions has offer_id field set
  rpc GetOfferDetailsById (GetOfferDetailsByIdRequest) returns (GetOfferDetailsByIdResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch the catalog of offers where the offers could vary based on their type.
  rpc GetOfferCatalogV1 (GetOfferCatalogV1Request) returns (GetOfferCatalogV1Response) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch rewards and offers related information for home including -
  // fi coins balance and cta to ways to earn page based on current fi coins balance
  // offers catalog consisting of fi coin offers, debit card offers and exchanger offers
  // Deprecated
  rpc GetRewardsAndOffersForHome (GetRewardsAndOffersForHomeRequest) returns (GetRewardsAndOffersForHomeResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch rewards, catalog offers and card offers related information including -
  // fi coins balance, cta to ways to earn page and catalog offers
  // debit card and credit offers
  rpc GetRewardsAndOffersWidget (GetRewardsAndOffersWidgetRequest) returns (GetRewardsAndOffersWidgetResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // will be called just before InitiateRedemption rpc to check if input is required for redeeming offer.
  // takes in offer id and and returns deeplink to the screen where user input should be taken.
  rpc GetRedeemOfferInputScreen (RedeemOfferInputScreenRequest) returns (RedeemOfferInputScreenResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // will be called from fi perks screen.
  // used to initiate a redemption.
  rpc InitiateRedemption (InitiateRedemptionRequest) returns (InitiateRedemptionResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // will be called from fi perks screen.
  // used to confirm an already initiated redemption.
  rpc ConfirmRedemption (ConfirmRedemptionRequest) returns (ConfirmRedemptionResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetRedeemedOffersV1 rpc to fetch active redeemed offers along with exchanger orders (of physical merch and EGV type) for an actor
  // it will be called from the "My Orders" screen.
  rpc GetRedeemedOffersV1 (GetRedeemedOffersV1Request) returns (GetRedeemedOffersV1Response) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for fetching collected offers page layout.
  // figma -> https://www.figma.com/design/Bc8Y41Lgv4YMmX00n8V9ZU/%F0%9F%8E%81-Rewards-%E2%80%A2-Workfile-2?node-id=20606-18937&t=kK4kZztOBk6ehR82-4
  rpc GetRedeemedOffersCatalogLayout (GetRedeemedOffersCatalogLayoutRequest) returns (GetRedeemedOffersCatalogLayoutResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // will be called from fi perks screen.
  // fetches active redeemed offers for an actor.
  rpc GetActiveRedeemedOffers (GetActiveRedeemedOffersRequest) returns (GetActiveRedeemedOffersResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // will be called from fi perks screen.
  // fetches expired redeemed offers for an actor.
  rpc GetExpiredRedeemedOffers (GetExpiredRedeemedOffersRequest) returns (GetExpiredRedeemedOffersResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // rpc to fetch the redirection url for viewing the redeemed offer details,
  // useful for offers where the post the offer redemption on the Fi App, the redeemed offer details are visible on the vendor app only.
  rpc GetRedeemedOfferDetailsRedirectionInfo (GetRedeemedOfferDetailsRedirectionInfoRequest) returns (GetRedeemedOfferDetailsRedirectionInfoResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch exchanger offer by id
  rpc GetExchangerOfferById (GetExchangerOfferByIdRequest) returns (GetExchangerOfferByIdResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC redeems the exchanger offer by trading the currency and returning the options to choose from
  rpc RedeemExchangerOffer (RedeemExchangerOfferRequest) returns (RedeemExchangerOfferResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to choose one of the options for an exchanger offer order
  rpc ChooseExchangerOrderOption (ChooseExchangerOrderOptionRequest) returns (ChooseExchangerOrderOptionResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to check if any user input is required for the exchanger order. If required,
  // it returns a deeplink to the screen from where input should be taken.
  rpc GetExchangerOrderInputScreen (GetExchangerOrderInputScreenRequest) returns (GetExchangerOrderInputScreenResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for submitting input obtained from users required for fulfillment of exchanger order.
  rpc SubmitExchangerOrderUserInput (SubmitExchangerOrderUserInputRequest) returns (SubmitExchangerOrderUserInputResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for fetching different components to be shown on CatalogOffersScreen
  rpc GetCatalogOffersAndFilters (GetCatalogOffersAndFiltersRequest) returns (GetCatalogOffersAndFiltersResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for fetching catalog offers page
  // Ref : https://docs.google.com/document/d/1RWsQAALGw3N9BR_thBooYhr-GugYKLbpIWPMKy3vSgI/edit#heading=h.c7ch7ebjupsg
  // figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=10010-14735&node-type=canvas&t=9r3xrmHUKNR6nOz8-0
  rpc GetOffersCatalogPage (GetOffersCatalogPageRequest) returns (GetOffersCatalogPageResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for fetching which version of offers catalog page to render
  // Ref :
  rpc GetOffersCatalogPageVersionToRender (GetOffersCatalogPageVersionToRenderRequest) returns (GetOffersCatalogPageVersionToRenderResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for fetching credit/debit card offers page layout tabs
  // https://www.figma.com/file/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?type=design&node-id=10230%3A12810&mode=design&t=e3mtrgFYWCSAMclM-1
  rpc GetCardOffersTabs (GetCardOffersTabsRequest) returns (GetCardOffersTabsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for fetching credit/debit card offers page layout.
  rpc GetCardOffersCatalogLayout (GetCardOffersCatalogLayoutRequest) returns (GetCardOffersCatalogLayoutResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for fetching fi card offers.
  rpc GetCardOffers (GetCardOffersRequest) returns (GetCardOffersResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetCardOfferDetails fetches credit/debit card offer details by offer id.
  rpc GetCardOfferDetails (GetCardOfferDetailsRequest) returns (GetCardOfferDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // rpc to fetch MyRewards screen details for actor
  rpc GetMyRewardsScreenDetails (GetMyRewardsScreenDetailsRequest) returns (GetMyRewardsScreenDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for getting details for the convert fi coins screen in offer redemption flow
  rpc GetConvertFiCoinsOfferRedemptionScreen (GetConvertFiCoinsOfferRedemptionScreenRequest) returns (GetConvertFiCoinsOfferRedemptionScreenResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for getting details for the user details screen in offer redemption flow
  rpc GetUserDetailsInputOfferRedemptionScreen (GetUserDetailsInputOfferRedemptionScreenRequest) returns (GetUserDetailsInputOfferRedemptionScreenResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for getting dynamic url for webpage with card details screen
  rpc GetDynamicUrlForWebPageScreen (GetDynamicUrlForWebPageScreenRequest) returns (GetDynamicUrlForWebPageScreenResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for getting ways to earn rewards screen
  rpc GetWaysToEarnRewardsScreen (GetWaysToEarnRewardsRequest) returns (GetWaysToEarnRewardsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for getting ways to earn detail screen
  rpc GetWaysToEarnRewardDetailScreen (GetWaysToEarnRewardDetailRequest) returns (GetWaysToEarnRewardDetailResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC for telling client which WaysToEarnRewards screen is to be rendered (v1 or v2)
  // this is a temporary RPC being used for transitioning from V1 of ways to earn rewards screen to V2
  rpc GetWaysToEarnRewardsScreenVersionToRender (GetWaysToEarnRewardsScreenVersionToRenderRequest) returns (GetWaysToEarnRewardsScreenVersionToRenderResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to return the details to render a Claimed Reward or Exchanged rewards screen on clients. Figma:
  // https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=12244%3A79466&t=buqkfpbtIexoCbix-4
  rpc GetClaimedRewardDetails (GetClaimedRewardDetailsRequest) returns (GetClaimedRewardDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to force retry reward processing for stuck rewards
  rpc ForceRetryRewardProcessing (ForceRetryRewardProcessingRequest) returns (ForceRetryRewardProcessingResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}
