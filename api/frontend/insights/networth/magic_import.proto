syntax = "proto3";

package frontend.insights.networth;

import "api/frontend/insights/networth/manual_form.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/header_bar.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";

option go_package = "github.com/epifi/gamma/api/frontend/insights/networth";
option java_package = "com.github.epifi.gamma.api.frontend.insights.networth";

// Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12516-8011&t=vjav20xKlpC3t1ui-4
message MagicImportedAssetsListScreen {
  // Toolbar
  api.typesv2.HeaderBar header_bar = 1;
  AssetsSummary assets_summary = 2;
  repeated ImportedAssetsListComponent imported_assets_list_components = 3;
  FooterComponent footer_component = 4;
}

message AssetsSummary {
  api.typesv2.common.Text title = 1;
  HeroImage hero_image = 2;
  api.typesv2.ui.VerticalKeyValuePair assets_info = 3;
  FeedbackComponent feedback_component = 4;
}

message HeroImage {
  // this is the background lottie for the hero image
  api.typesv2.common.VisualElement bg_lottie = 1;
  // Frame which gets displayed on top of hero image
  api.typesv2.common.VisualElement frame = 2;
}

message FeedbackComponent {
  FeedbackView thumbs_up = 1;
  FeedbackView thumbs_down = 2;
}

message FeedbackView {
  api.typesv2.common.VisualElement normal_view = 1;
  api.typesv2.common.VisualElement selected_view = 2;
}

message ImportedAssetsListComponent {
  api.typesv2.common.Text title = 1;
  repeated ImportedAssetsListItem imported_assets_list_items = 2;
  api.typesv2.common.ui.widget.BackgroundColour border_color = 3;
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 4;
  api.typesv2.common.ui.widget.BackgroundColour divider_color = 5;
}

message ImportedAssetsListItem {
  api.typesv2.common.Text asset_name = 1;
  api.typesv2.ui.IconTextComponent asset_value = 2;
  api.typesv2.ui.IconTextComponent import_error = 3;
  ImportedAssetsListItemEditDetails edit_details = 4;
  bool is_selected = 5;
  bool is_editable = 6;
  // this will tell the client that some information related to asset is missing
  // it can't be added to networth without provided info
  bool has_missing_info = 7;
  // this will be used to identify related image on client side
  string file_name = 8;
}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12384-4503&t=vjav20xKlpC3t1ui-4
// these are details to fill in to edit the import asset fetched details
message ImportedAssetsListItemEditDetails {
  api.typesv2.common.Text title = 1;
  frontend.insights.networth.NetWorthManualForm manual_form = 2;
}

message FooterComponent {
  api.typesv2.ui.IconTextComponent share_button = 1;
  api.typesv2.ui.IconTextComponent add_to_networth_button = 2;
}
