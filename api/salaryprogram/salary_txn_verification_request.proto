// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=SalaryTxnVerificationRequestSource,SalaryTxnVerificationVersion,SalaryTxnVerificationRequestStatus,SalaryTxnVerificationRequestSubStatus,SalaryTxnVerificationRequestVerifiedBy,AcknowledgmentStatus,SalaryTxnVerificationFailureReasonCategory,SalaryTxnVerificationFailureReasonSubCategory,SalaryAutoVerifierMeta
syntax = "proto3";

package salaryprogram;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/salaryprogram";
option java_package = "com.github.epifi.gamma.api.salaryprogram";

// SalaryTxnVerificationRequest denotes the request raised for a salary txn verification.
message SalaryTxnVerificationRequest {
  // unique identifier of verification request
  string id = 1;

  // denotes the actor whose txn needs to be verified.
  string actor_id = 2;

  // external order id of the the txn which needs to be verified.
  string txn_id = 3;

  // denotes the source from which the verification request was raised.
  SalaryTxnVerificationRequestSource request_source = 4;

  // denotes the current status of verification.
  SalaryTxnVerificationRequestStatus verification_status = 5;

  // denotes the sub-status of verification.
  SalaryTxnVerificationRequestSubStatus verification_sub_status = 6;

  // denotes who verified the request.
  SalaryTxnVerificationRequestVerifiedBy verified_by = 7;

  // denotes the version of salary txn verification that is used to verify the current request.
  SalaryTxnVerificationVersion verification_version = 9;

  // internal id of employer who had initiated the salary txn.
  // this could be auto-populated for auto-verified salary txn verification request or
  // could be updated manually by ops for ops verified salary txn verification request.
  string txn_employer_id = 10;

  // txn timestamp, helpful for sorting verification request based on txn recency.
  google.protobuf.Timestamp txn_timestamp = 11;

  // useful for marking that a given verification request's status was acknowledged by the user,
  // useful for manually raised salary verification requests where user ack is needed for dismissing the request from the app.
  AcknowledgmentStatus user_ack_status = 12;

  // denotes the reason category due to which the verification is marked as failed for this txn,
  // will be UNSPECIFIED if the verification status is not FAILED.
  SalaryTxnVerificationFailureReasonCategory verification_failure_reason_category = 13;
  // denotes the reason sub-category due to which the verification is marked as failed for this txn,
  // will be UNSPECIFIED if the verification status is not FAILED.
  SalaryTxnVerificationFailureReasonSubCategory verification_failure_reason_sub_category = 14;
  // denotes the verification remark for the txn
  string verification_remark = 18;

  // contains metadata of checks ran on txn by in-house salary auto verifier, ex- field used for remitter name if the verification is success
  SalaryAutoVerifierMeta auto_verifier_meta = 19;

  google.protobuf.Timestamp created_at = 15;
  google.protobuf.Timestamp updated_at = 16;
  google.protobuf.Timestamp deleted_at = 17;


}

message VerificationFailureReasonSubCategories {
  // supported salary txn verification failure reason sub-categories.
  repeated string sub_categories = 1;
}

// SalaryTxnVerificationRequestSource denotes the source from which the salary txn verification request was raised.
enum SalaryTxnVerificationRequestSource {
  SALARY_TXN_VERIFICATION_REQUEST_SOURCE_UNSPECIFIED = 0;
  VERIFICATION_REQUEST_SOURCE_TXN_EVENT = 1;
  VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST = 2;
  VERIFICATION_REQUEST_SOURCE_SALARY_OPS_REQUEST = 3;
  // verification request created from script to activate salary benefits
  VERIFICATION_REQUEST_SOURCE_ACTIVATION_SCRIPT = 4;
}

// SalaryTxnVerificationVersion denotes the version of salary txn verification used to verify a verification request.
// checks could change over time hence version would help know why a particular txn was verified or failed.
enum SalaryTxnVerificationVersion {
  SALARY_TXN_VERIFICATION_VERSION_UNSPECIFIED = 0;
  // In Version V1 verification logic includes checking
  // 1. if the txn does not has REFUND tag
  // 2. txn_amount >= 25k
  // 3. txn_protocol IN (NEFT, RTGS, IMPS)
  // 4. remitter_name of txn == current employer of user
  VERIFICATION_VERSION_V1 = 1;
}

// SalaryTxnVerificationRequestStatus denotes the status of salary txn verification request.
enum SalaryTxnVerificationRequestStatus {
  SALARY_TXN_VERIFICATION_REQUEST_STATUS_UNSPECIFIED = 0;
  REQUEST_STATUS_IN_PROGRESS = 1;
  REQUEST_STATUS_VERIFIED = 2;
  REQUEST_STATUS_VERIFICATION_FAILED = 3;
}

// SalaryTxnVerificationRequestSubStatus denotes the sub-status of salary txn verification request.
enum SalaryTxnVerificationRequestSubStatus {
  SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED = 0;
  // denotes that the verification request needs to manually reviewed by the ops team.
  REQUEST_SUB_STATUS_AWAITING_OPS_VERIFICATION = 1;
  // denotes that the requested txn is a genuine salary txn but employer is different from what is linked to user's profile.
  // user needs to update his employment details for the verification to be completed.
  REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE = 2;
  // denotes that verification request is escalated to the engg team for review.
  REQUEST_STATUS_ESCALATED_TO_ENGG_TEAM = 3;
}

// SalaryTxnVerificationRequestVerifiedBy denotes who verified the salary txn verification request .
enum SalaryTxnVerificationRequestVerifiedBy {
  SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED = 0;
  VERIFIED_BY_SYSTEM = 1;
  VERIFIED_BY_OPS = 2;
}

enum SalaryTxnVerificationRequestFieldMask {
  SALARY_TXN_VERIFICATION_REQ_FIELD_MASK_UNSPECIFIED = 0;
  VERIFICATION_STATUS = 1;
  VERIFICATION_SUB_STATUS = 2;
  VERIFIED_BY = 3;
  TXN_EMPLOYER_ID = 4;
  USER_ACK_STATUS = 5;
  VERIFICATION_FAILURE_REASON_CATEGORY = 6;
  VERIFICATION_FAILURE_REASON_SUB_CATEGORY = 7;
  VERIFICATION_REMARK = 8;
  AUTO_VERIFIER_META = 9;
}

enum AcknowledgmentStatus {
  ACKNOWLEDGEMENT_STATUS_UNSPECIFIED = 0;
  ACKNOWLEDGED = 1;
  ACKNOWLEDGMENT_NOT_REQUIRED = 2;
}

enum SalaryTxnVerificationFailureReasonCategory {
  FAILURE_REASON_CATEGORY_UNSPECIFIED = 0;
  EMPLOYER_IS_NOT_AN_ORG = 1;
  REMITTER_IS_NOT_AN_ENTITY = 2;
  NOT_PART_OF_EMPLOYER_DB = 3;
  INSUFFICIENT_INFO = 4;
  EMPLOYER_IS_A_FINTECH_COMPANY = 5;
  REMITTER_IS_A_FINTECH_COMPANY = 6;
  NOT_A_SALARY_TRANSACTION = 7;
  TRANSACTION_SOURCE_ISSUE = 8;
}

// SalaryTxnVerificationFailureReasonSubCategory is sub category of SalaryTxnVerificationFailureReasonCategory
enum SalaryTxnVerificationFailureReasonSubCategory {
  FAILURE_REASON_SUB_CATEGORY_UNSPECIFIED = 0;
  EMPLOYER_NAME_SAME_AS_THE_ACCOUNT_HOLDER = 1;
  EMPLOYER_NAME_AS_ANOTHER_INDIVIDUAL = 2;
  REMITTER_NAME_SAME_AS_THE_ACCOUNT_HOLDER = 3;
  REMITTER_NAME_AS_ANOTHER_INDIVIDUAL = 4;
  NOT_VERIFICABLE_BY_GSTIN_ALSO = 5;
  REMITTER_NAME_NOT_AVAILABLE = 6;
  PARTIAL_REMITTER_NAME = 7;
  PAYMENT_PROTOCOL_IS_UNSPECIFIED = 8;
  OTHERS = 9;
  MULTIPLE_REMITTER_NAME = 10;
  MERCHANT_ACCOUNT_TRANSFER = 11;
  EXPENSE_REIMBURSEMENT = 12;
  PF_WITHDRAWAL = 13;
  REMITTER_IS_A_LENDING_COMPANY = 14;
  REMITTER_IS_A_FINANCIAL_INSTITUTION = 15;
  INVESTMENT_WITHDRAWAL = 16;
}

// contains metadata of checks ran on txn by in-house salary auto verifier fo, ex- field used for remitter name if the verification is success
message SalaryAutoVerifierMeta {
  VerificationSuccessMeta verification_success_meta = 1;
  message VerificationSuccessMeta {
    // field used for txn remitter name to match with employer, ex- PI -> verfied_name
    FieldUsedForTxnRemitterName field_used_for_txn_remitter_name = 1;
    // employer matched with txn remitter to verify txn as salary txn
    EmployerMatchedWithTxnRemitter employer_matched_with_txn_remitter = 2;
    // logic used for matching txn remitter to the employer
    RemitterToEmployerMatchLogic remitter_to_employer_match_logic = 3;
  }

  // logic used for matching txn remitter to the employer
  enum RemitterToEmployerMatchLogic {
    REMITTER_TO_EMPLOYER_MATCH_LOGIC_UNSPECIFIED = 0;
    // when ds api was used to match remitter name to employer name
    DS_NAME_MATCH = 1;
    // when txn remitter was matched with the PI id associated with the employer
    PAYMENT_INSTRUMENT_ID_MATCH = 2;
  }

  // field used for txn remitter name to match with employer, ex- PI -> verfied_name
  enum FieldUsedForTxnRemitterName {
    FIELD_USED_FOR_TXN_REMITTER_NAME_MATCH_UNSPECIFIED = 0;
    PAYMENT_INSTRUMENT_VERIFIED_NAME = 1;
    PAYMENT_INSTRUMENT_ACCOUNT_NAME = 2;
    TXN_REMITTER_ACTOR_NAME = 3;
    NAME_FROM_FEDERAL_REMITTER_API = 4;
  }

  // employer matched with txn remitter
  enum EmployerMatchedWithTxnRemitter {
    EMPLOYER_MATCHED_WITH_TXN_REMITTER_UNSPECIFIED = 0;
    // employer that user declared was used for txn verification
    USER_DECLARED_EMPLOYER = 1;
    // employer from employer db was used for txn verification
    EMPLOYER_FROM_EMPLOYER_DB = 2;
    // for set of PI ids, salary verification is bypassed to prevent downgrade of salary employee to salary basic
    BYPASS_PAYMENT_INSTRUMENT_ID_MATCH = 3;
  }
}
