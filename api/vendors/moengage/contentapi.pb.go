// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/moengage/contentapi.proto

package moengage

import (
	insights "github.com/epifi/gamma/api/vendors/moengage/insights"
	stocks "github.com/epifi/gamma/api/vendors/moengage/stocks"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetUserAttributesRequest contains all the query parameters that the content API will support.
// To keep the API extendable with minimum changes on Moengage dashboard, we'll support taking in a list of fields (string)
// that we want to fetch and a list of key-value pairs for any data that may be needed to fetch the required information.
type GetUserAttributesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// moengage's user ID that maps 1:1 to Fi's internal actorId
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// comma separated list of area names for which we want to fetch PII information, e.g., REWARDS,QUEST
	Areas string `protobuf:"bytes,2,opt,name=areas,proto3" json:"areas,omitempty"`
	// comma separated list of fields to fetch. API's response
	FieldMask string `protobuf:"bytes,3,opt,name=field_mask,json=fieldMask,proto3" json:"field_mask,omitempty"`
	// comma separated list of information required by services to fetch the fields, like externalTxnId, summaryDuration, etc.
	// information needs to be present as an object of key:value pairs. e.g., txn_id:XXXXXXXX, duration:7d, etc.
	RequestMetadata string `protobuf:"bytes,4,opt,name=request_metadata,json=requestMetadata,proto3" json:"request_metadata,omitempty"`
}

func (x *GetUserAttributesRequest) Reset() {
	*x = GetUserAttributesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAttributesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAttributesRequest) ProtoMessage() {}

func (x *GetUserAttributesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAttributesRequest.ProtoReflect.Descriptor instead.
func (*GetUserAttributesRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{0}
}

func (x *GetUserAttributesRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserAttributesRequest) GetAreas() string {
	if x != nil {
		return x.Areas
	}
	return ""
}

func (x *GetUserAttributesRequest) GetFieldMask() string {
	if x != nil {
		return x.FieldMask
	}
	return ""
}

func (x *GetUserAttributesRequest) GetRequestMetadata() string {
	if x != nil {
		return x.RequestMetadata
	}
	return ""
}

// GetUserAttributesResponse is the response object that will be returned by the content API
type GetUserAttributesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// contains key:value pairs where keys are areas given in requested fields
	// value is map of key:value pairs of field name to user attributes
	AreaToFieldNameUserAttributes map[string]*FieldNameToUserAttributeMap `protobuf:"bytes,1,rep,name=area_to_field_name_user_attributes,proto3" json:"area_to_field_name_user_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetUserAttributesResponse) Reset() {
	*x = GetUserAttributesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAttributesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAttributesResponse) ProtoMessage() {}

func (x *GetUserAttributesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAttributesResponse.ProtoReflect.Descriptor instead.
func (*GetUserAttributesResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserAttributesResponse) GetAreaToFieldNameUserAttributes() map[string]*FieldNameToUserAttributeMap {
	if x != nil {
		return x.AreaToFieldNameUserAttributes
	}
	return nil
}

type FieldNameToUserAttributeMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// contains key:value pairs where keys are the requested fields in  GetPiiDataRequest.fields_to_fetch
	// and values are the data that's requested.
	FieldNameToUserAttributes map[string]*UserAttribute `protobuf:"bytes,1,rep,name=field_name_to_user_attributes,proto3" json:"field_name_to_user_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FieldNameToUserAttributeMap) Reset() {
	*x = FieldNameToUserAttributeMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldNameToUserAttributeMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldNameToUserAttributeMap) ProtoMessage() {}

func (x *FieldNameToUserAttributeMap) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldNameToUserAttributeMap.ProtoReflect.Descriptor instead.
func (*FieldNameToUserAttributeMap) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{2}
}

func (x *FieldNameToUserAttributeMap) GetFieldNameToUserAttributes() map[string]*UserAttribute {
	if x != nil {
		return x.FieldNameToUserAttributes
	}
	return nil
}

type UserAttribute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to ValueTypes:
	//
	//	*UserAttribute_StringValue
	//	*UserAttribute_RewardsSummary
	//	*UserAttribute_SpendsSummary
	//	*UserAttribute_LoanSummary
	//	*UserAttribute_CreditCardSummary
	//	*UserAttribute_DebitCardSummary
	//	*UserAttribute_MarketIndexSummary
	//	*UserAttribute_StockDetails
	//	*UserAttribute_AaSalaryProgramSummary
	//	*UserAttribute_LoanOfferDetails
	//	*UserAttribute_PortfolioChangeDetails
	ValueTypes isUserAttribute_ValueTypes `protobuf_oneof:"ValueTypes"`
}

func (x *UserAttribute) Reset() {
	*x = UserAttribute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAttribute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAttribute) ProtoMessage() {}

func (x *UserAttribute) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAttribute.ProtoReflect.Descriptor instead.
func (*UserAttribute) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{3}
}

func (m *UserAttribute) GetValueTypes() isUserAttribute_ValueTypes {
	if m != nil {
		return m.ValueTypes
	}
	return nil
}

func (x *UserAttribute) GetStringValue() string {
	if x, ok := x.GetValueTypes().(*UserAttribute_StringValue); ok {
		return x.StringValue
	}
	return ""
}

func (x *UserAttribute) GetRewardsSummary() *RewardsSummary {
	if x, ok := x.GetValueTypes().(*UserAttribute_RewardsSummary); ok {
		return x.RewardsSummary
	}
	return nil
}

func (x *UserAttribute) GetSpendsSummary() *insights.SpendsSummary {
	if x, ok := x.GetValueTypes().(*UserAttribute_SpendsSummary); ok {
		return x.SpendsSummary
	}
	return nil
}

func (x *UserAttribute) GetLoanSummary() *LoanSummary {
	if x, ok := x.GetValueTypes().(*UserAttribute_LoanSummary); ok {
		return x.LoanSummary
	}
	return nil
}

func (x *UserAttribute) GetCreditCardSummary() *CreditCardSummary {
	if x, ok := x.GetValueTypes().(*UserAttribute_CreditCardSummary); ok {
		return x.CreditCardSummary
	}
	return nil
}

func (x *UserAttribute) GetDebitCardSummary() *DebitCardSummary {
	if x, ok := x.GetValueTypes().(*UserAttribute_DebitCardSummary); ok {
		return x.DebitCardSummary
	}
	return nil
}

func (x *UserAttribute) GetMarketIndexSummary() *stocks.MarketIndexSummary {
	if x, ok := x.GetValueTypes().(*UserAttribute_MarketIndexSummary); ok {
		return x.MarketIndexSummary
	}
	return nil
}

func (x *UserAttribute) GetStockDetails() *stocks.Stock {
	if x, ok := x.GetValueTypes().(*UserAttribute_StockDetails); ok {
		return x.StockDetails
	}
	return nil
}

func (x *UserAttribute) GetAaSalaryProgramSummary() *AaSalaryProgramSummary {
	if x, ok := x.GetValueTypes().(*UserAttribute_AaSalaryProgramSummary); ok {
		return x.AaSalaryProgramSummary
	}
	return nil
}

func (x *UserAttribute) GetLoanOfferDetails() *LoanOfferDetails {
	if x, ok := x.GetValueTypes().(*UserAttribute_LoanOfferDetails); ok {
		return x.LoanOfferDetails
	}
	return nil
}

func (x *UserAttribute) GetPortfolioChangeDetails() *PortfolioChangeDetails {
	if x, ok := x.GetValueTypes().(*UserAttribute_PortfolioChangeDetails); ok {
		return x.PortfolioChangeDetails
	}
	return nil
}

type isUserAttribute_ValueTypes interface {
	isUserAttribute_ValueTypes()
}

type UserAttribute_StringValue struct {
	// for simple string values
	StringValue string `protobuf:"bytes,1,opt,name=string_value,proto3,oneof"`
}

type UserAttribute_RewardsSummary struct {
	// for summary of rewards earned by the user
	RewardsSummary *RewardsSummary `protobuf:"bytes,2,opt,name=rewards_summary,proto3,oneof"`
}

type UserAttribute_SpendsSummary struct {
	// summary of user spends over a time period
	SpendsSummary *insights.SpendsSummary `protobuf:"bytes,3,opt,name=spends_summary,proto3,oneof"`
}

type UserAttribute_LoanSummary struct {
	// Summary of loan details
	LoanSummary *LoanSummary `protobuf:"bytes,4,opt,name=loan_summary,proto3,oneof"`
}

type UserAttribute_CreditCardSummary struct {
	// Summary of credit card details
	CreditCardSummary *CreditCardSummary `protobuf:"bytes,5,opt,name=credit_card_summary,proto3,oneof"`
}

type UserAttribute_DebitCardSummary struct {
	// summary of debit card details
	DebitCardSummary *DebitCardSummary `protobuf:"bytes,6,opt,name=debit_card_summary,proto3,oneof"`
}

type UserAttribute_MarketIndexSummary struct {
	// summary of a stock market index
	MarketIndexSummary *stocks.MarketIndexSummary `protobuf:"bytes,7,opt,name=market_index_summary,proto3,oneof"`
}

type UserAttribute_StockDetails struct {
	// detailed info about a stock
	StockDetails *stocks.Stock `protobuf:"bytes,8,opt,name=stock_details,proto3,oneof"`
}

type UserAttribute_AaSalaryProgramSummary struct {
	// detailed info about aasalary
	AaSalaryProgramSummary *AaSalaryProgramSummary `protobuf:"bytes,9,opt,name=aa_salary_program_summary,proto3,oneof"`
}

type UserAttribute_LoanOfferDetails struct {
	// detailed info about a loan offer
	LoanOfferDetails *LoanOfferDetails `protobuf:"bytes,10,opt,name=loan_offer_details,proto3,oneof"`
}

type UserAttribute_PortfolioChangeDetails struct {
	// details about daily portfolio change
	PortfolioChangeDetails *PortfolioChangeDetails `protobuf:"bytes,11,opt,name=portfolio_change_details,proto3,oneof"`
}

func (*UserAttribute_StringValue) isUserAttribute_ValueTypes() {}

func (*UserAttribute_RewardsSummary) isUserAttribute_ValueTypes() {}

func (*UserAttribute_SpendsSummary) isUserAttribute_ValueTypes() {}

func (*UserAttribute_LoanSummary) isUserAttribute_ValueTypes() {}

func (*UserAttribute_CreditCardSummary) isUserAttribute_ValueTypes() {}

func (*UserAttribute_DebitCardSummary) isUserAttribute_ValueTypes() {}

func (*UserAttribute_MarketIndexSummary) isUserAttribute_ValueTypes() {}

func (*UserAttribute_StockDetails) isUserAttribute_ValueTypes() {}

func (*UserAttribute_AaSalaryProgramSummary) isUserAttribute_ValueTypes() {}

func (*UserAttribute_LoanOfferDetails) isUserAttribute_ValueTypes() {}

func (*UserAttribute_PortfolioChangeDetails) isUserAttribute_ValueTypes() {}

type RewardsSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FiCoinsRewardsSummary string `protobuf:"bytes,1,opt,name=fi_coins_rewards_summary,proto3" json:"fi_coins_rewards_summary,omitempty"`
	CashRewardsSummary    string `protobuf:"bytes,2,opt,name=cash_rewards_summary,proto3" json:"cash_rewards_summary,omitempty"`
}

func (x *RewardsSummary) Reset() {
	*x = RewardsSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardsSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardsSummary) ProtoMessage() {}

func (x *RewardsSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardsSummary.ProtoReflect.Descriptor instead.
func (*RewardsSummary) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{4}
}

func (x *RewardsSummary) GetFiCoinsRewardsSummary() string {
	if x != nil {
		return x.FiCoinsRewardsSummary
	}
	return ""
}

func (x *RewardsSummary) GetCashRewardsSummary() string {
	if x != nil {
		return x.CashRewardsSummary
	}
	return ""
}

type LoanSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmiAmount string `protobuf:"bytes,1,opt,name=emi_amount,proto3" json:"emi_amount,omitempty"`
	// Emi date. For users who missed a EMI, this data will be in the past, for other this will be in future
	EmiDate string `protobuf:"bytes,2,opt,name=emi_date,proto3" json:"emi_date,omitempty"`
	// Days past due date
	Dpd int32 `protobuf:"varint,3,opt,name=dpd,proto3" json:"dpd,omitempty"`
	// Days till due date
	DaysTillNextDue int32 `protobuf:"varint,4,opt,name=days_till_next_due,proto3" json:"days_till_next_due,omitempty"`
	// Bank account details
	BankDetails *BankDetails `protobuf:"bytes,5,opt,name=bank_details,proto3" json:"bank_details,omitempty"`
}

func (x *LoanSummary) Reset() {
	*x = LoanSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanSummary) ProtoMessage() {}

func (x *LoanSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanSummary.ProtoReflect.Descriptor instead.
func (*LoanSummary) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{5}
}

func (x *LoanSummary) GetEmiAmount() string {
	if x != nil {
		return x.EmiAmount
	}
	return ""
}

func (x *LoanSummary) GetEmiDate() string {
	if x != nil {
		return x.EmiDate
	}
	return ""
}

func (x *LoanSummary) GetDpd() int32 {
	if x != nil {
		return x.Dpd
	}
	return 0
}

func (x *LoanSummary) GetDaysTillNextDue() int32 {
	if x != nil {
		return x.DaysTillNextDue
	}
	return 0
}

func (x *LoanSummary) GetBankDetails() *BankDetails {
	if x != nil {
		return x.BankDetails
	}
	return nil
}

type BankDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BankAccountNumber string `protobuf:"bytes,1,opt,name=bank_account_number,proto3" json:"bank_account_number,omitempty"`
	BankName          string `protobuf:"bytes,2,opt,name=bank_name,proto3" json:"bank_name,omitempty"`
}

func (x *BankDetails) Reset() {
	*x = BankDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankDetails) ProtoMessage() {}

func (x *BankDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankDetails.ProtoReflect.Descriptor instead.
func (*BankDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{6}
}

func (x *BankDetails) GetBankAccountNumber() string {
	if x != nil {
		return x.BankAccountNumber
	}
	return ""
}

func (x *BankDetails) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

type CreditCardSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstName              string `protobuf:"bytes,1,opt,name=first_name,proto3" json:"first_name,omitempty"`
	BillAmount             string `protobuf:"bytes,2,opt,name=bill_amount,proto3" json:"bill_amount,omitempty"`
	LastFourDigitsOfCard   string `protobuf:"bytes,3,opt,name=last_four_digits_of_card,proto3" json:"last_four_digits_of_card,omitempty"`
	DueDate                string `protobuf:"bytes,4,opt,name=due_date,proto3" json:"due_date,omitempty"`
	BillGenerationDate     string `protobuf:"bytes,5,opt,name=bill_generation_date,proto3" json:"bill_generation_date,omitempty"`
	MinimumAmountDue       string `protobuf:"bytes,6,opt,name=minimum_amount_due,proto3" json:"minimum_amount_due,omitempty"`
	LimitUtilizedPercent   string `protobuf:"bytes,7,opt,name=limit_utilized_percent,proto3" json:"limit_utilized_percent,omitempty"`
	NumberOfDaysOverdue    string `protobuf:"bytes,8,opt,name=number_of_days_overdue,proto3" json:"number_of_days_overdue,omitempty"`
	UnpaidBillAmount       string `protobuf:"bytes,9,opt,name=unpaid_bill_amount,proto3" json:"unpaid_bill_amount,omitempty"`
	UnpaidMinimumAmountDue string `protobuf:"bytes,10,opt,name=unpaid_minimum_amount_due,proto3" json:"unpaid_minimum_amount_due,omitempty"`
	IsMinimumAmountDuePaid bool   `protobuf:"varint,11,opt,name=is_minimum_amount_due_paid,proto3" json:"is_minimum_amount_due_paid,omitempty"`
	IsBillAmountPaid       bool   `protobuf:"varint,12,opt,name=is_bill_amount_paid,proto3" json:"is_bill_amount_paid,omitempty"`
	InterestCharges        string `protobuf:"bytes,13,opt,name=interest_charges,proto3" json:"interest_charges,omitempty"`
}

func (x *CreditCardSummary) Reset() {
	*x = CreditCardSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardSummary) ProtoMessage() {}

func (x *CreditCardSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardSummary.ProtoReflect.Descriptor instead.
func (*CreditCardSummary) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{7}
}

func (x *CreditCardSummary) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CreditCardSummary) GetBillAmount() string {
	if x != nil {
		return x.BillAmount
	}
	return ""
}

func (x *CreditCardSummary) GetLastFourDigitsOfCard() string {
	if x != nil {
		return x.LastFourDigitsOfCard
	}
	return ""
}

func (x *CreditCardSummary) GetDueDate() string {
	if x != nil {
		return x.DueDate
	}
	return ""
}

func (x *CreditCardSummary) GetBillGenerationDate() string {
	if x != nil {
		return x.BillGenerationDate
	}
	return ""
}

func (x *CreditCardSummary) GetMinimumAmountDue() string {
	if x != nil {
		return x.MinimumAmountDue
	}
	return ""
}

func (x *CreditCardSummary) GetLimitUtilizedPercent() string {
	if x != nil {
		return x.LimitUtilizedPercent
	}
	return ""
}

func (x *CreditCardSummary) GetNumberOfDaysOverdue() string {
	if x != nil {
		return x.NumberOfDaysOverdue
	}
	return ""
}

func (x *CreditCardSummary) GetUnpaidBillAmount() string {
	if x != nil {
		return x.UnpaidBillAmount
	}
	return ""
}

func (x *CreditCardSummary) GetUnpaidMinimumAmountDue() string {
	if x != nil {
		return x.UnpaidMinimumAmountDue
	}
	return ""
}

func (x *CreditCardSummary) GetIsMinimumAmountDuePaid() bool {
	if x != nil {
		return x.IsMinimumAmountDuePaid
	}
	return false
}

func (x *CreditCardSummary) GetIsBillAmountPaid() bool {
	if x != nil {
		return x.IsBillAmountPaid
	}
	return false
}

func (x *CreditCardSummary) GetInterestCharges() string {
	if x != nil {
		return x.InterestCharges
	}
	return ""
}

type DebitCardSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ZeroForexMarkupSavedTillNow          string `protobuf:"bytes,1,opt,name=zero_forex_markup_saved_till_now,proto3" json:"zero_forex_markup_saved_till_now,omitempty"`
	ZeroForexMarkupSavedForLastSevenDays string `protobuf:"bytes,2,opt,name=zero_forex_markup_saved_for_last_seven_days,proto3" json:"zero_forex_markup_saved_for_last_seven_days,omitempty"`
	DomesticTransactionsAmount           string `protobuf:"bytes,3,opt,name=domestic_transactions_amount,proto3" json:"domestic_transactions_amount,omitempty"`
	InternationalTransactionsAmount      string `protobuf:"bytes,4,opt,name=international_transactions_amount,proto3" json:"international_transactions_amount,omitempty"`
	CommunicationAddress                 string `protobuf:"bytes,5,opt,name=communication_address,proto3" json:"communication_address,omitempty"`
	PermanentAddress                     string `protobuf:"bytes,6,opt,name=permanent_address,proto3" json:"permanent_address,omitempty"`
}

func (x *DebitCardSummary) Reset() {
	*x = DebitCardSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebitCardSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebitCardSummary) ProtoMessage() {}

func (x *DebitCardSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebitCardSummary.ProtoReflect.Descriptor instead.
func (*DebitCardSummary) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{8}
}

func (x *DebitCardSummary) GetZeroForexMarkupSavedTillNow() string {
	if x != nil {
		return x.ZeroForexMarkupSavedTillNow
	}
	return ""
}

func (x *DebitCardSummary) GetZeroForexMarkupSavedForLastSevenDays() string {
	if x != nil {
		return x.ZeroForexMarkupSavedForLastSevenDays
	}
	return ""
}

func (x *DebitCardSummary) GetDomesticTransactionsAmount() string {
	if x != nil {
		return x.DomesticTransactionsAmount
	}
	return ""
}

func (x *DebitCardSummary) GetInternationalTransactionsAmount() string {
	if x != nil {
		return x.InternationalTransactionsAmount
	}
	return ""
}

func (x *DebitCardSummary) GetCommunicationAddress() string {
	if x != nil {
		return x.CommunicationAddress
	}
	return ""
}

func (x *DebitCardSummary) GetPermanentAddress() string {
	if x != nil {
		return x.PermanentAddress
	}
	return ""
}

type AaSalaryProgramSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastActivatedMonth               int32  `protobuf:"varint,1,opt,name=last_activated_month,proto3" json:"last_activated_month,omitempty"`
	LastActivatedYear                int32  `protobuf:"varint,2,opt,name=last_activated_year,proto3" json:"last_activated_year,omitempty"`
	TransferDaysDue                  int32  `protobuf:"varint,3,opt,name=transfer_days_due,proto3" json:"transfer_days_due,omitempty"`
	TransferOverDueSince             int32  `protobuf:"varint,4,opt,name=transfer_over_due_since,proto3" json:"transfer_over_due_since,omitempty"`
	IsAaSalaryActive                 bool   `protobuf:"varint,5,opt,name=is_aa_salary_active,proto3" json:"is_aa_salary_active,omitempty"`
	DaysSinceLastTransfer            int32  `protobuf:"varint,6,opt,name=days_since_last_transfer,proto3" json:"days_since_last_transfer,omitempty"`
	UpcomingActivationMonth          int32  `protobuf:"varint,7,opt,name=upcoming_activation_month,proto3" json:"upcoming_activation_month,omitempty"`
	UpcomingActivationYear           int32  `protobuf:"varint,8,opt,name=upcoming_activation_year,proto3" json:"upcoming_activation_year,omitempty"`
	CashbackForLatestCommittedAmount string `protobuf:"bytes,9,opt,name=cashback_for_latest_committed_amount,proto3" json:"cashback_for_latest_committed_amount,omitempty"`
	LatestCommittedAmount            int64  `protobuf:"varint,10,opt,name=latest_committed_amount,proto3" json:"latest_committed_amount,omitempty"`
}

func (x *AaSalaryProgramSummary) Reset() {
	*x = AaSalaryProgramSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaSalaryProgramSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaSalaryProgramSummary) ProtoMessage() {}

func (x *AaSalaryProgramSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaSalaryProgramSummary.ProtoReflect.Descriptor instead.
func (*AaSalaryProgramSummary) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{9}
}

func (x *AaSalaryProgramSummary) GetLastActivatedMonth() int32 {
	if x != nil {
		return x.LastActivatedMonth
	}
	return 0
}

func (x *AaSalaryProgramSummary) GetLastActivatedYear() int32 {
	if x != nil {
		return x.LastActivatedYear
	}
	return 0
}

func (x *AaSalaryProgramSummary) GetTransferDaysDue() int32 {
	if x != nil {
		return x.TransferDaysDue
	}
	return 0
}

func (x *AaSalaryProgramSummary) GetTransferOverDueSince() int32 {
	if x != nil {
		return x.TransferOverDueSince
	}
	return 0
}

func (x *AaSalaryProgramSummary) GetIsAaSalaryActive() bool {
	if x != nil {
		return x.IsAaSalaryActive
	}
	return false
}

func (x *AaSalaryProgramSummary) GetDaysSinceLastTransfer() int32 {
	if x != nil {
		return x.DaysSinceLastTransfer
	}
	return 0
}

func (x *AaSalaryProgramSummary) GetUpcomingActivationMonth() int32 {
	if x != nil {
		return x.UpcomingActivationMonth
	}
	return 0
}

func (x *AaSalaryProgramSummary) GetUpcomingActivationYear() int32 {
	if x != nil {
		return x.UpcomingActivationYear
	}
	return 0
}

func (x *AaSalaryProgramSummary) GetCashbackForLatestCommittedAmount() string {
	if x != nil {
		return x.CashbackForLatestCommittedAmount
	}
	return ""
}

func (x *AaSalaryProgramSummary) GetLatestCommittedAmount() int64 {
	if x != nil {
		return x.LatestCommittedAmount
	}
	return 0
}

type LoanOfferDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanOfferAmount    string `protobuf:"bytes,1,opt,name=loan_offer_amount,proto3" json:"loan_offer_amount,omitempty"`
	InterestRateYearly string `protobuf:"bytes,2,opt,name=interest_rate_yearly,proto3" json:"interest_rate_yearly,omitempty"`
	Lender             string `protobuf:"bytes,3,opt,name=lender,proto3" json:"lender,omitempty"`
	OfferExpiryDate    string `protobuf:"bytes,4,opt,name=offer_expiry_date,proto3" json:"offer_expiry_date,omitempty"`
}

func (x *LoanOfferDetails) Reset() {
	*x = LoanOfferDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanOfferDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanOfferDetails) ProtoMessage() {}

func (x *LoanOfferDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanOfferDetails.ProtoReflect.Descriptor instead.
func (*LoanOfferDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{10}
}

func (x *LoanOfferDetails) GetLoanOfferAmount() string {
	if x != nil {
		return x.LoanOfferAmount
	}
	return ""
}

func (x *LoanOfferDetails) GetInterestRateYearly() string {
	if x != nil {
		return x.InterestRateYearly
	}
	return ""
}

func (x *LoanOfferDetails) GetLender() string {
	if x != nil {
		return x.Lender
	}
	return ""
}

func (x *LoanOfferDetails) GetOfferExpiryDate() string {
	if x != nil {
		return x.OfferExpiryDate
	}
	return ""
}

type PortfolioChangeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PortfolioDailyChangeAmount     string                `protobuf:"bytes,1,opt,name=portfolio_daily_change_amount,proto3" json:"portfolio_daily_change_amount,omitempty"`
	PortfolioDailyChangePercentage *stocks.NumericMetric `protobuf:"bytes,2,opt,name=portfolio_daily_change_percentage,proto3" json:"portfolio_daily_change_percentage,omitempty"`
	// This will convey the date for which the portfolio daily change summary was calculated
	PortfolioSummaryDate           string                `protobuf:"bytes,3,opt,name=portfolio_summary_date,proto3" json:"portfolio_summary_date,omitempty"`
	DailyTopGainerName             string                `protobuf:"bytes,4,opt,name=daily_top_gainer_name,proto3" json:"daily_top_gainer_name,omitempty"`
	DailyTopGainerChangePercentage string                `protobuf:"bytes,5,opt,name=daily_top_gainer_change_percentage,proto3" json:"daily_top_gainer_change_percentage,omitempty"`
	DailyBenchmarkChangePercentage *stocks.NumericMetric `protobuf:"bytes,6,opt,name=daily_benchmark_change_percentage,proto3" json:"daily_benchmark_change_percentage,omitempty"`
}

func (x *PortfolioChangeDetails) Reset() {
	*x = PortfolioChangeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioChangeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioChangeDetails) ProtoMessage() {}

func (x *PortfolioChangeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_moengage_contentapi_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioChangeDetails.ProtoReflect.Descriptor instead.
func (*PortfolioChangeDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_moengage_contentapi_proto_rawDescGZIP(), []int{11}
}

func (x *PortfolioChangeDetails) GetPortfolioDailyChangeAmount() string {
	if x != nil {
		return x.PortfolioDailyChangeAmount
	}
	return ""
}

func (x *PortfolioChangeDetails) GetPortfolioDailyChangePercentage() *stocks.NumericMetric {
	if x != nil {
		return x.PortfolioDailyChangePercentage
	}
	return nil
}

func (x *PortfolioChangeDetails) GetPortfolioSummaryDate() string {
	if x != nil {
		return x.PortfolioSummaryDate
	}
	return ""
}

func (x *PortfolioChangeDetails) GetDailyTopGainerName() string {
	if x != nil {
		return x.DailyTopGainerName
	}
	return ""
}

func (x *PortfolioChangeDetails) GetDailyTopGainerChangePercentage() string {
	if x != nil {
		return x.DailyTopGainerChangePercentage
	}
	return ""
}

func (x *PortfolioChangeDetails) GetDailyBenchmarkChangePercentage() *stocks.NumericMetric {
	if x != nil {
		return x.DailyBenchmarkChangePercentage
	}
	return nil
}

var File_api_vendors_moengage_contentapi_proto protoreflect.FileDescriptor

var file_api_vendors_moengage_contentapi_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x6f,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2f,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2f, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x73, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x93, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x72, 0x65, 0x61, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x72, 0x65, 0x61, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0xbd, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x9e, 0x01, 0x0a, 0x22, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x74, 0x6f, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x4e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x72,
	0x65, 0x61, 0x54, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x22, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x74, 0x6f, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x1a, 0x7f, 0x0a, 0x22, 0x41, 0x72, 0x65, 0x61, 0x54, 0x6f, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x43, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4d, 0x61, 0x70, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa1, 0x02, 0x0a, 0x1b, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e,
	0x61, 0x6d, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x4d, 0x61, 0x70, 0x12, 0x92, 0x01, 0x0a, 0x1d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4d, 0x61, 0x70, 0x2e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x1d, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x1a, 0x6d, 0x0a, 0x1e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x35,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa3, 0x07, 0x0a, 0x0d, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0c, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x4c, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x48, 0x00, 0x52, 0x0f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12,
	0x49, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x53, 0x70, 0x65, 0x6e, 0x64,
	0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x70, 0x65, 0x6e,
	0x64, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x43, 0x0a, 0x0c, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x48,
	0x00, 0x52, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12,
	0x57, 0x0a, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x48, 0x00, 0x52, 0x13, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x54, 0x0a, 0x12, 0x64, 0x65, 0x62, 0x69,
	0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d,
	0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x48, 0x00, 0x52, 0x12, 0x64, 0x65, 0x62, 0x69,
	0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x61,
	0x0a, 0x14, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x73,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x48, 0x00, 0x52, 0x14, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x12, 0x46, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x73, 0x2e, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x68, 0x0a, 0x19, 0x61, 0x61, 0x5f,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x73,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e,
	0x41, 0x61, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x48, 0x00, 0x52, 0x19, 0x61, 0x61, 0x5f, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x12, 0x54, 0x0a, 0x12, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x12, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x66, 0x0a, 0x18, 0x70, 0x6f, 0x72,
	0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x50,
	0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x18, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c,
	0x69, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22,
	0x80, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x3a, 0x0a, 0x18, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x32,
	0x0a, 0x14, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x73,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x61,
	0x73, 0x68, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x22, 0xce, 0x01, 0x0a, 0x0b, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6d, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6d, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6d, 0x69, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6d, 0x69, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x64, 0x70, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x64, 0x70, 0x64,
	0x12, 0x2e, 0x0a, 0x12, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x64, 0x61,
	0x79, 0x73, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x64, 0x75, 0x65,
	0x12, 0x41, 0x0a, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x22, 0x5d, 0x0a, 0x0b, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x30, 0x0a, 0x13, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x8d, 0x05, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x69, 0x6c, 0x6c,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62,
	0x69, 0x6c, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x18, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x66, 0x6f, 0x75, 0x72, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x73, 0x5f, 0x6f,
	0x66, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x66, 0x6f, 0x75, 0x72, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x73, 0x5f, 0x6f,
	0x66, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75,
	0x6d, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x12, 0x36, 0x0a, 0x16, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f,
	0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x75, 0x74,
	0x69, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x36,
	0x0a, 0x16, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x61, 0x79, 0x73,
	0x5f, 0x6f, 0x76, 0x65, 0x72, 0x64, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x6f,
	0x76, 0x65, 0x72, 0x64, 0x75, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64,
	0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x19, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64,
	0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x64, 0x75, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x75, 0x6e, 0x70, 0x61, 0x69,
	0x64, 0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x64, 0x75, 0x65, 0x12, 0x3e, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x6d, 0x69, 0x6e, 0x69, 0x6d,
	0x75, 0x6d, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x70, 0x61,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1a, 0x69, 0x73, 0x5f, 0x6d, 0x69, 0x6e,
	0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x5f,
	0x70, 0x61, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x13, 0x69, 0x73, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x22, 0xb6, 0x03, 0x0a, 0x10, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x4a, 0x0a, 0x20, 0x7a, 0x65, 0x72, 0x6f, 0x5f,
	0x66, 0x6f, 0x72, 0x65, 0x78, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x75, 0x70, 0x5f, 0x73, 0x61, 0x76,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x20, 0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x5f, 0x6d, 0x61,
	0x72, 0x6b, 0x75, 0x70, 0x5f, 0x73, 0x61, 0x76, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x5f,
	0x6e, 0x6f, 0x77, 0x12, 0x60, 0x0a, 0x2b, 0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x66, 0x6f, 0x72, 0x65,
	0x78, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x75, 0x70, 0x5f, 0x73, 0x61, 0x76, 0x65, 0x64, 0x5f, 0x66,
	0x6f, 0x72, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x76, 0x65, 0x6e, 0x5f, 0x64, 0x61,
	0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x2b, 0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x66,
	0x6f, 0x72, 0x65, 0x78, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x75, 0x70, 0x5f, 0x73, 0x61, 0x76, 0x65,
	0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x76, 0x65, 0x6e,
	0x5f, 0x64, 0x61, 0x79, 0x73, 0x12, 0x42, 0x0a, 0x1c, 0x64, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x69,
	0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x64, 0x6f, 0x6d,
	0x65, 0x73, 0x74, 0x69, 0x63, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x21, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x21, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2c, 0x0a,
	0x11, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e,
	0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xdc, 0x04, 0x0a, 0x16,
	0x41, 0x61, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x32, 0x0a, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x13, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x79, 0x65, 0x61,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x12, 0x2c, 0x0a, 0x11,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x64, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x64, 0x75, 0x65, 0x12, 0x38, 0x0a, 0x17, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x64, 0x75, 0x65, 0x5f,
	0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x17, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x73,
	0x69, 0x6e, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x61, 0x61, 0x5f, 0x73, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x13, 0x69, 0x73, 0x5f, 0x61, 0x61, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x3a, 0x0a, 0x18, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x73,
	0x69, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x73,
	0x69, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x12, 0x3c, 0x0a, 0x19, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x12, 0x3a, 0x0a, 0x18, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x18, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x12, 0x52, 0x0a, 0x24,
	0x63, 0x61, 0x73, 0x68, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x24, 0x63, 0x61, 0x73, 0x68,
	0x62, 0x61, 0x63, 0x6b, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x38, 0x0a, 0x17, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x17, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xba, 0x01, 0x0a, 0x10, 0x4c,
	0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x2c, 0x0a, 0x11, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32, 0x0a,
	0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x79,
	0x65, 0x61, 0x72, 0x6c, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x6c,
	0x79, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0x88, 0x04, 0x0a, 0x16, 0x50, 0x6f, 0x72, 0x74,
	0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x44, 0x0a, 0x1d, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f,
	0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x70, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x74, 0x0a, 0x21, 0x70, 0x6f, 0x72, 0x74,
	0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x4e, 0x75,
	0x6d, 0x65, 0x72, 0x69, 0x63, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x21, 0x70, 0x6f, 0x72,
	0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x36,
	0x0a, 0x16, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16,
	0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f,
	0x74, 0x6f, 0x70, 0x5f, 0x67, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x70,
	0x5f, 0x67, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4e, 0x0a, 0x22,
	0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x70, 0x5f, 0x67, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x22, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f,
	0x74, 0x6f, 0x70, 0x5f, 0x67, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x74, 0x0a, 0x21,
	0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x5f,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x73, 0x2e, 0x4e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52,
	0x21, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b,
	0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67,
	0x65, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_moengage_contentapi_proto_rawDescOnce sync.Once
	file_api_vendors_moengage_contentapi_proto_rawDescData = file_api_vendors_moengage_contentapi_proto_rawDesc
)

func file_api_vendors_moengage_contentapi_proto_rawDescGZIP() []byte {
	file_api_vendors_moengage_contentapi_proto_rawDescOnce.Do(func() {
		file_api_vendors_moengage_contentapi_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_moengage_contentapi_proto_rawDescData)
	})
	return file_api_vendors_moengage_contentapi_proto_rawDescData
}

var file_api_vendors_moengage_contentapi_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_vendors_moengage_contentapi_proto_goTypes = []interface{}{
	(*GetUserAttributesRequest)(nil),    // 0: vendors.moengage.GetUserAttributesRequest
	(*GetUserAttributesResponse)(nil),   // 1: vendors.moengage.GetUserAttributesResponse
	(*FieldNameToUserAttributeMap)(nil), // 2: vendors.moengage.FieldNameToUserAttributeMap
	(*UserAttribute)(nil),               // 3: vendors.moengage.UserAttribute
	(*RewardsSummary)(nil),              // 4: vendors.moengage.RewardsSummary
	(*LoanSummary)(nil),                 // 5: vendors.moengage.LoanSummary
	(*BankDetails)(nil),                 // 6: vendors.moengage.BankDetails
	(*CreditCardSummary)(nil),           // 7: vendors.moengage.CreditCardSummary
	(*DebitCardSummary)(nil),            // 8: vendors.moengage.DebitCardSummary
	(*AaSalaryProgramSummary)(nil),      // 9: vendors.moengage.AaSalaryProgramSummary
	(*LoanOfferDetails)(nil),            // 10: vendors.moengage.LoanOfferDetails
	(*PortfolioChangeDetails)(nil),      // 11: vendors.moengage.PortfolioChangeDetails
	nil,                                 // 12: vendors.moengage.GetUserAttributesResponse.AreaToFieldNameUserAttributesEntry
	nil,                                 // 13: vendors.moengage.FieldNameToUserAttributeMap.FieldNameToUserAttributesEntry
	(*insights.SpendsSummary)(nil),      // 14: vendors.moengage.SpendsSummary
	(*stocks.MarketIndexSummary)(nil),   // 15: vendors.moengage.stocks.MarketIndexSummary
	(*stocks.Stock)(nil),                // 16: vendors.moengage.stocks.Stock
	(*stocks.NumericMetric)(nil),        // 17: vendors.moengage.stocks.NumericMetric
}
var file_api_vendors_moengage_contentapi_proto_depIdxs = []int32{
	12, // 0: vendors.moengage.GetUserAttributesResponse.area_to_field_name_user_attributes:type_name -> vendors.moengage.GetUserAttributesResponse.AreaToFieldNameUserAttributesEntry
	13, // 1: vendors.moengage.FieldNameToUserAttributeMap.field_name_to_user_attributes:type_name -> vendors.moengage.FieldNameToUserAttributeMap.FieldNameToUserAttributesEntry
	4,  // 2: vendors.moengage.UserAttribute.rewards_summary:type_name -> vendors.moengage.RewardsSummary
	14, // 3: vendors.moengage.UserAttribute.spends_summary:type_name -> vendors.moengage.SpendsSummary
	5,  // 4: vendors.moengage.UserAttribute.loan_summary:type_name -> vendors.moengage.LoanSummary
	7,  // 5: vendors.moengage.UserAttribute.credit_card_summary:type_name -> vendors.moengage.CreditCardSummary
	8,  // 6: vendors.moengage.UserAttribute.debit_card_summary:type_name -> vendors.moengage.DebitCardSummary
	15, // 7: vendors.moengage.UserAttribute.market_index_summary:type_name -> vendors.moengage.stocks.MarketIndexSummary
	16, // 8: vendors.moengage.UserAttribute.stock_details:type_name -> vendors.moengage.stocks.Stock
	9,  // 9: vendors.moengage.UserAttribute.aa_salary_program_summary:type_name -> vendors.moengage.AaSalaryProgramSummary
	10, // 10: vendors.moengage.UserAttribute.loan_offer_details:type_name -> vendors.moengage.LoanOfferDetails
	11, // 11: vendors.moengage.UserAttribute.portfolio_change_details:type_name -> vendors.moengage.PortfolioChangeDetails
	6,  // 12: vendors.moengage.LoanSummary.bank_details:type_name -> vendors.moengage.BankDetails
	17, // 13: vendors.moengage.PortfolioChangeDetails.portfolio_daily_change_percentage:type_name -> vendors.moengage.stocks.NumericMetric
	17, // 14: vendors.moengage.PortfolioChangeDetails.daily_benchmark_change_percentage:type_name -> vendors.moengage.stocks.NumericMetric
	2,  // 15: vendors.moengage.GetUserAttributesResponse.AreaToFieldNameUserAttributesEntry.value:type_name -> vendors.moengage.FieldNameToUserAttributeMap
	3,  // 16: vendors.moengage.FieldNameToUserAttributeMap.FieldNameToUserAttributesEntry.value:type_name -> vendors.moengage.UserAttribute
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_api_vendors_moengage_contentapi_proto_init() }
func file_api_vendors_moengage_contentapi_proto_init() {
	if File_api_vendors_moengage_contentapi_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_moengage_contentapi_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAttributesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moengage_contentapi_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAttributesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moengage_contentapi_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldNameToUserAttributeMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moengage_contentapi_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAttribute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moengage_contentapi_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardsSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moengage_contentapi_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moengage_contentapi_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moengage_contentapi_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moengage_contentapi_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebitCardSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moengage_contentapi_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaSalaryProgramSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moengage_contentapi_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanOfferDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_moengage_contentapi_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioChangeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_vendors_moengage_contentapi_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*UserAttribute_StringValue)(nil),
		(*UserAttribute_RewardsSummary)(nil),
		(*UserAttribute_SpendsSummary)(nil),
		(*UserAttribute_LoanSummary)(nil),
		(*UserAttribute_CreditCardSummary)(nil),
		(*UserAttribute_DebitCardSummary)(nil),
		(*UserAttribute_MarketIndexSummary)(nil),
		(*UserAttribute_StockDetails)(nil),
		(*UserAttribute_AaSalaryProgramSummary)(nil),
		(*UserAttribute_LoanOfferDetails)(nil),
		(*UserAttribute_PortfolioChangeDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_moengage_contentapi_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_moengage_contentapi_proto_goTypes,
		DependencyIndexes: file_api_vendors_moengage_contentapi_proto_depIdxs,
		MessageInfos:      file_api_vendors_moengage_contentapi_proto_msgTypes,
	}.Build()
	File_api_vendors_moengage_contentapi_proto = out.File
	file_api_vendors_moengage_contentapi_proto_rawDesc = nil
	file_api_vendors_moengage_contentapi_proto_goTypes = nil
	file_api_vendors_moengage_contentapi_proto_depIdxs = nil
}
