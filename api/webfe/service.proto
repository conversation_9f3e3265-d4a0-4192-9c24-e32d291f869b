syntax = "proto3";

package webfe;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/firefly.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/webfe/enums.proto";
import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/webfe";
option java_package = "com.github.epifi.gamma.api.webfe";

service Webfe {

  rpc GeneratePhoneOtp (GeneratePhoneOtpRequest) returns (GeneratePhoneOtpResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }


  rpc VerifyPhoneOtp (VerifyPhoneOtpRequest) returns (VerifyPhoneOtpResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc GenerateEmailOtp (GenerateEmailOtpRequest) returns (GenerateEmailOtpResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc VerifyEmailOtp (VerifyEmailOtpRequest) returns (VerifyEmailOtpResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc SendAppLinkToUser (SendAppLinkToUserRequest) returns (SendAppLinkToUserResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // rpc to verify the otp for a given session. Upon successful verification, the desired flow will start based on the
  // feature
  rpc ValidateLogin (ValidateLoginRequest) returns (ValidateLoginResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to fetch the status of any waitlist specific flows which can be long-running and hence would
  // require polling from client side
  rpc GetRequestStatus (GetRequestStatusRequest) returns (GetRequestStatusResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // CheckCreditCardEligibility rpc to check if user is eligible for credit card
  rpc CheckCreditCardEligibility (CheckCreditCardEligibilityRequest) returns (CheckCreditCardEligibilityResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

// Request for checking the eligibility of a credit card application.
message CheckCreditCardEligibilityRequest {
  // Request header containing metadata for the frontend.
  frontend.header.RequestHeader req = 1;
  // Name of the user.
  api.typesv2.common.Name name = 2;
  // PAN (Permanent Account Number) of the user. Used to enrich user entry.
  string pan = 3;
  // Date of birth of the user. Used to enrich user entry.
  google.type.Date dob = 4;
  // Email ID of the user.
  string email_id = 5;
  // Consents that the user has given as part of the eligibility check.
  repeated ConsentType consents = 6;
  // Token for authentication and authorization.
  string token = 7;
  // Type of card program for which eligibility is being checked.
  api.typesv2.CardProgramType card_program_type = 8;
  // Vendor providing the card program.
  api.typesv2.CardProgramVendor card_program_vendor = 9;
}


message CheckCreditCardEligibilityResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // identifier for fetching the status of the flow that has been initiated on the basis of the
  // flow inputted in the request
  string client_req_id = 2;
}

message ValidateLoginRequest {
  frontend.header.RequestHeader req = 1;
  // Unique identifier of ValidateLoginRequest
  string token = 2 [(validate.rules).string.uuid = true];
  // OTP that is shared to the phone number via SMS.
  string otp = 3 [(validate.rules).string = {len: 6, pattern: "^[0-9]+$"}];
  // enum to determine which flow has to be initiated.
  // deprecated in favour of FlowName
  webfe.WebFlow web_flow = 5 [deprecated = true];
  webfe.FlowName flow_name = 6;
  // email id of the user
  string email_id = 7;
  // PAN number of the user. This will be used to enrich the user entry
  string pan = 8;
  // date of birth of the user. This will be used to enrich the user entry
  google.type.Date dob = 9;
  // phone number of the user for which the otp verification will be done
  api.typesv2.common.PhoneNumber phone_number = 10;
  // consents that the user has given as a part of entering OTP
  repeated ConsentType consents = 11;
  // name of the user
  api.typesv2.common.Name name = 12;
}

message ValidateLoginResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // identifier for fetching the status of the flow that has been initiated on the basis of the
  // flow inputted in the request
  string client_req_id = 2;
  // Represents a User's access token.
  // This token will serve as authentication parameter.
  string access_token = 3;
  string actor_id = 4;
}

message GetRequestStatusRequest {
  frontend.header.RequestHeader req = 1;
  // this will be required as an identifier for polling
  string client_req_id = 2;
  // flow for which the status has to be checked
  // deprecated in favour of FlowName
  webfe.WebFlow web_flow = 3 [deprecated = true];
  webfe.FlowName flow_name = 5;
  // actor id of the user for whom the flow has been started
  string actor_id = 4;
}

message GetRequestStatusResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // status of the flow that was re-triggered
  enum RequestStatus {
    STATUS_UNSPECIFIED = 0;
    STATUS_IN_PROGRESS = 1;
    STATUS_SUCCESSFUL = 2;
    STATUS_FAILED = 3;
  }
  RequestStatus request_status = 2;
  DisplayInfo display_info = 3;
}

// a generic structure to display a screen
message DisplayInfo {
  // icon to be displayed above the screen title. This can be used to
  // represent the status of the reuqest
  string header_icon = 1;
  // this will be displayed as a screen heading
  string screen_title = 2;
  // this will be a screen message that will be displayed below the
  // screen title
  string screen_message = 3;
  // image occurring on the background
  string screen_image = 4;
  // text to be displayed on the cta at the bottom of the screen
  string cta_text = 5;
  // additional text to be displayed below the screen message
  string additional_text = 6;
  // text to be displayed at the bottom of the screen. This will be
  // an informatory text
  string bottom_text = 7;
}


message GeneratePhoneOtpRequest {
  frontend.header.RequestHeader req = 1;

  // Phone number that is to be verified
  api.typesv2.common.PhoneNumber phone_number = 2;

  // Flow for which OTP is being generated.
  // deprecated in favour of flow_name
  GenerateOTPFlow generate_otp_flow = 3 [deprecated = true];

  webfe.FlowName flow_name = 5;

  // Unique identifier of GenerateOtp request
  // If token(+phone_number) is not sent, a new OTP is generated and sent to the user via SMS
  // If token(+phone_number) is sent, request is treated as ResendOtp and existing OTP is sent to the user phone via SMS
  string token = 4;

}

message GeneratePhoneOtpResponse {
  enum Status {
    // Success
    OK = 0;
    // Input token is not available on the system
    // Generate new token
    NOT_FOUND = 5;
    // Internal error
    INTERNAL = 13;
    // Input token has been used already and is not active.
    // Cannot reuse it
    TOKEN_INACTIVE = 100;
    // Input token is expired and cannot use it
    // Generate new token
    TOKEN_EXPIRY = 101;
    // Too many resend attempts
    // Generate new token
    RESEND_LIMIT = 102;
    // Resend request too soon
    // Wait for some time and retry
    RESEND_REQ_TOO_SOON = 103;
  }

  frontend.header.ResponseHeader resp_header = 1;

  // Unique identifier of GeneratePhoneOtp request
  string token = 2;

  // A timer(in seconds) for the client post which it should raise a new request for Otp
  // Any attempt prior to this timer will not be honored & will result in error
  uint32 retry_timer_seconds = 3;
}

// Request message for verifying a phone number with OTP.
message VerifyPhoneOtpRequest {
  // Header information for the request.
  frontend.header.RequestHeader req = 1;

  // Phone number that is being verified.
  api.typesv2.common.PhoneNumber phone_number = 2;

  // Unique identifier of the GeneratePhoneOtp request.
  string token = 3;

  // OTP from the user.
  // todo(teja) otp validation rule
  string otp = 4;

  // Flag to indicate whether to send a link to WhatsApp.
  api.typesv2.common.BooleanEnum has_whatsapp_consent = 5;

  // List of consents from the user.
  repeated ConsentType consent_types = 6;

  // Optional URL through which the user entered the web flow.
  string web_url = 7;

  // Enum to determine which web flow has to be initiated.
  // deprecated in favour of flow_name
  webfe.WebFlow web_flow = 8 [deprecated = true];

  webfe.FlowName flow_name = 10;

  // Additional details for web flow.
  AdditionalDetails additional_details = 9;
}

// Additional details container.
message AdditionalDetails {
  // Oneof for various types of web flow data.
  oneof WebFlowData {
    CreditCardEligibilityCheckData credit_card_eligibility_check_data = 1;
  }
}

// Web eligibility check data for the cc eligibility web flow.
message CreditCardEligibilityCheckData {
  // Type of card program for which eligibility is being checked.
  api.typesv2.CardProgramType card_program_type = 1;

  // Vendor providing the card program.
  api.typesv2.CardProgramVendor card_program_vendor = 2;
}

message VerifyPhoneOtpResponse {
  enum Status {
    // Success
    OK = 0;
    // Client specified an invalid argument
    INVALID_ARGUMENT = 3;
    // Input token is not available on the system
    // Generate new token
    NOT_FOUND = 5;
    // Internal error
    INTERNAL = 13;
    // Input token has been used already and is not active.
    // Cannot reuse it
    TOKEN_INACTIVE = 100;
    // Input token is expired and cannot use it
    // Generate new token
    TOKEN_EXPIRY = 101;
    // Otp is not correct
    OTP_INCORRECT = 102;
    // Otp is not valid
    // Account will be locked if additional incorrect attempts are made
    OTP_INCORRECT_LAST_ATTEMPT = 103;
    // Account is locked temporarily
    // There have been too many incorrect attempts
    // Wait for some time and retry with a new token
    OTP_INCORRECT_LOCKED = 104;
    // Too many incorrect attempts on the token
    // Generate new token
    OTP_VERIFY_LIMIT_EXCEEDED = 105;
    // The user doesn't have access to the app due to access being revoked
    OTP_USER_ACCESS_SOFT_BLOCK = 108;
    // The user doesn't have access to the app due to user being blacklisted by FI
    OTP_USER_ACCESS_BLACKLISTED = 111;
    // Error while fetcing Otp entry from DB
    ERR_FETCHING_OTP_ENTRY = 112;
    // device detail in request is different from the details present in otp entry
    DIFFERENT_DEVICE = 113;
    // otp token in request does not match with the one present in DB entry
    INVALID_OTP_TOKEN = 114;
  }

  frontend.header.ResponseHeader resp_header = 1;

  //Access token which allows user for email verification
  // todo(teja) otp validation rule
  string access_token = 2;

  frontend.deeplink.Deeplink next_screen = 3;

  // actor id of the user for whom the flow has been started
  string actor_id = 4;
}


message GenerateEmailOtpRequest {
  frontend.header.RequestHeader req = 1;

  // email that is to be verified
  string email = 2;

  // Flow for which OTP is being generated.
  GenerateOTPFlow generate_otp_flow = 3;

  // Unique identifier of GenerateEmailOtp request
  // If token(+email) is not sent, a new OTP is generated and sent to the user via SMS
  // If token(+email) is sent, request is treated as ResendOtp and existing OTP is sent to the corresponding email
  string token = 4;

  // client_req_id is the unique identifier of a process entry for a client,
  // client would get this in the screen options of SendWorkEmailOtp screen
  string client_req_id = 5;
}

message GenerateEmailOtpResponse {
  enum Status {
    // Success
    OK = 0;
    // Input token is not available on the system
    // Generate new token
    NOT_FOUND = 5;
    // Internal error
    INTERNAL = 13;
    // Input token has been used already and is not active.
    // Cannot reuse it
    TOKEN_INACTIVE = 100;
    // Input token is expired and cannot use it
    // Generate new token
    TOKEN_EXPIRY = 101;
    // Too many resend attempts
    // Generate new token
    RESEND_LIMIT = 102;
    // Resend request too soon
    // Wait for some time and retry
    RESEND_REQ_TOO_SOON = 103;
  }

  frontend.header.ResponseHeader resp_header = 1;

  // Unique identifier of GenerateEmailOtp request
  string token = 2;

  // A timer(in seconds) for the client post which it should raise a new request for Otp
  // Any attempt prior to this timer will not be honored & will result in error
  uint32 retry_timer_seconds = 3;
}

message VerifyEmailOtpRequest {
  frontend.header.RequestHeader req = 1;

  // email that is to be verified
  string email = 2;

  //OTP from user
  string otp = 3;

  //unique identifier of GenerateEmailRequest
  string token = 4;

  // client_req_id is the unique identifier of a process entry for a client,
  // client would get this in the screen options of SendWorkEmailOtp screen
  string client_req_id = 5;
}

message VerifyEmailOtpResponse {
  enum Status {
    // Success
    OK = 0;
    // Client specified an invalid argument
    INVALID_ARGUMENT = 3;
    // Input token is not available on the system
    // Generate new token
    NOT_FOUND = 5;
    // Internal error
    INTERNAL = 13;
    // Input token has been used already and is not active.
    // Cannot reuse it
    TOKEN_INACTIVE = 100;
    // Input token is expired and cannot use it
    // Generate new token
    TOKEN_EXPIRY = 101;
    // Otp is not correct
    OTP_INCORRECT = 102;
    // Otp is not valid
    // Account will be locked if additional incorrect attempts are made
    OTP_INCORRECT_LAST_ATTEMPT = 103;
    // Account is locked temporarily
    // There have been too many incorrect attempts
    // Wait for some time and retry with a new token
    OTP_INCORRECT_LOCKED = 104;
    // Too many incorrect attempts on the token
    // Generate new token
    OTP_VERIFY_LIMIT_EXCEEDED = 105;
    // The user doesn't have access to the app due to access being revoked
    OTP_USER_ACCESS_SOFT_BLOCK = 108;
    // The user doesn't have access to the app due to user being blacklisted by FI
    OTP_USER_ACCESS_BLACKLISTED = 111;
    // Error while fetcing Otp entry from DB
    ERR_FETCHING_OTP_ENTRY = 112;
    // device detail in request is different from the details present in otp entry
    DIFFERENT_DEVICE = 113;
    // otp token in request does not match with the one present in DB entry
    INVALID_OTP_TOKEN = 114;
  }

  frontend.header.ResponseHeader resp_header = 1;
}

message SendAppLinkToUserRequest {
  frontend.header.RequestHeader req = 1;
}

message SendAppLinkToUserResponse {
  frontend.header.ResponseHeader resp_header = 1;
}
