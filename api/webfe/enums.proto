syntax = "proto3";

package webfe;

option go_package = "github.com/epifi/gamma/api/webfe";
option java_package = "com.github.epifi.gamma.api.webfe";

enum GenerateOTPFlow {
  GENERATE_OTP_FLOW_UNSPECIFIED = 0;
  GENERATE_OTP_FLOW_B2B_ONBOARDING = 1;
  GENERATE_OTP_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK = 2;
}

enum ConsentType {
  CONSENT_TYPE_UNSPECIFIED = 0;
  CONSENT_TYPE_FI_TNC = 1;
  CONSENT_TYPE_FED_TNC = 2;
  CONSENT_TYPE_FI_PRIVACY_POLICY = 3;
  CONSENT_TYPE_FI_WEALTH_TNC = 4;
  // This consent will tell users that their credit report data will be pulled from the vendor (eg. Experian)
  CONSENT_TYPE_CREDIT_REPORT_DATA_PULL = 5;
}

enum WebFlow {
  WEB_FLOW_UNSPECIFIED = 0;
  WEB_FLOW_OFF_APP_CC_ELIGIBILITY_CHECK = 1;
}

enum FlowName {
  FLOW_NAME_UNSPECIFIED = 0;
  FLOW_NAME_LOANS_ELIGIBILITY = 1;
  FLOW_NAME_CREDIT_SCORE_ANALYSER = 2;
  FLOW_NAME_CC_ELIGIBILITY_CHECK = 3;
  FLOW_NAME_NET_WORTH_MCP_AUTH = 4;
}
